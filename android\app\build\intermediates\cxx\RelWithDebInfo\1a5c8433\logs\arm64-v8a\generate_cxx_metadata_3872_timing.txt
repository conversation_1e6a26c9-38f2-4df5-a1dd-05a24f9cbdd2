# C/C++ build system timings
generate_cxx_metadata
  [gap of 90ms]
  create-invalidation-state 175ms
  generate-prefab-packages
    [gap of 114ms]
    exec-prefab 1553ms
    [gap of 210ms]
  generate-prefab-packages completed in 1877ms
  execute-generate-process
    [gap of 15ms]
    exec-configure 2330ms
    [gap of 330ms]
  execute-generate-process completed in 2675ms
  [gap of 156ms]
  write-metadata-json-to-file 17ms
generate_cxx_metadata completed in 5003ms

