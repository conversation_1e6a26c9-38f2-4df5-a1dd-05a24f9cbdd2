{"name": "Delivery partner", "slug": "my-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.mohandhass2.myapp", "googleServicesFile": "./android/app/google-services.json"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", ["expo-document-picker", {"iCloudContainerEnvironment": "Production"}]], "experiments": {"typedRoutes": true}, "extra": {"BASE_URL": "http://13.235.24.176:7600/mobile-api/delivery_partner/", "BASE_URL2": "http://13.235.24.176:7600/mobile-api/", "router": {"origin": false}, "eas": {"projectId": "7e03dded-f346-4fa7-9987-65e131b1010f"}}, "sdkVersion": "52.0.0", "platforms": ["ios", "android", "web"]}