import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  FlatList,
} from "react-native";
import React, { useState } from "react";
import { router } from "expo-router";
import BackIcon from "@/assets/icons/BackArrow.svg";
import BellIcon from "../../../../../assets/ProfileAsser/SectionIcon/UserprofileAssert/bell-dynamic-gradient.svg";
import { SafeAreaView } from "react-native-safe-area-context";

const index = () => {
  const [data, setdata] = useState([]);
  return (
    <SafeAreaView className="flex-1">
      <View className="items-center justify-center relative mt-6">
        <TouchableOpacity
          className="absolute left-[3%]"
          onPress={() => {
            router.back();
          }}
        >
          <BackIcon />
        </TouchableOpacity>
        <View>
          <Text className="font-[600] text-[20px] leading-[30px]">
            Notifications
          </Text>
        </View>
      </View>
      <FlatList
        data={data}
        contentContainerStyle={{
          flex: 1,
        }}
        ListEmptyComponent={() => <EmptyComponent />}
        renderItem={(item) => {
          return (
            <>
              <Text>MIOMIDm</Text>
            </>
          );
        }}
      />
    </SafeAreaView>
  );
};

const EmptyComponent = () => {
  return (
    <View className="flex-1 justify-center items-center">
      <View>
        <BellIcon />
      </View>
      <View className="mt-2">
        <Text className="font-[500] text-[16px] leading-[24px] text-[#627164]">You have not received any notifications yet</Text>
      </View>
    </View>
  );
};

export default index;
