[{"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\D_\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\RNDateTimePickerCGen-generated.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\RNDateTimePickerCGen-generated.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\RNDateTimePickerCGen-generated.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\13e5db451150cc1628879224ca4ec527\\ComponentDescriptors.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\ComponentDescriptors.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\ComponentDescriptors.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\EventEmitters.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\EventEmitters.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\EventEmitters.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\Props.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\Props.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\Props.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\RNDateTimePickerCGenJSI-generated.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\RNDateTimePickerCGenJSI-generated.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\RNDateTimePickerCGenJSI-generated.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\ShadowNodes.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\ShadowNodes.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\ShadowNodes.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\States.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\States.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\States.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_RNCSlider_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\003b49a80570ffb3c0571bc833857a58\\components\\RNCSlider\\RNCSliderMeasurementsManager.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\common\\cpp\\react\\renderer\\components\\RNCSlider\\RNCSliderMeasurementsManager.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\common\\cpp\\react\\renderer\\components\\RNCSlider\\RNCSliderMeasurementsManager.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_RNCSlider_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\9075f005231ce092d6ccc5e2d3456a36\\renderer\\components\\RNCSlider\\RNCSliderShadowNode.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\common\\cpp\\react\\renderer\\components\\RNCSlider\\RNCSliderShadowNode.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\common\\cpp\\react\\renderer\\components\\RNCSlider\\RNCSliderShadowNode.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_RNCSlider_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\5122c60e63b948760d540d4c42578b2c\\generated\\source\\codegen\\jni\\RNCSlider-generated.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\RNCSlider-generated.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\RNCSlider-generated.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_RNCSlider_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\08d7e72103038234790cf2d9aa38e8e7\\renderer\\components\\RNCSlider\\ComponentDescriptors.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\ComponentDescriptors.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\ComponentDescriptors.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_RNCSlider_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\c838027c612a797883778a7030e7c603\\react\\renderer\\components\\RNCSlider\\EventEmitters.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\EventEmitters.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\EventEmitters.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_RNCSlider_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\77fd0a957a73c497cf9ef944d6bb91cd\\jni\\react\\renderer\\components\\RNCSlider\\Props.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\Props.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\Props.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_RNCSlider_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\dfc4e1350bd27d7d0e423e38a8fd1f3a\\components\\RNCSlider\\RNCSliderJSI-generated.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\RNCSliderJSI-generated.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\RNCSliderJSI-generated.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_RNCSlider_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\c838027c612a797883778a7030e7c603\\react\\renderer\\components\\RNCSlider\\ShadowNodes.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\ShadowNodes.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\ShadowNodes.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_RNCSlider_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\77fd0a957a73c497cf9ef944d6bb91cd\\jni\\react\\renderer\\components\\RNCSlider\\States.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\States.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\States.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnskia_autolinked_build\\CMakeFiles\\react_codegen_rnskia.dir\\react\\renderer\\components\\rnskia\\ComponentDescriptors.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\ComponentDescriptors.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\ComponentDescriptors.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnskia_autolinked_build\\CMakeFiles\\react_codegen_rnskia.dir\\react\\renderer\\components\\rnskia\\EventEmitters.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\EventEmitters.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\EventEmitters.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnskia_autolinked_build\\CMakeFiles\\react_codegen_rnskia.dir\\react\\renderer\\components\\rnskia\\Props.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\Props.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\Props.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnskia_autolinked_build\\CMakeFiles\\react_codegen_rnskia.dir\\react\\renderer\\components\\rnskia\\ShadowNodes.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\ShadowNodes.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\ShadowNodes.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnskia_autolinked_build\\CMakeFiles\\react_codegen_rnskia.dir\\react\\renderer\\components\\rnskia\\States.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\States.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\States.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnskia_autolinked_build\\CMakeFiles\\react_codegen_rnskia.dir\\react\\renderer\\components\\rnskia\\rnskiaJSI-generated.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\rnskiaJSI-generated.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\rnskiaJSI-generated.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnskia_autolinked_build\\CMakeFiles\\react_codegen_rnskia.dir\\rnskia-generated.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\rnskia-generated.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\rnskia-generated.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\ComponentDescriptors.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\EventEmitters.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\Props.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\ShadowNodes.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\States.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\rngesturehandler_codegen-generated.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\782825489b2de6dd24b2ca840b493cca\\RNCSafeAreaViewShadowNode.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\f09d0ff9a11a83e8212c8b543480fb1f\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\0dfb3abc2da69bf3c1b6c958ae2ef096\\safeareacontext\\ComponentDescriptors.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\0dfb3abc2da69bf3c1b6c958ae2ef096\\safeareacontext\\EventEmitters.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\90a06d5e9d2845d55ca8b5b4feec886a\\components\\safeareacontext\\Props.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\90a06d5e9d2845d55ca8b5b4feec886a\\components\\safeareacontext\\ShadowNodes.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\90a06d5e9d2845d55ca8b5b4feec886a\\components\\safeareacontext\\States.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\8443cc8dc8ba497e2ecc8392eacca0ba\\safeareacontextJSI-generated.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\c404e8c5b3ec0b9cac506195c32c0556\\codegen\\jni\\safeareacontext-generated.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\566b5fde03bdfc9ae37b7c403d9c407e\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\9a5103abd33c845dd2828a71ebbe6d85\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\503213f527c2ef03fceffc79113385ff\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\503213f527c2ef03fceffc79113385ff\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\503213f527c2ef03fceffc79113385ff\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\503213f527c2ef03fceffc79113385ff\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\d7621f148084165e4d14a0288a9d9dab\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\9a67e56fa1c110143ff4774556ca52c8\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\2f99b1dcdb07b3fe88f257f8e5cd06ce\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\24af7573a39b142b5a15253f8ee44411\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\2f99b1dcdb07b3fe88f257f8e5cd06ce\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\24af7573a39b142b5a15253f8ee44411\\jni\\react\\renderer\\components\\rnscreens\\States.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\13a34cfcd3b2d637eb9e74cb747ec491\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\18b74893c726cdc94dbebd11f4529888\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\cf30f482125d83e58b71403bfee51979\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\rnsvg.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\android\\src\\main\\jni\\rnsvg.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\android\\src\\main\\jni\\rnsvg.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\72ce3b6f28029d404cd484e756fc3511\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\3ca25319e7abcd7beabd5a427bc75b84\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\9cfcaa9c901b86b625a918218e5ad042\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\3ca25319e7abcd7beabd5a427bc75b84\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\9cfcaa9c901b86b625a918218e5ad042\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp"}, {"directory": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\72ce3b6f28029d404cd484e756fc3511\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp.o -c D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp", "file": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp"}]