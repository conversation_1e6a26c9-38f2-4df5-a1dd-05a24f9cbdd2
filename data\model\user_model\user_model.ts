export class UserModel implements UserEntity {
  id: number;
  name: string;
  email: string;
  phone: string;
  image: string;
  dob: string;
  gender: string;
  is_phone_verified: number;
  is_email_verified: number;
  date_of_joinning: string;
  is_online: number;

  constructor({
    id,
    name,
    email,
    phone,
    image,
    dob,
    gender,
    is_phone_verified,
    is_email_verified,
    date_of_joinning,
    is_online,
  }: {
    id: number;
    name: string;
    email: string;
    phone: string;
    image: string;
    dob: string;
    gender: string;
    is_phone_verified: number;
    is_email_verified: number;
    date_of_joinning: string;
    is_online: number;
  }) {
    this.id = id;
    this.name = name;
    this.email = email;
    this.phone = phone;
    this.image = image;
    this.dob = dob;
    this.gender = gender;
    this.is_phone_verified = is_phone_verified;
    this.is_email_verified = is_email_verified;
    this.date_of_joinning = date_of_joinning;
    this.is_online = is_online;
  }
}
