import React, { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import BackArrow from "@/assets/icons/BackArrow.svg";
import BagIcon from "../../../../../assets/Hometab/bagICon.svg";
import useGetApiDatawithParam from "../../../../../hooks/useGetApiDatawithParam";
import DateTimePicker from "@react-native-community/datetimepicker";

const index = () => {
  const [date, setdate] = useState("");
  const [display_date, setdisplay_date] = useState("");
  const { data, isLoading, triggerreFresh } = useGetApiDatawithParam({
    endpoint: "my_earning",
    param: { select_date: date },
  });
  const getDates = (daysToAdd) => {
    const today = new Date();
    const datesArray = [];

    for (let i = 0; i <= daysToAdd; i++) {
      const currentDate = new Date();
      currentDate.setDate(today.getDate() + i); // Add 'i' days to today's date
      datesArray.push(currentDate.toDateString()); // Add formatted date to array
    }
    return datesArray;
  };

  const dates = getDates(2);

  const [showpicker, setshowpicker] = useState(false);

  const toggledatepicker = () => {
    setshowpicker(!showpicker);
  };

  const change = ({ type }, selectedDate) => {
    if (type == "set") {
      setshowpicker(false);
      const currentdate = selectedDate.toDateString();
      setdate(currentdate);
      let data = currentdate.split(" ");
      setdisplay_date(`${data[0]} ${data[2]}th ${data[1]}`);
      triggerreFresh();
    } else {
      setshowpicker(false);
    }
  };
  return (
    <SafeAreaView>
      <View className="px-4 mt-3">
        <View className="flex-row relative items-center justify-start mt-4">
          <TouchableOpacity
            onPress={() => {
              router.back();
            }}
            className="absolute"
          >
            <View>
              <BackArrow />
            </View>
          </TouchableOpacity>
          <View className="flex-1 items-center justify-center">
            <Text className="font-[400] text-[19px] leading-[39px]">
              Your Earnings
            </Text>
          </View>
        </View>
      </View>
      <View className="mt-6 items-center justify-center flex-row space-x-2">
        {isLoading ? (
          <></>
        ) : (
          <>
            {dates.map((LoginData) => {
              let data = LoginData.split(" ");
              return (
                <TouchableOpacity
                  onPress={() => {
                    setdate(LoginData);
                    setdisplay_date(`${data[0]} ${data[2]}th ${data[1]}`);
                    triggerreFresh();
                  }}
                  className="ml-2 items-center justify-center w-[83px] h-[62px] border-[1px] border-[#00660A] rounded-[8px]"
                >
                  <Text className="">{data[0]}</Text>
                  <Text className="">
                    {data[2]}th {data[1]}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </>
        )}
        <TouchableOpacity
          onPress={toggledatepicker}
          className="items-center justify-center w-[43px] h-[62px] border-[1px] border-[#DEDEDE] rounded-[8px]"
        >
          <BagIcon />
        </TouchableOpacity>
        {showpicker && (
          <DateTimePicker
            value={date === "" ? new Date() : new Date(date)}
            mode="date"
            onChange={change}
          />
        )}
      </View>
      {data?.data?.map((date, index) => {
        let UniqSet = new Set();
        UniqSet.add(date?.date);
        if (date?.data?.length === 0) {
          return null;
        }
        return (
          <View className="px-4 mt-4" key={index}>
            <View className="my-4">
              <Text className="">{UniqSet}</Text>
            </View>
            {date?.data?.map((earning) => {
              return (
                <EarningCardComponent earning={earning} key={earning.id} />
              );
            })}
          </View>
        );
      })}
    </SafeAreaView>
  );
};

export const EarningCardComponent = ({ earning }) => {
  return (
    <>
      <View className="flex-row p-4 justify-between border-[1px] border-[#E9EAE9] rounded-[8px]">
        <View className="">
          <View className="">
            <Text className="flex flex-wrap w-[99%]">{earning?.message}</Text>
          </View>
          {/* <View className="mt-2">
            <Text className="">08:30 AM</Text>
          </View> */}
        </View>
        <View className="">
          <Text className="">₹ {earning?.amount}</Text>
        </View>
      </View>
    </>
  );
};

export default index;
