import { View, Text, TouchableOpacity } from "react-native";
import React from "react";
import BackIcon from "@/assets/icons/BackArrow.svg";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import TextinputWithEdit from "../../../../../component/TextinputWithEdit";
import { useForm } from "react-hook-form";
import TextinputFile from "../../../../../component/TextinputFile";
import ButtonComponent from "../../../../../component/ButtonComponent";
import useSetApiData from "../../../../../hooks/useSetApiData";
import useGetApiData from "../../../../../hooks/useGetApiData";

const index = () => {
  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm();
  const { SetFunction } = useSetApiData({ endpoint: "auth/add_bank_details" });
  const { data, isLoading, triggerreFresh } = useGetApiData({
    endpoint: "get_bank_details",
  });
  console.log(data);
  const HandleSubmit = (val) => {
    SetFunction({
      account_no: val.accountnumber,
      bank_name: val.bankname,
      ifsc_code: val.IFSCcode,
    });
  };
  return (
    <SafeAreaView>
      <View className="items-center justify-center relative mt-6">
        <TouchableOpacity
          className="absolute left-[3%]"
          onPress={() => {
            router.back();
          }}
        >
          <BackIcon />
        </TouchableOpacity>
        <View>
          <Text className="font-[600] text-[20px] leading-[30px]">
            Account Details
          </Text>
        </View>
      </View>
      {isLoading ? (
        <></>
      ) : (
        <>
          <View className="px-4 mt-3">
            <Text className="font-[400] text-[16px] text-[#4D4D4D]">
              Account Number
            </Text>
            <TextinputFile
              control={control}
              name={"accountnumber"}
              placeholder={data?.data?.account_no}
            />
          </View>
          <View className="px-4 mt-3">
            <Text className="font-[400] text-[16px] text-[#4D4D4D]">
              Bank Name
            </Text>
            <TextinputFile
              control={control}
              name={"bankname"}
              placeholder={data?.data?.bank_name}
            />
          </View>
          <View className="px-4 mt-3">
            <Text className="font-[400] text-[16px] text-[#4D4D4D]">IFSC</Text>
            <TextinputFile
              control={control}
              name={"IFSCcode"}
              placeholder={data?.data?.ifsc_code}
            />
          </View>
        </>
      )}

      <View className="mt-6 px-4">
        <ButtonComponent
          value={isValid}
          pressfun={() => {
            handleSubmit(HandleSubmit)();
          }}
          text={"Submit"}
        />
      </View>
    </SafeAreaView>
  );
};

export default index;
