import { View, Text } from "react-native";
import React, { useEffect, useState } from "react";
import { Audio } from "expo-av";
import Ring from "../../assets/Sound/Apple.mp3";

const PlaySound = () => {
  const [sound, setSound] = useState(null);
  const playSound = async () => {
    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: true,
        staysActiveInBackground: true,
      });
      const sound = new Audio.Sound();
      await sound.loadAsync(Ring);

      setSound(sound);

      // Play the sound
      await sound.playAsync();

      // Stop after 10 seconds
      setTimeout(() => {
        sound.stopAsync();
      }, 10000);
    } catch (error) {
      console.error("Error playing sound:", error);
    }
  };

  useEffect(() => {
    return () => {
      // Unload the sound when the component unmounts
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, [sound]);
  return { playSound };
};

export default PlaySound;
