import AsyncStorage from "@react-native-async-storage/async-storage";
import Constants from "expo-constants";
import axios from "axios";
import { useEffect, useState } from "react";

const useUpdateWithFormData = ({ endpoint }) => {
  const [data, setdata] = useState(null);
  const [isLoading, setLoading] = useState(false);
  const UpdateBankDetails = async (datas) => {
    try {
      await setLoading(true);
      const token = await AsyncStorage.getItem("login");
      const responce = await axios
        .post(
          `${Constants.expoConfig?.extra?.BASE_URL}auth/${endpoint}`,
          datas,
          {
            headers: {
              "X-Powered-By": "Express",
              "Access-Control-Allow-Origin": "*",
              "content-type": "multipart/form-data",
              Authorization: token,
            },
          }
        )
        .then((val) => {
          return val.data;
        })
        .then((datas) => {
          setdata(datas);
          return datas;
        })
        .catch((error) => {
          console.error(error.response ? error.response.data : error.message);
        });
      await setLoading(false);
      return responce;
    } catch (error) {
      console.log(error);
    }
  };

  return { isLoading, data, UpdateBankDetails };
};

export default useUpdateWithFormData;
