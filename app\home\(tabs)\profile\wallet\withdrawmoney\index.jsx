import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Dimensions,
  Alert,
} from "react-native";
import React, { useState } from "react";
import { router, useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import BackArrow from "@/assets/icons/BackArrow.svg";
import Rsicon from "@/assets/Hometab/rs.svg";
import useSetApiData from "../../../../../../hooks/useSetApiData";

const withdrawmoney = () => {
  const [value, setvalue] = useState("0");
  const { amount } = useLocalSearchParams();
  const { SetFunction } = useSetApiData({
    endpoint: "deposit_money_to_wallet",
  });
  return (
    <SafeAreaView className="flex-1">
      <View className="px-4 bg-[#FFFFFF] flex-1 justify-between">
        <View>
          <View className="justify-between mt-3">
            <View className="flex-row relative items-center justify-start mt-4">
              <TouchableOpacity
                onPress={() => {
                  router.back();
                }}
                className="absolute"
              >
                <View>
                  <BackArrow />
                </View>
              </TouchableOpacity>
              <View className="flex-1 items-center justify-center">
                <Text className="font-[400] text-[26px] leading-[39px]">
                  Withdraw Money
                </Text>
              </View>
            </View>
          </View>
          {/* Amount Details Area */}
          <View className="items-center justify-center mt-6 mb-14">
            <Text className="font-[500] text-[16px] leading-[24px] text-[#627164]">
              Balance Available
            </Text>
            <View className="mt-1 flex-row items-center">
              <View>
                <Rsicon width={16} height={25} />
              </View>
              <Text className="font-[400] text-[24px] leading-[36px]">
                {amount}
              </Text>
            </View>
          </View>
          {/* Enter Amount Area */}
          <View className="flex-row items-center justify-center">
            <View className="mr-4">
              <Rsicon width={16} height={25} />
            </View>
            <TextInput
              className="font-[400] text-[24px] leading-[36px] text-[#627164]"
              cursorColor={"#000"}
              value={value}
              onFocus={() => {
                setvalue("");
              }}
              onChangeText={setvalue}
              keyboardType="numeric"
            />
          </View>
        </View>
        <View className="">
          <TouchableOpacity
            onPress={() => {
              if (value < 100) {
                return Alert.alert(
                  "Amount Less then 100Rs",
                  "The Amount needs to be more than 100 Rs."
                );
              }
              if (Number(value) > Number(amount)) {
                return Alert.alert(
                  "Not enough money.",
                  "You do not have enough balance to withdraw."
                );
              }
              router.push({
                pathname: "home/profile/wallet/withdrawmoney/choosemode",
                params: {
                  amount: value,
                },
              });
            }}
            className="items-center justify-center h-[44px] bg-[#00660A] mb-4"
          >
            <Text className="font-[400] text-[16px] leading-[24px] text-[#FFFFFF]">
              Withdraw Money
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default withdrawmoney;
