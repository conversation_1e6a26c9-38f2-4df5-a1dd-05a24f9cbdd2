{"logs": [{"outputFile": "com.mohandhass2.myapp-mergeReleaseResources-83:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4d414adee14b24510c264c21c70961f0\\transformed\\appcompat-1.7.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "799,894,987,1087,1169,1266,1374,1451,1526,1618,1712,1803,1899,1994,2088,2184,2276,2368,2460,2538,2634,2729,2824,2921,3017,3115,3266,14996", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "889,982,1082,1164,1261,1369,1446,1521,1613,1707,1798,1894,1989,2083,2179,2271,2363,2455,2533,2629,2724,2819,2916,3012,3110,3261,3355,15070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,193,260,326,401,466,531,600,671,744,819,886,956,1029,1101,1178,1254,1326,1396,1465,1545,1613,1683,1750", "endColumns": "65,71,66,65,74,64,64,68,70,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "116,188,255,321,396,461,526,595,666,739,814,881,951,1024,1096,1173,1249,1321,1391,1460,1540,1608,1678,1745,1814"}, "to": {"startLines": "50,64,143,145,146,150,163,164,165,202,203,206,207,210,211,212,214,215,217,219,220,222,225,227,228", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3360,4574,10489,10615,10681,10965,11804,11869,11938,14563,14636,14859,14926,15142,15215,15287,15437,15513,15655,15794,15863,16044,16252,16438,16505", "endColumns": "65,71,66,65,74,64,64,68,70,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "3421,4641,10551,10676,10751,11025,11864,11933,12004,14631,14706,14921,14991,15210,15282,15359,15508,15580,15720,15858,15938,16107,16317,16500,16569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b84b30b80c5949bd0f3610f30992b047\\transformed\\play-services-base-18.3.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4646,4747,4875,4990,5092,5199,5315,5417,5618,5728,5829,5958,6073,6180,6288,6343,6400", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "4742,4870,4985,5087,5194,5310,5412,5505,5723,5824,5953,6068,6175,6283,6338,6395,6467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a39c8a492fe916611935c0d3835604e6\\transformed\\material-1.6.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,269,359,466,539,601,679,738,796,874,931,987,1046,1104,1158,1244,1300,1358,1412,1477,1570,1644,1722,1812,1875,1938,2015,2082,2148,2212,2281,2356,2417,2488,2555,2615,2695,2758,2841,2926,3000,3065,3141,3189,3253,3329,3407,3469,3533,3596,3676,3751,3827,3903", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,66,89,106,72,61,77,58,57,77,56,55,58,57,53,85,55,57,53,64,92,73,77,89,62,62,76,66,65,63,68,74,60,70,66,59,79,62,82,84,73,64,75,47,63,75,77,61,63,62,79,74,75,75,68", "endOffsets": "197,264,354,461,534,596,674,733,791,869,926,982,1041,1099,1153,1239,1295,1353,1407,1472,1565,1639,1717,1807,1870,1933,2010,2077,2143,2207,2276,2351,2412,2483,2550,2610,2690,2753,2836,2921,2995,3060,3136,3184,3248,3324,3402,3464,3528,3591,3671,3746,3822,3898,3967"}, "to": {"startLines": "19,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "652,3426,4154,4244,4351,6812,10125,10556,10907,11030,11108,11165,11221,11280,11338,11392,11478,11534,11592,11646,11711,12009,12083,12161,12251,12314,12377,12454,12521,12587,12651,12720,12795,12856,12927,12994,13054,13134,13197,13280,13365,13439,13504,13580,13628,13692,13768,13846,13908,13972,14035,14115,14190,14266,14494", "endLines": "22,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,201", "endColumns": "12,66,89,106,72,61,77,58,57,77,56,55,58,57,53,85,55,57,53,64,92,73,77,89,62,62,76,66,65,63,68,74,60,70,66,59,79,62,82,84,73,64,75,47,63,75,77,61,63,62,79,74,75,75,68", "endOffsets": "794,3488,4239,4346,4419,6869,10198,10610,10960,11103,11160,11216,11275,11333,11387,11473,11529,11587,11641,11706,11799,12078,12156,12246,12309,12372,12449,12516,12582,12646,12715,12790,12851,12922,12989,13049,13129,13192,13275,13360,13434,13499,13575,13623,13687,13763,13841,13903,13967,14030,14110,14185,14261,14337,14558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7460a359c4c281b49833222722dfee74\\transformed\\play-services-wallet-18.1.3\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "206", "endColumns": "67", "endOffsets": "273"}, "to": {"startLines": "231", "startColumns": "4", "startOffsets": "16732", "endColumns": "71", "endOffsets": "16799"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2d12819c5c8831c86b34929884541b49\\transformed\\play-services-basement-18.3.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "5510", "endColumns": "107", "endOffsets": "5613"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\00636ad238812e00d92433007e026a91\\transformed\\exoplayer-core-2.18.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,478,554", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "106,162,220,273,345,399,473,549,608"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8530,8586,8642,8700,8753,8825,8879,8953,9029", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "8581,8637,8695,8748,8820,8874,8948,9024,9083"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eeb7a35c1d2dda21edaace2253c1f099\\transformed\\exoplayer-ui-2.18.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,602,672,741,811,887,962,1017,1078,1152,1226,1288,1349,1408,1473,1562,1648,1737,1800,1867,1932,1987,2061,2134,2195,2258,2310,2368,2415,2476,2532,2594,2651,2711,2767,2822,2885,2947,3010,3059,3112,3179,3246", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,88,85,88,62,66,64,54,73,72,60,62,51,57,46,60,55,61,56,59,55,54,62,61,62,48,52,66,66,48", "endOffsets": "276,439,597,667,736,806,882,957,1012,1073,1147,1221,1283,1344,1403,1468,1557,1643,1732,1795,1862,1927,1982,2056,2129,2190,2253,2305,2363,2410,2471,2527,2589,2646,2706,2762,2817,2880,2942,3005,3054,3107,3174,3241,3290"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,331,494,6874,6944,7013,7083,7159,7234,7289,7350,7424,7498,7560,7621,7680,7745,7834,7920,8009,8072,8139,8204,8259,8333,8406,8467,9088,9140,9198,9245,9306,9362,9424,9481,9541,9597,9652,9715,9777,9840,9889,9942,10009,10076", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,88,85,88,62,66,64,54,73,72,60,62,51,57,46,60,55,61,56,59,55,54,62,61,62,48,52,66,66,48", "endOffsets": "326,489,647,6939,7008,7078,7154,7229,7284,7345,7419,7493,7555,7616,7675,7740,7829,7915,8004,8067,8134,8199,8254,8328,8401,8462,8525,9135,9193,9240,9301,9357,9419,9476,9536,9592,9647,9710,9772,9835,9884,9937,10004,10071,10120"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e9aa3803274a6cf3eaa3a68ef58f1ff5\\transformed\\ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,255,343,434,512,586,663,741,815,888,963,1030,1103,1173,1242,1317,1382", "endColumns": "75,73,87,90,77,73,76,77,73,72,74,66,72,69,68,74,64,115", "endOffsets": "176,250,338,429,507,581,658,736,810,883,958,1025,1098,1168,1237,1312,1377,1493"}, "to": {"startLines": "62,63,84,85,86,147,148,199,200,204,205,209,213,216,218,223,224,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4424,4500,6555,6643,6734,10756,10830,14342,14420,14711,14784,15075,15364,15585,15725,16112,16187,16322", "endColumns": "75,73,87,90,77,73,76,77,73,72,74,66,72,69,68,74,64,115", "endOffsets": "4495,4569,6638,6729,6807,10825,10902,14415,14489,14779,14854,15137,15432,15650,15789,16182,16247,16433"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f0ad3059e848ec2624bad4e9e6fc7d3\\transformed\\browser-1.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "83,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "6472,10203,10295,10396", "endColumns": "82,91,100,92", "endOffsets": "6550,10290,10391,10484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b14b43fc1759186e6ca5ed92b9460e08\\transformed\\foundation-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,136", "endColumns": "80,76", "endOffsets": "131,208"}, "to": {"startLines": "229,230", "startColumns": "4,4", "startOffsets": "16574,16655", "endColumns": "80,76", "endOffsets": "16650,16727"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6591935515ee6cd5ec7881ab1d3c64e\\transformed\\core-1.13.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "52,53,54,55,56,57,58,221", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3493,3585,3684,3778,3872,3965,4058,15943", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3580,3679,3773,3867,3960,4053,4149,16039"}}]}]}