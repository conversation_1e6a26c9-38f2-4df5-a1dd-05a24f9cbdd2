import { View, Text, TouchableOpacity, Image } from "react-native";
import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import BackIcon from "@/assets/icons/BackArrow.svg";
import ManShirt from "../../../../../assets/Rewards/man-T-shirt.png";
import WoManShirt from "../../../../../assets/Rewards/woman-T-shirt.png";
import { ScrollView } from "react-native";

const index = () => {
  const [gender, setgender] = useState("Man");
  const [Size, setSize] = useState("S");
  const [Unit, setUnit] = useState("cm");

  return (
    <SafeAreaView className="flex-1">
      <ScrollView>
        <View className="items-center justify-center relative mt-6">
          <TouchableOpacity
            className="absolute left-[3%]"
            onPress={() => {
              router.back();
            }}
          >
            <BackIcon />
          </TouchableOpacity>
          <View>
            <Text className="font-[600] text-[20px] leading-[30px]">
              Select your T-shirt size
            </Text>
          </View>
        </View>
        <View className="px-4 mt-4">
          <View className="items-center">
            <Text className="font-[500] font-Pop text-[20px] leading-[30px]">
              I am a
            </Text>
          </View>
          <View className="items-center mt-4">
            <View className="flex-row w-[165px] h-[34px] items-center justify-center border-[1px] border-[#ACB9D5] rounded-[20px]">
              <TouchableOpacity
                onPress={() => {
                  setgender("Man");
                }}
                className="relative w-[80px] h-[26px] rounded-[40px] items-center justify-center"
              >
                <Text
                  style={{
                    color: gender === "Man" ? "#fff" : "#001A03",
                  }}
                  className="z-10 font-[400] font-Pop text-[12px] leading-[18px] "
                >
                  Man
                </Text>
                {gender === "Man" && (
                  <View className="absolute w-full h-full bg-[#00660A] rounded-[40px]" />
                )}
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  setgender("Woman");
                }}
                className="relative w-[80px] h-[26px] rounded-[40px] items-center justify-center"
              >
                <Text
                  style={{
                    color: gender === "Woman" ? "#fff" : "#001A03",
                  }}
                  className="font-[400] font-Pop text-[12px] leading-[18px] z-10"
                >
                  Woman
                </Text>
                {gender === "Woman" && (
                  <View className="absolute w-full h-full bg-[#00660A] rounded-[40px]" />
                )}
              </TouchableOpacity>
            </View>
          </View>
          <View className="items-center mt-10">
            <Image
              source={gender === "Man" ? ManShirt : WoManShirt}
              className="object-cover"
            />
          </View>
          <View className="items-center mt-4">
            <Text className="">Size: {Size}</Text>
          </View>
          <View className="items-center flex-row justify-center space-x-5 mt-4">
            <TouchableOpacity
              onPress={() => {
                setSize("S");
              }}
              style={{
                borderColor: Size === "S" ? "#00660A" : "#E9EAE9",
              }}
              className="w-[43px] h-[43px] items-center justify-center border rounded-[8px]"
            >
              <Text className="">S</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setSize("M");
              }}
              style={{
                borderColor: Size === "M" ? "#00660A" : "#E9EAE9",
              }}
              className="w-[43px] h-[43px] items-center justify-center border rounded-[8px]"
            >
              <Text className="">M</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setSize("L");
              }}
              style={{
                borderColor: Size === "L" ? "#00660A" : "#E9EAE9",
              }}
              className="w-[43px] h-[43px] items-center justify-center border rounded-[8px]"
            >
              <Text className="">L</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setSize("XL");
              }}
              style={{
                borderColor: Size === "XL" ? "#00660A" : "#E9EAE9",
              }}
              className="w-[43px] h-[43px] items-center justify-center border rounded-[8px]"
            >
              <Text className="">XL</Text>
            </TouchableOpacity>
          </View>
          <View className="items-center mt-5 flex-row space-x-10 justify-center ">
            <Text className="font-[500] font-Pop text-[18px] leading-[27px] text-[#627164]">
              Size Chart
            </Text>
            <View className="">
              <View className="flex-row w-[90px] h-[26px] items-center justify-center border-[1px] border-[#ACB9D5] rounded-[20px]">
                <TouchableOpacity
                  onPress={() => {
                    setUnit("In");
                  }}
                  className="relative w-[45px] h-[26px] rounded-tl-[40px] rounded-bl-[40px]  items-center justify-center bg-[#D9D9D9]"
                >
                  <Text
                    style={{
                      color: Unit === "Man" ? "#fff" : "#001A03",
                    }}
                    className="z-10 font-[400] font-Pop text-[12px] leading-[18px] "
                  >
                    In
                  </Text>
                  {Unit === "In" && (
                    <View className="absolute w-full h-full bg-[#00660A] rounded-[40px]" />
                  )}
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    setUnit("cm");
                  }}
                  className="relative w-[46px] h-[26px] rounded-tr-[40px] rounded-br-[40px] items-center justify-center bg-[#D9D9D9]"
                >
                  <Text
                    style={{
                      color: Unit === "cm" ? "#fff" : "#001A03",
                    }}
                    className="font-[400] font-Pop text-[12px] leading-[18px] z-10"
                  >
                    cm
                  </Text>
                  {Unit === "cm" && (
                    <View className="absolute w-full h-full bg-[#00660A] rounded-[40px]" />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
          <View className="my-6">
            <View className="flex-row ">
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">Size</Text>
              </View>
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">Chest (in)</Text>
              </View>
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">Front Length (in)</Text>
              </View>
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">Across Shoulder (in)</Text>
              </View>
            </View>
            <View className="flex-row mt-4">
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">S</Text>
              </View>
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">96.5</Text>
              </View>
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">69.9</Text>
              </View>
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">41.3</Text>
              </View>
            </View>
            <View className="flex-row mt-4">
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">M</Text>
              </View>
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">101.6</Text>
              </View>
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">71.1</Text>
              </View>
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">43.2</Text>
              </View>
            </View>
            <View className="flex-row mt-4">
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">L</Text>
              </View>
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">106.7</Text>
              </View>
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">72.4</Text>
              </View>
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">45.1</Text>
              </View>
            </View>
            <View className="flex-row mt-4">
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">XL</Text>
              </View>
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">113</Text>
              </View>
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">73.7</Text>
              </View>
              <View className="flex-1 items-center justify-center">
                <Text className="text-center">47.3</Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default index;
