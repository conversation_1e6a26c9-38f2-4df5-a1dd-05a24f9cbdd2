import * as ImagePicker from "expo-image-picker";
import BackArrow from "../../../../assets/icons/BackArrow.svg";
import CamaraIcon from "@/assets/icons/bottemSheetIcon/camara.svg";
import Close from "@/assets/icons/bottemSheetIcon/Close.svg";
import DocIcon from "@/assets/icons/bottemSheetIcon/gallery.svg";
import RBSheet from "react-native-raw-bottom-sheet";
import React, { useRef, useState } from "react";
import TextinputFile from "../../../../component/TextinputFile";
import UpArrow from "../../../../assets/profile/uparrow.svg";
import UploadIcon from "@/assets/icons/uploadIcon.svg";
import useTenStackMutate from "@/hooks/useTenStackMutate/useTenStackMutate";
import useUpdateWithFormData from "../../../../hooks/useUpdateWithFormData";
import { router } from "expo-router";
import { Try } from "expo-router/build/views/Try";
import { useForm } from "react-hook-form";
import { Alert, Text, View } from "react-native";
import { TouchableOpacity } from "react-native";
import { Image } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { UploadField } from "../addprofile/page1";

const addDetails = () => {
  const { control, setValue, handleSubmit } = useForm();

  const { mutate: upload_driving_licence_front_part } = useTenStackMutate({
    invalidateQueriesKey: ["upload_driving_licence_front_part"],
    endpoint: "auth/upload_driving_licence_front_part",
  });
  const { mutate: upload_driving_licence_back_part } = useTenStackMutate({
    invalidateQueriesKey: ["upload_driving_licence_back_part"],
    endpoint: "auth/upload_driving_licence_back_part",
  });
  const { mutate: upload_registration_certificate } = useTenStackMutate({
    invalidateQueriesKey: ["upload_registration_certificate"],
    endpoint: "auth/upload_registration_certificate",
  });
  const Submit = () => {
    handleSubmit(async (data) => {
      if (
        data?.licence_front_part === undefined ||
        data?.licence_front_part === null ||
        data?.licence_front_part === ""
      ) {
        Alert.alert("Error", "Please upload a licence_front_part picture");
        return;
      }
      if (
        data?.licence_back_part === undefined ||
        data?.licence_back_part === null ||
        data?.licence_back_part === ""
      ) {
        Alert.alert("Error", "Please upload a licence_back_part picture");
        return;
      }
      if (
        data?.registration_certificate === undefined ||
        data?.registration_certificate === null ||
        data?.registration_certificate === ""
      ) {
        Alert.alert(
          "Error",
          "Please upload a registration_certificate picture"
        );
        return;
      }
      try {
        const licence_front_part_f = new FormData();
        const licence_back_part_f = new FormData();
        const registration_certificate_f = new FormData();

        // Fix the registration certificate FormData
        registration_certificate_f.append("registration_certificate", {
          uri: data?.registration_certificate,
          name: "registration_certificate.jpg",
          type: "image/jpeg",
        } as any);

        // Fix the driving license FormData
        licence_front_part_f.append("driving_licence", {
          uri: data?.licence_front_part,
          name: "licence_front.jpg",
          type: "image/jpeg",
        } as any);

        licence_back_part_f.append("driving_licence", {
          uri: data?.licence_back_part,
          name: "licence_back.jpg",
          type: "image/jpeg",
        } as any);

        // Upload registration certificate first
        await upload_registration_certificate(registration_certificate_f, {
          onSuccess: (response) => {
            console.log("Registration certificate response:", response);
            console.log("registration_certificate uploaded successfully");
          },
          onError: (error) => {
            console.error("Registration certificate error:", error);
            Alert.alert("Error", "Failed to upload registration certificate");
            throw error; // Prevent continuing if this fails
          },
        });

        // Then upload driving license parts
        await upload_driving_licence_front_part(licence_front_part_f, {
          onSuccess: (response) => {
            console.log("Front license response:", response);
            console.log("licence_front_part uploaded successfully");
          },
          onError: (error) => {
            console.error("Front license error:", error);
            Alert.alert("Error", "Failed to upload licence front part");
            throw error;
          },
        });

        await upload_driving_licence_back_part(licence_back_part_f, {
          onSuccess: (response) => {
            console.log("Back license response:", response);
            console.log("licence_back_part uploaded successfully");
          },
          onError: (error) => {
            console.error("Back license error:", error);
            Alert.alert("Error", "Failed to upload licence back part");
            throw error;
          },
        });

        // If all uploads successful, navigate
        router.push({
          pathname: "/auth/createprofile/worksettingcompleted/completed",
        });
      } catch (error) {
        console.error("Upload error:", error);
        Alert.alert("Error", "Failed to upload documents. Please try again.");
      }
    })();
  };
  return (
    <SafeAreaView className="flex-1 justify-between">
      <View className="">
        <View className="px-5">
          <View className="flex-row relative items-center justify-start mt-4">
            <TouchableOpacity
              onPress={() => {
                router.back();
              }}
              className="absolute"
            >
              <View>
                <BackArrow />
              </View>
            </TouchableOpacity>
            <View className="flex-1 items-center justify-center">
              <Text className="font-[400] text-[19px] leading-[39px]">
                Create your profile
              </Text>
            </View>
          </View>
        </View>

        <View className="px-4">
          <View className="mt-6">
            <Text className="font-Pop font-[400] text-[18px] leading-[27px]">
              Upload your documents
            </Text>
            <Text className="font-Pop font-[400] text-[14px] leading-[21px]">
              Upload all documents to start earning
            </Text>
          </View>
          <View className="">
            {/* Aadhar card TextField Area */}
            <View className="mt-4">
              <Text className="font-[400] text-[16px] text-[#4D4D4D]">
                1. Driving License (compulsory)
              </Text>
            </View>
          </View>

          <View
            className="mt-4 justify-between"
            style={{
              flexDirection: "row",
            }}
          >
            <UploadField
              text={"Front side"}
              setValue={setValue}
              name={"licence_front_part"}
              key={1}
            />
            <UploadField
              text={"Back side"}
              setValue={setValue}
              name={"licence_back_part"}
              key={2}
            />
          </View>

          <View className="mt-6">
            <Text className="font-[400] text-[16px] leading-[20px] font-Pop">
              2. Registration Certificate (compulsory)
            </Text>
            {/* Last Name TextField Area */}
            <View className="mt-4">
              <Text className="font-[400] text-[16px] text-[#4D4D4D]">
                Image ( PNG, JPG, PDF)
              </Text>
            </View>
          </View>

          <View className="mt-5 w-full">
            <UploadField2
              text={"Back side"}
              setValue={setValue}
              name={"registration_certificate"}
              key={1}
            />
          </View>
        </View>
      </View>
      <TouchableOpacity
        onPress={() => {
          Submit();
        }}
        className="m-4 h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
      >
        <Text className="font-[400] text-[16px] leading-[24px] text-[#fff] font-Pop">
          Continue
        </Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export const UploadField2 = ({ title, text, setValue, images, name }) => {
  const [image, setImage] = useState(images ? images : "");

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      quality: 1,
    });

    if (!result.canceled) {
      setValue(name, result.assets[0].uri);
      setImage((state) => {
        return result.assets[0].uri;
      });
    }
  };
  const openCamera = async () => {
    let result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      quality: 1,
    });

    if (!result.canceled) {
      setValue(name, result.assets[0].uri);
      setImage(() => {
        return result.assets[0].uri;
      });
    }
  };
  const refRBSheet = useRef();
  return (
    <TouchableOpacity
      onPress={() => {
        refRBSheet.current.open();
      }}
      className="border-[1px] space-y-5 border-[#E9EAE9] h-[120px] w-full items-center justify-center rounded-[8px]"
    >
      <UpArrow />
      <Text className="font-[500] font-Pop text-[12px] leading-[18px] text-[#00660A]">
        Browse Files
      </Text>
      <Image
        className="absolute"
        source={{ uri: image }}
        style={{ width: 100, height: 100 }}
      />

      <View className="mt-2">
        <RBSheet
          ref={refRBSheet}
          customStyles={{
            container: {
              borderRadius: 20,
            },
            draggableIcon: {
              backgroundColor: "#000",
            },
          }}
          customModalProps={{
            statusBarTranslucent: true,
          }}
          customAvoidingViewProps={{
            enabled: false,
          }}
        >
          <View className="px-6 py-2 justify-center">
            <View className="mt-2">
              <View className="flex-row justify-between">
                <View />
                <TouchableOpacity
                  onPress={() => {
                    refRBSheet.current.close();
                  }}
                >
                  <Close />
                </TouchableOpacity>
              </View>
            </View>
          </View>
          <View className="flex-1 flex-row items-center justify-evenly">
            <TouchableOpacity
              onPress={() => {
                openCamera();
              }}
            >
              <View className="items-center justify-center space-y-2">
                <CamaraIcon width={40} height={40} />
                <Text>Camara</Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                pickImage();
              }}
            >
              <View className="items-center justify-center space-y-2">
                <DocIcon width={40} height={40} />
                <Text>Document</Text>
              </View>
            </TouchableOpacity>
          </View>
        </RBSheet>
      </View>
    </TouchableOpacity>
  );
};

export default addDetails;
