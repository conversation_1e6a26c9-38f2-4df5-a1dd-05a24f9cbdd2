{"info": {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}, "cxxBuildFolder": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\.cxx\\RelWithDebInfo\\1a5c8433\\x86_64", "soFolder": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433\\obj\\x86_64", "soRepublishFolder": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cmake\\release\\obj\\x86_64", "abiPlatformVersion": 24, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DPROJECT_BUILD_DIR=D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build", "-DREACT_ANDROID_DIR=D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native\\ReactAndroid", "-DANDROID_STL=c++_shared", "-DANDROID_USE_LEGACY_TOOLCHAIN_FILE=ON"], "cFlagsList": [], "cppFlagsList": [], "variantName": "release", "isDebuggableEnabled": false, "validAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\.cxx", "intermediatesBaseFolder": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates", "intermediatesFolder": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx", "gradleModulePathName": ":app", "moduleRootFolder": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app", "moduleBuildFile": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build.gradle", "makeFile": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125", "ndkFolderBeforeSymLinking": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125", "ndkVersion": "26.1.10909125", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 34, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "x86": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android", "sdkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": true}, "outputOptions": [], "ninjaExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": ["C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar"], "prefabPackages": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\prefab", "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\intermediates\\prefab_package\\release\\prefab", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8a5944fdc62bae076801e020e1281841\\transformed\\hermes-android-0.76.9-release\\prefab", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6dd910b94089a928dab663afb856f4b\\transformed\\fbjni-0.6.0\\prefab"], "prefabPackageConfigurations": ["D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\intermediates\\prefab_package_configuration\\release\\prefabReleaseConfigurePackage\\prefab_publication.json"], "stlType": "c++_shared", "optimizationTag": "RelWithDebInfo"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\.cxx\\RelWithDebInfo\\1a5c8433\\prefab\\x86_64", "isActiveAbi": true, "fullConfigurationHash": "1a5c84334d18493618686i59j713q5u1jz6o6xm6e1z382z1f2r134j1g5l", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.6.0.\n#   - $NDK is the path to NDK 26.1.10909125.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HD:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=24\n-DANDROID_PLATFORM=android-24\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DC<PERSON>KE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=RelWithDebInfo\n-DCMAKE_FIND_ROOT_PATH=$PROJECT/app/.cxx/RelWithDebInfo/$HASH/prefab/$ABI/prefab\n-B$PROJECT/app/.cxx/RelWithDebInfo/$HASH/$ABI\n-GNinja\n-DPROJECT_BUILD_DIR=$PROJECT/app/build\n-DREACT_ANDROID_DIR=D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid\n-DANDROID_STL=c++_shared\n-DANDROID_USE_LEGACY_TOOLCHAIN_FILE=ON", "configurationArguments": ["-HD:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=24", "-DANDROID_PLATFORM=android-24", "-DANDROID_ABI=x86_64", "-DCMAKE_ANDROID_ARCH_ABI=x86_64", "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125", "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125", "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433\\obj\\x86_64", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433\\obj\\x86_64", "-DCMAKE_BUILD_TYPE=RelWithDebInfo", "-DCMAKE_FIND_ROOT_PATH=D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\.cxx\\RelWithDebInfo\\1a5c8433\\prefab\\x86_64\\prefab", "-BD:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\.cxx\\RelWithDebInfo\\1a5c8433\\x86_64", "-<PERSON><PERSON><PERSON><PERSON>", "-DPROJECT_BUILD_DIR=D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build", "-DREACT_ANDROID_DIR=D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native\\ReactAndroid", "-DANDROID_STL=c++_shared", "-DANDROID_USE_LEGACY_TOOLCHAIN_FILE=ON"], "stlLibraryFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so", "intermediatesParentFolder": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433"}