import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AxiosHeaders } from "axios";
import { axiosInstance } from "@/api/axios";

const useTenStackMutate = ({
  invalidateQueriesKey,
  endpoint,
}: {
  invalidateQueriesKey: string[];
  endpoint: string;
}) => {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (data?: any) => {
      try {
        const isFormData = data instanceof FormData;

        const headers = {
          "Access-Control-Allow-Origin": "*",
          ...(isFormData
            ? { "Content-Type": "multipart/form-data" }
            : { "Content-Type": "application/json" }),
        };

        console.log("Sending request to:", endpoint);
        console.log("With data:", data);
        console.log("Headers:", headers);

        const response = await axiosInstance
          .post(endpoint, data, { headers })
          .catch((error) => {
            console.error("API Error:", error.response?.data || error.message);
            throw error;
          });

        console.log("Response received:", response.data);

        if (!response.data) {
          throw new Error("No response data received");
        }

        return response.data;
      } catch (error) {
        console.error("API Error:", error.response?.data || error.message);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log("Mutation successful:", data);
      invalidateQueriesKey.forEach((key) => {
        queryClient.invalidateQueries({ queryKey: [key] });
      });
    },
    onError: (error) => {
      console.error("Mutation error:", error);
    },
  });

  return {
    ...mutation,
    isLoading: mutation.isPending,
  };
};

export default useTenStackMutate;
