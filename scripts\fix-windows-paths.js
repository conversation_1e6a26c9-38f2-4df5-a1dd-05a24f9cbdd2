/**
 * This script fixes Windows path issues with Metro bundler in Expo
 * It specifically addresses the error with creating directories containing colons
 */

const fs = require("fs");
const path = require("path");

// Only apply the fix on Windows
if (process.platform === "win32") {
  console.log("Applying Windows path fix for Metro bundler...");

  // Create the directory structure without the colon
  const projectRoot = process.cwd();
  const expoDir = path.join(projectRoot, ".expo");
  const metroDir = path.join(expoDir, "metro");
  const externalsDir = path.join(metroDir, "externals");
  const nodeSeaDir = path.join(externalsDir, "node_sea"); // Use underscore instead of colon

  // Create all directories recursively
  try {
    if (!fs.existsSync(externalsDir)) {
      fs.mkdirSync(externalsDir, { recursive: true });
      console.log(`Created directory: ${externalsDir}`);
    }

    if (!fs.existsSync(nodeSeaDir)) {
      fs.mkdirSync(nodeSeaDir);
      console.log(`Created directory: ${nodeSeaDir}`);

      // Create a placeholder file to make Metro happy
      fs.writeFileSync(
        path.join(nodeSeaDir, "index.js"),
        "// This is a placeholder file for Windows compatibility\nmodule.exports = {};"
      );
      console.log("Created placeholder index.js file");
    }

    // Monkey-patch the fs.mkdir function to handle paths with colons
    const originalMkdir = fs.mkdir;
    fs.mkdir = function (dirPath, options, callback) {
      // Check if the path contains a colon (excluding drive letter)
      if (typeof dirPath === "string" && dirPath.substring(2).includes(":")) {
        // Replace the problematic path with our safe path
        if (dirPath.includes("node:sea")) {
          const safePath = dirPath.replace("node:sea", "node_sea");
          console.log(`Redirecting mkdir from ${dirPath} to ${safePath}`);
          return originalMkdir(safePath, options, callback);
        }
      }

      // Call the original function for all other paths
      return originalMkdir(dirPath, options, callback);
    };

    // Also patch the fs.promises.mkdir function
    const originalPromisesMkdir = fs.promises.mkdir;
    fs.promises.mkdir = function (dirPath, options) {
      // Check if the path contains a colon (excluding drive letter)
      if (typeof dirPath === "string" && dirPath.substring(2).includes(":")) {
        // Replace the problematic path with our safe path
        if (dirPath.includes("node:sea")) {
          const safePath = dirPath.replace("node:sea", "node_sea");
          console.log(
            `Redirecting promises.mkdir from ${dirPath} to ${safePath}`
          );
          return originalPromisesMkdir(safePath, options);
        }
      }

      // Call the original function for all other paths
      return originalPromisesMkdir(dirPath, options);
    };

    console.log("Successfully applied Windows path fix for Metro bundler");
  } catch (error) {
    console.error("Error applying Windows path fix:", error);
  }
} else {
  console.log("Not running on Windows, no path fix needed");
}
