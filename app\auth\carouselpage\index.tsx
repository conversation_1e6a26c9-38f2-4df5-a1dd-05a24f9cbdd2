import AsyncStorage from "@react-native-async-storage/async-storage";
import React, { useEffect, useState } from "react";
import useLoginApi from "../../../hooks/useLoginApi";
import { Redirect, router } from "expo-router";
import { Controller, useForm } from "react-hook-form";
import { SafeAreaView } from "react-native-safe-area-context";
import { useLogin } from "../../../store";
import { PhoneNumberField } from "../createaccount";

import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  Image,
  Alert,
  ScrollView,
} from "react-native";

const index = () => {
  const data = [
    {
      id: 1,
      image: require("../../../assets/images/slidImage/Frame **********.png"),
    },
    {
      id: 2,
      image: require("../../../assets/images/slidImage/Layer_2.png"),
    },
  ];
  const {
    control,
    watch,
    handleSubmit,
    reset,
    formState: { errors, isValid },
  } = useForm();
  const { SetFunction: SendOtp } = useLoginApi({
    endpoint: "sendOtp",
  });
  const Submit = (data) => {
    SendOtp({
      phone: Number(data.phone),
    }).then((response) => {
      reset();
      Alert.alert("Testing OTP", String(response?.data?.otp));
      router.push({
        pathname: "/auth/createaccount/otp",
        params: { number: data.phone, otp: response?.data?.otp },
      });
    });
  };
  const [Login, setLogin] = useState(false);
  const CheckLogin = async () => {
    try {
      let Token = await AsyncStorage.getItem("login");
      if (Token === null) {
        setLogin(false);
      } else {
        setLogin(true);
      }
    } catch (error) {
      console.log(error);
    }
  };
  useEffect(() => {
    CheckLogin();
  }, []);
  return (
    <SafeAreaView className="flex-1 bg-[#fff]">
      <>
        <View
          className=""
          style={{
            flex: 1,
            backgroundColor: "#000",
            minHeight: 300,
          }}
        >
          <FlatList
            data={data}
            horizontal
            keyExtractor={({ id }) => {
              return id.toString();
            }}
            snapToInterval={460}
            showsHorizontalScrollIndicator={false}
            renderItem={({ item: { id, image } }) => {
              return (
                <View key={id} className="object-contain">
                  <Image source={image} className="w-[450px] h-[460px]" />
                </View>
              );
            }}
          />
        </View>
        <View className="items-center p-6 my-10">
          <Text className="font-[500] text-[24px] leading-[22px] font-Pop p-2">
            Tap, Deliver, Get Paid
          </Text>
          <Text className="text-center font-[400] font-Pop text-[16px] leading-[19px] text-[#627164]">
            Sign up in minutes and start earning fast! The more you deliver, the
            more you fuel your financial future. Let's get rolling!
          </Text>
        </View>

        <View className="px-4 flex-1 ">
          <Controller
            name={"phone"}
            control={control}
            rules={{
              minLength: { value: 10, message: "Enter 10 digit number" },
              required: { value: true, message: "Enter your number" },
              pattern: {
                value: /^[0-9]{10}$/,
                message: "Please enter a valid 10-digit phone number",
              },
            }}
            render={({ field: { onChange, name, value } }) => (
              <View className="px-4 flex-1  justify-end mb-8">
                <TextInput
                  maxLength={10}
                  className="border-[1px] border-[#E9EAE9] h-[52px] pl-2 mt-4"
                  style={{
                    borderWidth: 1,
                    borderColor: "#E9EAE9",
                  }}
                  value={value}
                  onChangeText={onChange}
                  keyboardType="numeric"
                  placeholder="Enter your 10 digit mobile number"
                />
              </View>
            )}
          />
        </View>

        {errors.phone && (
          <Text className="text-red-500 text-center">
            {(errors as any).phone.message}
          </Text>
        )}

        <TouchableOpacity
          onPress={handleSubmit(Submit)}
          className="mx-4 my-4 h-[44px] items-center justify-center bg-[#00660A]"
        >
          <Text className="font-Pop font-[400] text-[16px] leading-[24px] text-[#fff]">
            Get Started
          </Text>
        </TouchableOpacity>
      </>
    </SafeAreaView>
  );
};

export default index;
