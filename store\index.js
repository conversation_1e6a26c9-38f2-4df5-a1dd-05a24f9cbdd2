import { create } from "zustand";
import AsyncStorage from "@react-native-async-storage/async-storage";

export const useLogin = create((set) => ({
  userToken: null,
  isLogin: false,
  setuserToken: (token) => {
    AsyncStorage.setItem("login", token);
    set((state) => ({ userToken: token }));
  },
  removeuserToken: () => {
    AsyncStorage.removeItem("login");
    set((state) => ({ userToken: null }));
  },
}));

export const UserStatus = create((set) => ({
  Status: false,
  setStatus: (status) => {
    set(() => ({ Status: status }));
  },
}));

export const useConformNumberEmail = create((set) => ({
  phone: null,
  email: null,
  setPhone: (phone) => {
    set(() => ({
      phone: phone,
    }));
  },
  setEmail: (email) => {
    set(() => ({
      email: email,
    }));
  },
}));
