                        -HD:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\intermediates\cxx\RelWithDebInfo\1a5c8433\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\intermediates\cxx\RelWithDebInfo\1a5c8433\obj\x86
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-DCMAKE_FIND_ROOT_PATH=D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\prefab\x86\prefab
-BD:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\x86
-GNinja
-DPROJECT_BUILD_DIR=D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build
-DREACT_ANDROID_DIR=D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native\ReactAndroid
-DANDROID_STL=c++_shared
-DANDROID_USE_LEGACY_TOOLCHAIN_FILE=ON
                        Build command args: []
                        Version: 2