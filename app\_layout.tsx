import * as Location from "expo-location";
import AsyncStorage from "@react-native-async-storage/async-storage";
import LoadingComponent from "../component/LoadingComponent/LoadingComponent";
import React, { useEffect, useState } from "react";
import useCheckStatus from "@/hooks/checkStatusHook/useCheckStatus";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useFonts } from "expo-font";
import { Redirect, Stack } from "expo-router";
import { Alert } from "react-native";
import { Provider } from "react-redux";
import { store } from "@/redux/store";

const queryClient = new QueryClient();

const _layout = () => {
  const [loaded] = useFonts({
    "Poppins Regular": require("../assets/font/Poppins-Regular.ttf"),
  });
  if (!loaded) {
    return null;
  }
  return (
    <QueryClientProvider client={queryClient}>
      <Provider store={store}>
        <AppContent />
      </Provider>
    </QueryClientProvider>
  );
};

const AppContent = () => {
  const [Loading, setLoading] = useState(false);
  const [Login, setLogin] = useState(false);

  const CheckLogin = async () => {
    try {
      setLoading(true);
      let Token = await AsyncStorage.getItem("login");
      if (Token === null) {
        setLogin(false);
      } else {
        setLogin(true);
      }
      setLoading(false);
    } catch (error) {
      console.log(error);
    }
  };
  // const { mutate: SetFunction } = useTenStackMutate({
  //   invalidateQueriesKey: [""],
  //   endpoint: "auth/set_current_location",
  // });
  const DeliveryPerson = async () => {
    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Permission denied",
        "Allow location access to use this feature."
      );
      return;
    }

    let location = await Location.getCurrentPositionAsync({});
    const { latitude, longitude } = location.coords;
    // SetFunction({
    //   latitude: latitude,
    //   longitude: longitude,
    // });
  };

  useEffect(() => {
    DeliveryPerson();
    CheckLogin();
  }, []);

  const { data: statusData, isLoading } = useCheckStatus();

  return (
    <>
      {isLoading ? (
        <LoadingComponent />
      ) : (
        <>
          <Stack screenOptions={{ headerShown: false }} />
          {Login ? (
            <>
              {statusData?.workStatus.data.status === 1 &&
              statusData?.profileStatus.data.status === 1 ? (
                <>
                  <Redirect href={"/home/<USER>/home"} />
                </>
              ) : (
                <>
                  <Redirect href={"/auth/createprofile"} />
                </>
              )}
            </>
          ) : (
            <>
              <Redirect href={"/auth/carouselpage"} />
              {/* <Redirect href={"/auth/createprofile/worksetting"} /> */}
            </>
          )}
        </>
      )}
    </>
  );
};

export default _layout;
