import React, { useEffect } from "react";
import backgroundimg from "@/assets/profile/bg.png";
import { router } from "expo-router";
import { Image, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const completed = () => {
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      router.dismissAll();
      router.replace("/home/<USER>/home");
    }, 4000);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [router]);
  return (
    <SafeAreaView className="flex-1">
      <View className="mt-7 px-5 relative flex-1 justify-center">
        <View className="absolute left-0 right-0 top-0 bottom-0 ">
          <Image
            source={backgroundimg}
            style={{
              zIndex: -10,
              opacity: 0.5,
            }}
          />
        </View>
        <View className="z-50 items-center justify-center p-8">
          <Text className="text-[#000] font-Pop font-[700] text-center leading-[48px] text-[32px]">
            Signup has been successfully completed !
          </Text>
          <Text className="mt-4 font-[500] text-[19px] leading-[27px] text-center">
            We will verify your profile and update you within 2-3 business days
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default completed;
