import React from "react";
import { Controller } from "react-hook-form";
import { TextInput, TouchableOpacity, View } from "react-native";

const TextinputFile = (props) => {
  return (
    <>
      <View
        className={
          "px-2 border-[1px] border-[#ACB9D5] h-[40px] items-center flex-row mt-2 rounded-[4px] justify-between " +
          props.containerstyle
        }
      >
        <Controller
          control={props?.control}
          name={props?.name}
          rules={{
            ...props?.rules,
          }}
          render={({ field: { onChange, onBlur, value } }) => (
            <TextInput
              value={value}
              onChangeText={(value) => onChange(value)}
              style={{
                color: "#000",
                flex: 1,
              }}
              placeholder={props?.placeholderVal}
              className={props.inputstyle}
              defaultValue={props?.placeholder}
              editable={props?.editable}
            />
          )}
        />
        <TouchableOpacity>{props.component}</TouchableOpacity>
      </View>
    </>
  );
};

export default TextinputFile;
