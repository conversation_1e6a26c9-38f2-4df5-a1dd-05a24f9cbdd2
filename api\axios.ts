import AsyncStorage from "@react-native-async-storage/async-storage";
import Constants from "expo-constants";
import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from "axios";

export const axiosInstance = axios.create({
  baseURL: Constants.expoConfig?.extra?.BASE_URL,
});

interface LoginResponse {
  payload: {
    token: string;
  };
}

interface CustomAxiosRequestConfig extends AxiosRequestConfig {
  _retry?: boolean;
}

interface QueueItem {
  resolve: (token: string) => void;
  reject: (error: AxiosError) => void;
}

let isRefreshing: boolean = false;
let failedQueue: QueueItem[] = [];
const processQueue = (error: AxiosError | null, token?: string) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else if (token) {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};
axiosInstance.interceptors.request.use(
  async (config) => {
    console.log(JSON.stringify(`${config.data} Date passed to API`, null, 2));
    console.log(
      JSON.stringify(`${config.headers} Headers passed to API`, null, 2)
    );
    const token = await AsyncStorage.getItem("login");
    if (token) {
      config.headers.Authorization = `${token}`;
    }

    if (config.data instanceof FormData) {
      config.headers["Content-Type"] = "multipart/form-data";
    }

    return config;
  },
  (error) => {
    console.log(
      "Request Error:",
      JSON.stringify(error || error.message, null, 2)
    );
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config as CustomAxiosRequestConfig;
    const number = await AsyncStorage.getItem("Number");
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      if (!isRefreshing) {
        isRefreshing = true;
        try {
          const response: AxiosResponse<LoginResponse> =
            await axiosInstance.post(
              `/refresh_token`,
              { phone: number },
              {
                headers: {
                  "X-Powered-By": "Express",
                  "Access-Control-Allow-Origin": "*",
                  "Content-Type": "application/json",
                },
              }
            );
          const newToken: string = response.data.payload.token;
          await AsyncStorage.setItem("login", newToken);
          processQueue(null, newToken);

          originalRequest.headers = {
            ...originalRequest.headers,
            Authorization: `${newToken}`,
          };
          return axiosInstance(originalRequest);
        } catch (refreshError) {
          const typedRefreshError = refreshError as AxiosError;
          console.error(
            typedRefreshError.response
              ? typedRefreshError.response.data
              : typedRefreshError.message
          );
          processQueue(typedRefreshError);
          await AsyncStorage.removeItem("login"); // Clear invalid token
          return Promise.reject(typedRefreshError);
        } finally {
          isRefreshing = false;
        }
      }
      return new Promise((resolve, reject) => {
        failedQueue.push({
          resolve: (token: string) => {
            originalRequest.headers = {
              ...originalRequest.headers,
              Authorization: `${token}`,
            };
            resolve(axiosInstance(originalRequest));
          },
          reject: (error: AxiosError) => {
            reject(error);
          },
        });
      });
    }
    return Promise.reject(error);
  }
);
