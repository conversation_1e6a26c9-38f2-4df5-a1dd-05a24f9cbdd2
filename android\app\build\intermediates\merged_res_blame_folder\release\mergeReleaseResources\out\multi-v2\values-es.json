{"logs": [{"outputFile": "com.mohandhass2.myapp-mergeReleaseResources-83:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9c569f151f8eecc9b25adff1be1284e4\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,223,279,332,406,476,543,609,662,734", "endColumns": "120,46,55,52,73,69,66,65,52,71,54", "endOffsets": "171,218,274,327,401,471,538,604,657,729,784"}, "to": {"startLines": "84,85,86,147,148,149,150,151,210,211,212", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7518,7639,7686,12392,12445,12519,12589,12656,17407,17460,17532", "endColumns": "120,46,55,52,73,69,66,65,52,71,54", "endOffsets": "7634,7681,7737,12440,12514,12584,12651,12717,17455,17527,17582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f0ad3059e848ec2624bad4e9e6fc7d3\\transformed\\browser-1.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,263,378", "endColumns": "106,100,114,104", "endOffsets": "157,258,373,478"}, "to": {"startLines": "83,143,144,145", "startColumns": "4,4,4,4", "startOffsets": "7411,11996,12097,12212", "endColumns": "106,100,114,104", "endOffsets": "7513,12092,12207,12312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e9aa3803274a6cf3eaa3a68ef58f1ff5\\transformed\\ui-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,283,381,484,573,652,745,837,924,1010,1101,1178,1254,1334,1410,1492,1562", "endColumns": "95,81,97,102,88,78,92,91,86,85,90,76,75,79,75,81,69,120", "endOffsets": "196,278,376,479,568,647,740,832,919,1005,1096,1173,1249,1329,1405,1487,1557,1678"}, "to": {"startLines": "62,63,87,88,89,155,156,207,208,215,216,220,224,227,229,234,235,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4813,4909,7742,7840,7943,12956,13035,17145,17237,17769,17855,18192,18512,18745,18904,19319,19401,19554", "endColumns": "95,81,97,102,88,78,92,91,86,85,90,76,75,79,75,81,69,120", "endOffsets": "4904,4986,7835,7938,8027,13030,13123,17232,17319,17850,17941,18264,18583,18820,18975,19396,19466,19670"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7460a359c4c281b49833222722dfee74\\transformed\\play-services-wallet-18.1.3\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "73", "endOffsets": "275"}, "to": {"startLines": "242", "startColumns": "4", "startOffsets": "20040", "endColumns": "77", "endOffsets": "20113"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b14b43fc1759186e6ca5ed92b9460e08\\transformed\\foundation-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "240,241", "startColumns": "4,4", "startOffsets": "19838,19938", "endColumns": "99,101", "endOffsets": "19933,20035"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6591935515ee6cd5ec7881ab1d3c64e\\transformed\\core-1.13.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "52,53,54,55,56,57,58,232", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3774,3873,3975,4075,4173,4280,4386,19145", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3868,3970,4070,4168,4275,4381,4501,19241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b84b30b80c5949bd0f3610f30992b047\\transformed\\play-services-base-18.3.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,583,687,844,973,1091,1197,1385,1490,1651,1779,1940,2093,2156,2221", "endColumns": "103,158,126,103,156,128,117,105,187,104,160,127,160,152,62,64,80", "endOffsets": "296,455,582,686,843,972,1090,1196,1384,1489,1650,1778,1939,2092,2155,2220,2301"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5074,5182,5345,5476,5584,5745,5878,6000,6270,6462,6571,6736,6868,7033,7190,7257,7326", "endColumns": "107,162,130,107,160,132,121,109,191,108,164,131,164,156,66,68,84", "endOffsets": "5177,5340,5471,5579,5740,5873,5995,6105,6457,6566,6731,6863,7028,7185,7252,7321,7406"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\00636ad238812e00d92433007e026a91\\transformed\\exoplayer-core-2.18.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,201,266,340,417,484,571,657", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "133,196,261,335,412,479,566,652,721"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10091,10174,10237,10302,10376,10453,10520,10607,10693", "endColumns": "82,62,64,73,76,66,86,85,68", "endOffsets": "10169,10232,10297,10371,10448,10515,10602,10688,10757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eeb7a35c1d2dda21edaace2253c1f099\\transformed\\exoplayer-ui-2.18.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,658,744,831,916,1012,1108,1183,1251,1346,1441,1507,1576,1642,1713,1821,1927,2034,2104,2191,2261,2341,2431,2522,2588,2652,2705,2763,2811,2870,2935,2997,3063,3135,3199,3260,3326,3391,3457,3510,3575,3654,3733", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,107,105,106,69,86,69,79,89,90,65,63,52,57,47,58,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,466,653,739,826,911,1007,1103,1178,1246,1341,1436,1502,1571,1637,1708,1816,1922,2029,2099,2186,2256,2336,2426,2517,2583,2647,2700,2758,2806,2865,2930,2992,3058,3130,3194,3255,3321,3386,3452,3505,3570,3649,3728,3786"}, "to": {"startLines": "2,11,15,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,8097,8183,8270,8355,8451,8547,8622,8690,8785,8880,8946,9015,9081,9152,9260,9366,9473,9543,9630,9700,9780,9870,9961,10027,10762,10815,10873,10921,10980,11045,11107,11173,11245,11309,11370,11436,11501,11567,11620,11685,11764,11843", "endLines": "10,14,18,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141", "endColumns": "17,12,12,85,86,84,95,95,74,67,94,94,65,68,65,70,107,105,106,69,86,69,79,89,90,65,63,52,57,47,58,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "330,516,703,8178,8265,8350,8446,8542,8617,8685,8780,8875,8941,9010,9076,9147,9255,9361,9468,9538,9625,9695,9775,9865,9956,10022,10086,10810,10868,10916,10975,11040,11102,11168,11240,11304,11365,11431,11496,11562,11615,11680,11759,11838,11896"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a39c8a492fe916611935c0d3835604e6\\transformed\\material-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,233,320,424,546,627,692,787,868,931,1020,1089,1152,1226,1290,1346,1464,1522,1584,1640,1720,1859,1948,2030,2141,2222,2302,2392,2459,2525,2604,2686,2774,2848,2925,2995,3074,3158,3242,3334,3434,3508,3589,3691,3744,3811,3904,3993,4055,4119,4182,4295,4388,4492,4586", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,86,103,121,80,64,94,80,62,88,68,62,73,63,55,117,57,61,55,79,138,88,81,110,80,79,89,66,65,78,81,87,73,76,69,78,83,83,91,99,73,80,101,52,66,92,88,61,63,62,112,92,103,93,82", "endOffsets": "228,315,419,541,622,687,782,863,926,1015,1084,1147,1221,1285,1341,1459,1517,1579,1635,1715,1854,1943,2025,2136,2217,2297,2387,2454,2520,2599,2681,2769,2843,2920,2990,3069,3153,3237,3329,3429,3503,3584,3686,3739,3806,3899,3988,4050,4114,4177,4290,4383,4487,4581,4664"}, "to": {"startLines": "19,51,59,60,61,90,142,152,157,159,160,161,162,163,164,165,166,167,168,169,170,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "708,3687,4506,4610,4732,8032,11901,12722,13128,13260,13349,13418,13481,13555,13619,13675,13793,13851,13913,13969,14049,14418,14507,14589,14700,14781,14861,14951,15018,15084,15163,15245,15333,15407,15484,15554,15633,15717,15801,15893,15993,16067,16148,16250,16303,16370,16463,16552,16614,16678,16741,16854,16947,17051,17324", "endLines": "22,51,59,60,61,90,142,152,157,159,160,161,162,163,164,165,166,167,168,169,170,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,209", "endColumns": "12,86,103,121,80,64,94,80,62,88,68,62,73,63,55,117,57,61,55,79,138,88,81,110,80,79,89,66,65,78,81,87,73,76,69,78,83,83,91,99,73,80,101,52,66,92,88,61,63,62,112,92,103,93,82", "endOffsets": "881,3769,4605,4727,4808,8092,11991,12798,13186,13344,13413,13476,13550,13614,13670,13788,13846,13908,13964,14044,14183,14502,14584,14695,14776,14856,14946,15013,15079,15158,15240,15328,15402,15479,15549,15628,15712,15796,15888,15988,16062,16143,16245,16298,16365,16458,16547,16609,16673,16736,16849,16942,17046,17140,17402"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2d12819c5c8831c86b34929884541b49\\transformed\\play-services-basement-18.3.0\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6110", "endColumns": "159", "endOffsets": "6265"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,208,283,353,436,505,572,652,735,822,917,989,1080,1164,1240,1323,1405,1480,1559,1634,1724,1797,1880,1956", "endColumns": "69,82,74,69,82,68,66,79,82,86,94,71,90,83,75,82,81,74,78,74,89,72,82,75,86", "endOffsets": "120,203,278,348,431,500,567,647,730,817,912,984,1075,1159,1235,1318,1400,1475,1554,1629,1719,1792,1875,1951,2038"}, "to": {"startLines": "50,64,146,153,154,158,171,172,173,213,214,217,218,221,222,223,225,226,228,230,231,233,236,238,239", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3617,4991,12317,12803,12873,13191,14188,14255,14335,17587,17674,17946,18018,18269,18353,18429,18588,18670,18825,18980,19055,19246,19471,19675,19751", "endColumns": "69,82,74,69,82,68,66,79,82,86,94,71,90,83,75,82,81,74,78,74,89,72,82,75,86", "endOffsets": "3682,5069,12387,12868,12951,13255,14250,14330,14413,17669,17764,18013,18104,18348,18424,18507,18665,18740,18899,19050,19140,19314,19549,19746,19833"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4d414adee14b24510c264c21c70961f0\\transformed\\appcompat-1.7.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,219", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "886,988,1101,1209,1294,1395,1523,1609,1690,1782,1876,1973,2067,2167,2261,2357,2453,2545,2637,2719,2826,2937,3036,3144,3252,3359,3518,18109", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "983,1096,1204,1289,1390,1518,1604,1685,1777,1871,1968,2062,2162,2256,2352,2448,2540,2632,2714,2821,2932,3031,3139,3247,3354,3513,3612,18187"}}]}]}