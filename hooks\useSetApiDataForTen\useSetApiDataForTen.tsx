import { AxiosHeaders, AxiosRequestHeaders } from "axios";
import { axiosInstance } from "@/api/axios";

const useSetApiDataForTen = <D extends Object>({
  endpoint,
  headers,
}: {
  endpoint: string;
  headers: AxiosHeaders;
}) => {
  const SetFunction = async (datas?: D) => {
    console.log(datas);
    try {
      const responce = await axiosInstance
        .post(`${endpoint}`, datas, {
          headers: {
            "X-Powered-By": "Express",
            "Access-Control-Allow-Origin": "*",
            "Content-Type": "multipart/form-data",
          },
        })
        .then((val) => {
          console.log(val.data);
          return val.data;
        });
      console.log(responce);
      return responce;
    } catch (error) {
      console.log(error);
      throw error;
    }
  };

  return { SetFunction };
};

export default useSetApiDataForTen;
