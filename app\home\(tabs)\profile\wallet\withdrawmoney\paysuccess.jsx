import { View, Text, Modal, Image, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import { LinearGradient } from "expo-linear-gradient";
import success from "@/assets/PaymentImg/Success.png";
import { router } from "expo-router";

const paysuccess = () => {
  const [modle, setmodle] = useState(true);
  return (
    <View className="relative flex-1">
      <LinearGradient
        colors={["#000", "#000"]}
        start={[0, 0]}
        style={{
          position: "absolute",
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
          zIndex: 1,
          flex: 1,
          opacity: 0.8,
        }}
      />
      <Modal transparent={true} visible={modle}>
        <View className="flex-1 items-center justify-center">
          <View className="w-[310px] h-[294px] bg-[#fff] rounded-[20px] justify-between">
            <View className="items-center mt-8">
              <View>
                <Image source={success} />
              </View>
              <View className="mt-2">
                <Text className="font-[600] text-[18px] leading-[24px] text-[#0E0E0E]">
                  Successful withdrawal
                </Text>
              </View>
              <View className="mt-2 px-5">
                <Text className="font-[400] text-center text-[16px] leading-[20px] text-[#616161]">
                  You will receive the money in your account within 2-5 business
                  days
                </Text>
              </View>
            </View>
            <View className="items-center mb-4 px-5">
              <TouchableOpacity
                onPress={() => {
                  router.push("home/profile/wallet");
                  setmodle(false);
                }}
                className="h-[48px] bg-[#00660A] w-full rounded-[8px] items-center justify-center"
              >
                <Text className="font-[500] text-[16px] text-[#FFFFFF] leading-[24px]">
                  Go Back
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default paysuccess;
