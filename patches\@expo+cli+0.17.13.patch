diff --git a/node_modules/@expo/cli/build/src/start/server/metro/externals.js b/node_modules/@expo/cli/build/src/start/server/metro/externals.js
index 1234567..7654321 100644
--- a/node_modules/@expo/cli/build/src/start/server/metro/externals.js
+++ b/node_modules/@expo/cli/build/src/start/server/metro/externals.js
@@ -94,7 +94,14 @@ async function tapNodeShims(projectRoot, nodeModulesPath) {
     for (const [name, contents] of Object.entries(nodeShims)) {
         const shimPath = path.join(nodeExternalsPath, name);
         try {
-            await fs.mkdir(path.dirname(shimPath), { recursive: true });
+            // Fix for Windows: replace colons in directory names
+            let dirPath = path.dirname(shimPath);
+            if (process.platform === 'win32') {
+                // Create a Windows-compatible path by replacing colons with underscores
+                dirPath = dirPath.replace(/:/g, '_');
+                shimPath = path.join(dirPath, path.basename(shimPath));
+            }
+            await fs.mkdir(dirPath, { recursive: true });
             await fs.writeFile(shimPath, contents);
         }
         catch (error) {
