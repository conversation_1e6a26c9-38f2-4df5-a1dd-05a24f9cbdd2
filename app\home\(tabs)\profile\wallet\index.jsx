import { View, Text, TouchableOpacity, Dimensions } from "react-native";
import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import Rsicon from "@/assets/Hometab/rs.svg";

import ClockIcon from "@/assets/icons/clockIcon.svg";
import BillIcon from "@/assets/icons/billicon.svg";
import RightArrow from "@/assets/icons/rightArrow.svg";
import AlartIcon from "@/assets/icons/alartIcon.svg";
import { router } from "expo-router";
import useGetApiData from "../../../../../hooks/useGetApiData";

const wallet = () => {
  const [ShowAlart, setShowAlart] = useState(false);
  const [Balance, setBalance] = useState(120000.0);
  const { data, isLoading, triggerreFresh } = useGetApiData({
    endpoint: "wallet_balance",
  });
  return (
    <SafeAreaView>
      <View
        className="bg-[#FFFFFF] "
        style={{
          height: Dimensions.get("window").height,
        }}
      >
        {/* Title Area */}
        <View className="items-center justify-center mt-10">
          <Text className="font-[400] text-[26px] leading-[39px]">Wallet</Text>
        </View>

        {/* Amount Details Area */}
        <View className="items-center justify-center mt-6 mb-8">
          <Text>Balance Available</Text>
          <View className="mt-1 flex-row items-center">
            <View>
              <Rsicon width={16} height={25} />
            </View>
            {isLoading ? (
              <></>
            ) : ( 
              <>
                <Text className="font-[400] text-[24px] leading-[36px]">
                  {data?.data}
                </Text>
              </>
            )}
          </View>
        </View>

        {/* Button Area */}
        <View className="flex-row justify-center items-center gap-[8px]">
          <TouchableOpacity
            onPress={() => {
              router.push({
                pathname: "home/profile/wallet/withdrawmoney",
                params: { amount: data?.data },
              });
            }}
            className="w-[162px] h-[44px] items-center justify-center border-[1.5px] border-[#00660A] rounded-[4px]"
          >
            <Text className="font-[400] text-[16px] leading-[24px] text-[#00660A]">
              Withdraw Money
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              router.push("home/profile/wallet/addmoney");
            }}
            className="w-[162px] h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
          >
            <Text className="font-[400] text-[16px] leading-[24px] text-[#fff]">
              Add money
            </Text>
          </TouchableOpacity>
        </View>

        <View className="px-5">
          {/* Alart Area */}
          {ShowAlart ? (
            <View className="w-full mt-12 bg-[#FFE9E9]">
              <View className="flex-row p-3 gap-[18px]">
                <AlartIcon />
                <View className="flex-1">
                  <Text className="font-[500] text-[16px]">
                    Account has been Suspended
                  </Text>
                  <Text className="font-[500] text-[12px] leading-[18px]">
                    Your account is suspended due to an overdue balance. Settle
                    it to start receiving orders again.
                  </Text>
                </View>
              </View>
            </View>
          ) : (
            <></>
          )}
          {/* Transaction History and Billing Details Area */}
          {/* <View className="mt-20">
            <TouchableOpacity
              onPress={() => {
                router.push("/home/<USER>");
              }}
            >
              <View className="flex-row items-center justify-between mb-8">
                <View className="flex-row items-center gap-3">
                  <ClockIcon />
                  <Text className="font-[400] text-[14px]">
                    See Transaction History
                  </Text>
                </View>
                <RightArrow />
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                router.push("home/billingdetails");
              }}
            >
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center gap-3">
                  <BillIcon fill={"#00660A"} />
                  <Text className="font-[400] text-[14px]">
                    See Billing Details
                  </Text>
                </View>
                <RightArrow />
              </View>
            </TouchableOpacity>
          </View> */}
        </View>
      </View>
    </SafeAreaView>
  );
};

export default wallet;
