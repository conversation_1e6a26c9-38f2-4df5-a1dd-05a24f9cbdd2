/**
 * This script runs the Windows path fix and then starts the Expo server
 */

// First, run our Windows path fix
require("./fix-windows-paths");

// Then start the Expo server using the child_process module
const { spawn } = require("child_process");
const path = require("path");
const fs = require("fs");

console.log("Starting Expo server...");

// Get the command-line arguments (excluding the first two: node and script path)
const args = process.argv.slice(2);

// Set CI environment variable to make Expo run in non-interactive mode
process.env.CI = "1";

// Default port if not specified
if (!args.includes("--port")) {
  args.push("--port", "8005");
}

// Add clear flag to clear terminal
if (!args.includes("--clear")) {
  args.push("--clear");
}

console.log(`Running command: npx expo start ${args.join(" ")}`);

// Spawn the Expo CLI process
const expo = spawn("npx", ["expo", "start", ...args], {
  stdio: "inherit", // Pipe all stdio to the parent process
  shell: true, // Use shell on Windows
});

// Handle process exit
expo.on("close", (code) => {
  console.log(`Expo server exited with code ${code}`);
  process.exit(code);
});

// Handle errors
expo.on("error", (err) => {
  console.error("Failed to start Expo server:", err);
  process.exit(1);
});
