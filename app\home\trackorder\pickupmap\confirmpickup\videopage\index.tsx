import * as FileSystem from "expo-file-system";
import React, { useCallback, useRef, useState } from "react";
import { useLocalSearchParams } from "expo-router";
import { router } from "expo-router";

import {
  CameraCapturedPicture,
  CameraType,
  CameraView,
  useCameraPermissions,
} from "expo-camera";

import {
  ActivityIndicator,
  Alert,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

const VideoCaptureScreen = () => {
  const { id, invoice_no, orderNumber, from } = useLocalSearchParams();
  const [isRecording, setIsRecording] = useState(false);
  const [type, setType] = useState<CameraType>("front");
  const [isCameraReady, setIsCameraReady] = useState(false);
  const [permission, requestPermission] = useCameraPermissions();
  const cameraRef = useRef<CameraView>(null);

  const onCameraReady = useCallback(() => {
    console.log("Camera is ready");
    setIsCameraReady(true);
  }, []);

  const handleRecordVideo = async () => {
    if (!isCameraReady) {
      Alert.alert("Error", "Please wait for camera to initialize");
      return;
    }

    try {
      if (isRecording) {
        setIsRecording(false);

        // Take a picture instead of recording video
        if (cameraRef.current) {
          const photo = await cameraRef.current.takePictureAsync();

          if (photo && photo.uri) {
            // Ensure the photo file exists
            const fileInfo = await FileSystem.getInfoAsync(photo.uri);
            console.log("Photo file info:", fileInfo);

            if (!fileInfo.exists) {
              throw new Error("Photo file not found");
            }

            // Copy to a permanent location
            const newPath = `${
              FileSystem.documentDirectory
            }photo_${Date.now()}.jpg`;
            await FileSystem.copyAsync({
              from: photo.uri,
              to: newPath,
            });

            console.log("Photo saved to:", newPath);

            // Navigate to the next screen
            router.replace({
              pathname: "/home/<USER>/pickupmap/confirmpickup/camara/otp",
              params: {
                id: id,
                invoice_no: invoice_no,
                orderNumber: orderNumber,
                video: newPath, // Using photo path instead of video
                from: from,
              },
            });
          }
        }
      } else {
        setIsRecording(true);

        // Show a message that we're "recording" (simulating video recording)
        Alert.alert("Taking Photo", "Press 'Stop' to capture the photo", [
          { text: "OK" },
        ]);
      }
    } catch (error: any) {
      console.error("Photo capture error:", error);
      Alert.alert(
        "Error",
        `Could not capture photo: ${error?.message || "Unknown error"}`
      );
      setIsRecording(false);
    }
  };

  if (!permission) {
    // Camera permissions are still loading
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text>Initializing camera...</Text>
      </View>
    );
  }

  if (!permission.granted) {
    // Camera permissions are not granted yet
    return (
      <View style={styles.centerContainer}>
        <Text>We need your permission to show the camera</Text>
        <TouchableOpacity style={styles.button} onPress={requestPermission}>
          <Text>Grant permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <CameraView
        ref={cameraRef}
        style={styles.camera}
        facing={type}
        onCameraReady={onCameraReady}
        onMountError={(error: any) => {
          console.error("Camera mount error:", error);
          Alert.alert("Error", "Failed to initialize camera");
        }}
      >
        <View style={styles.controls}>
          <TouchableOpacity
            style={styles.button}
            onPress={() => setType(type === "back" ? "front" : "back")}
          >
            <Text style={styles.text}>Flip</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.recordButton,
              { backgroundColor: isRecording ? "red" : "white" },
              !isCameraReady && styles.disabledButton,
            ]}
            onPress={handleRecordVideo}
            disabled={!isCameraReady}
          >
            <Text style={styles.text}>{isRecording ? "Stop" : "Record"}</Text>
          </TouchableOpacity>
        </View>
      </CameraView>
    </View>
  );
};

export default VideoCaptureScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  camera: {
    flex: 1,
  },
  controls: {
    flex: 1,
    flexDirection: "row",
    backgroundColor: "transparent",
    justifyContent: "space-around",
    alignItems: "flex-end",
    marginBottom: 20,
  },
  button: {
    backgroundColor: "#fff",
    padding: 10,
    borderRadius: 5,
  },
  recordButton: {
    padding: 20,
    borderRadius: 30,
  },
  disabledButton: {
    opacity: 0.5,
  },
  text: {
    fontSize: 16,
    color: "#000",
  },
});
