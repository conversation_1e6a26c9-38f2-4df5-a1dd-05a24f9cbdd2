import BackArrow from "../../../../assets/icons/BackArrow.svg";
import BikeIcon from "../../../../assets/Vehicle/Bike.svg";
import Checkbox from "expo-checkbox";
import CycleIcon from "../../../../assets/Vehicle/Cycle.svg";
import React, { useEffect, useState } from "react";
import TruckIcon from "../../../../assets/Vehicle/Truck.svg";
import useSetApiData from "../../../../hooks/useSetApiData";
import useTenStackMutate from "@/hooks/useTenStackMutate/useTenStackMutate";
import { router } from "expo-router";
import { Alert, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const index = () => {
  const [active, setactive] = useState<string[]>([]);

  const { mutate, isLoading } = useTenStackMutate({
    invalidateQueriesKey: ["delivery_person_vehicle"],
    endpoint: "auth/delivery_person_vehicle",
  });

  const Submit = () => {
    const vehicleData = {
      is_bike_available: active.includes("Bike") ? 1 : 2,
      is_cycle_available: active.includes("Cycle") ? 1 : 2,
      is_truck_available: active.includes("Truck") ? 1 : 2,
    };

    console.log("Submitting vehicle data:", vehicleData);

    mutate(vehicleData, {
      onSuccess: (response) => {
        console.log("Vehicle update response:", response);
        if (response?.msg === "Please enter userId!") {
          Alert.alert("Error", "Please enter userId!");
          return;
        }
        if (active.includes("Bike") || active.includes("Truck")) {
          router.push("/auth/createprofile/worksetting/addDetails");
        } else {
          router.push({
            pathname: "/auth/createprofile/worksettingcompleted/completed",
          });
        }
      },
      onError: (error) => {
        console.error("Vehicle update error:", error);
        Alert.alert("Error", "Failed to update vehicle details");
      },
    });
  };
  return (
    <SafeAreaView className="justify-between flex-1">
      <View className="px-5">
        <View className="flex-row relative items-center justify-start mt-4">
          <TouchableOpacity
            onPress={() => {
              router.back();
            }}
            className="absolute"
          >
            <View>
              <BackArrow />
            </View>
          </TouchableOpacity>
          <View className="flex-1 items-center justify-center">
            <Text className="font-[400] text-[19px] leading-[39px]">
              Select your Vehicle
            </Text>
          </View>
        </View>
        <View className="mt-4">
          <VehicleFieldComponent
            vehicleName={"Bike"}
            icon={<BikeIcon />}
            active={active}
            setactive={setactive}
            count={"Bike"}
          />
        </View>
        <View className="mt-4">
          <VehicleFieldComponent
            vehicleName={"Cycle"}
            icon={<CycleIcon />}
            active={active}
            setactive={setactive}
            count={"Cycle"}
          />
        </View>
        <View className="mt-4">
          <VehicleFieldComponent
            vehicleName={"Truck"}
            icon={<TruckIcon />}
            active={active}
            setactive={setactive}
            count={"Truck"}
          />
        </View>
      </View>
      <TouchableOpacity
        onPress={() => {
          Submit();
        }}
        className="m-4 h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
      >
        <Text className="font-[400] text-[16px] leading-[24px] text-[#fff] font-Pop">
          Continue
        </Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const VehicleFieldComponent = ({
  vehicleName,
  icon,
  active,
  count,
  setactive,
}: {
  vehicleName: string;
  icon: any;
  active: string[];
  count: string;
  setactive: React.Dispatch<React.SetStateAction<string[]>>;
}) => {
  const [checkBox, setcheckBox] = useState(false);
  useEffect(() => {
    setcheckBox(active.includes(count));
  }, [active, count]);
  return (
    <>
      <TouchableOpacity
        onPress={() => {
          setcheckBox(!checkBox);
          if (!checkBox) {
            if (count === "Truck") {
              setactive([]);
            } else if (count === "Bike" || count === "Cycle") {
              setactive((state) => {
                return state.filter((items) => {
                  return items !== "Truck";
                });
              });
            }
            setactive((state) => {
              return [...state, count];
            });
          } else {
            setactive((state) => {
              return [...state].filter((items) => {
                return count != items;
              });
            });
          }
        }}
        className="mt-4 flex-row items-center justify-between p-5 border-[1px] border-[#E9EAE9] rounded-[8px]"
      >
        <View className=" flex-row items-start space-x-3">
          <Checkbox
            value={checkBox}
            onValueChange={() => {
              setcheckBox(!checkBox);
              if (!checkBox) {
                setactive((state) => {
                  return [...state, count];
                });
              } else {
                setactive((state) => {
                  return [...state].filter((items) => {
                    return count != items;
                  });
                });
              }
            }}
          />
          <View className="space-y-2 items-start">
            <Text className="font-[400] text-[14px] leading-[21px] font-Pop">
              {vehicleName}
            </Text>
            <Text className="font-[400] text-[12px] leading-[18px] font-Pop text-[#627164]">
              Deliver by {vehicleName}
            </Text>
          </View>
        </View>
        <View className="">{icon}</View>
      </TouchableOpacity>
    </>
  );
};

export default index;
