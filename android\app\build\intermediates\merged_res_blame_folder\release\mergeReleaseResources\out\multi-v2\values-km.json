{"logs": [{"outputFile": "com.mohandhass2.myapp-mergeReleaseResources-83:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\00636ad238812e00d92433007e026a91\\transformed\\exoplayer-core-2.18.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,185,251,320,393,464,562,657", "endColumns": "70,58,65,68,72,70,97,94,68", "endOffsets": "121,180,246,315,388,459,557,652,721"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9605,9676,9735,9801,9870,9943,10014,10112,10207", "endColumns": "70,58,65,68,72,70,97,94,68", "endOffsets": "9671,9730,9796,9865,9938,10009,10107,10202,10271"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eeb7a35c1d2dda21edaace2253c1f099\\transformed\\exoplayer-ui-2.18.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,470,654,731,807,886,970,1058,1139,1205,1292,1381,1446,1508,1570,1636,1763,1886,2009,2081,2166,2237,2320,2402,2483,2545,2611,2664,2722,2772,2833,2892,2960,3030,3099,3166,3225,3291,3356,3423,3478,3535,3612,3689", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,76,75,78,83,87,80,65,86,88,64,61,61,65,126,122,122,71,84,70,82,81,80,61,65,52,57,49,60,58,67,69,68,66,58,65,64,66,54,56,76,76,55", "endOffsets": "280,465,649,726,802,881,965,1053,1134,1200,1287,1376,1441,1503,1565,1631,1758,1881,2004,2076,2161,2232,2315,2397,2478,2540,2606,2659,2717,2767,2828,2887,2955,3025,3094,3161,3220,3286,3351,3418,3473,3530,3607,3684,3740"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,520,7648,7725,7801,7880,7964,8052,8133,8199,8286,8375,8440,8502,8564,8630,8757,8880,9003,9075,9160,9231,9314,9396,9477,9539,10276,10329,10387,10437,10498,10557,10625,10695,10764,10831,10890,10956,11021,11088,11143,11200,11277,11354", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,76,75,78,83,87,80,65,86,88,64,61,61,65,126,122,122,71,84,70,82,81,80,61,65,52,57,49,60,58,67,69,68,66,58,65,64,66,54,56,76,76,55", "endOffsets": "330,515,699,7720,7796,7875,7959,8047,8128,8194,8281,8370,8435,8497,8559,8625,8752,8875,8998,9070,9155,9226,9309,9391,9472,9534,9600,10324,10382,10432,10493,10552,10620,10690,10759,10826,10885,10951,11016,11083,11138,11195,11272,11349,11405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a39c8a492fe916611935c0d3835604e6\\transformed\\material-1.6.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,304,404,516,596,661,755,825,887,974,1039,1098,1163,1224,1281,1400,1458,1519,1576,1647,1777,1863,1941,2049,2124,2195,2292,2359,2425,2505,2595,2681,2760,2837,2907,2982,3070,3140,3240,3339,3413,3489,3596,3650,3723,3814,3910,3972,4036,4099,4198,4296,4388,4488", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,99,111,79,64,93,69,61,86,64,58,64,60,56,118,57,60,56,70,129,85,77,107,74,70,96,66,65,79,89,85,78,76,69,74,87,69,99,98,73,75,106,53,72,90,95,61,63,62,98,97,91,99,82", "endOffsets": "219,299,399,511,591,656,750,820,882,969,1034,1093,1158,1219,1276,1395,1453,1514,1571,1642,1772,1858,1936,2044,2119,2190,2287,2354,2420,2500,2590,2676,2755,2832,2902,2977,3065,3135,3235,3334,3408,3484,3591,3645,3718,3809,3905,3967,4031,4094,4193,4291,4383,4483,4566"}, "to": {"startLines": "19,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "704,3634,4435,4535,4647,7583,11410,11906,12297,12425,12512,12577,12636,12701,12762,12819,12938,12996,13057,13114,13185,13537,13623,13701,13809,13884,13955,14052,14119,14185,14265,14355,14441,14520,14597,14667,14742,14830,14900,15000,15099,15173,15249,15356,15410,15483,15574,15670,15732,15796,15859,15958,16056,16148,16420", "endLines": "22,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,201", "endColumns": "12,79,99,111,79,64,93,69,61,86,64,58,64,60,56,118,57,60,56,70,129,85,77,107,74,70,96,66,65,79,89,85,78,76,69,74,87,69,99,98,73,75,106,53,72,90,95,61,63,62,98,97,91,99,82", "endOffsets": "868,3709,4530,4642,4722,7643,11499,11971,12354,12507,12572,12631,12696,12757,12814,12933,12991,13052,13109,13180,13310,13618,13696,13804,13879,13950,14047,14114,14180,14260,14350,14436,14515,14592,14662,14737,14825,14895,14995,15094,15168,15244,15351,15405,15478,15569,15665,15727,15791,15854,15953,16051,16143,16243,16498"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,211,283,353,437,503,572,648,725,808,888,959,1036,1118,1195,1278,1360,1436,1507,1577,1670,1749,1823,1902", "endColumns": "72,82,71,69,83,65,68,75,76,82,79,70,76,81,76,82,81,75,70,69,92,78,73,78,76", "endOffsets": "123,206,278,348,432,498,567,643,720,803,883,954,1031,1113,1190,1273,1355,1431,1502,1572,1665,1744,1818,1897,1974"}, "to": {"startLines": "50,64,143,145,146,150,163,164,165,202,203,206,207,210,211,212,214,215,217,219,220,222,225,227,228", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3561,4892,11834,11976,12046,12359,13315,13384,13460,16503,16586,16831,16902,17139,17221,17298,17455,17537,17685,17827,17897,18091,18320,18512,18591", "endColumns": "72,82,71,69,83,65,68,75,76,82,79,70,76,81,76,82,81,75,70,69,92,78,73,78,76", "endOffsets": "3629,4970,11901,12041,12125,12420,13379,13455,13532,16581,16661,16897,16974,17216,17293,17376,17532,17608,17751,17892,17985,18165,18389,18586,18663"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e9aa3803274a6cf3eaa3a68ef58f1ff5\\transformed\\ui-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,190,270,374,472,560,644,727,812,899,979,1064,1140,1214,1286,1357,1441,1507", "endColumns": "84,79,103,97,87,83,82,84,86,79,84,75,73,71,70,83,65,117", "endOffsets": "185,265,369,467,555,639,722,807,894,974,1059,1135,1209,1281,1352,1436,1502,1620"}, "to": {"startLines": "62,63,84,85,86,147,148,199,200,204,205,209,213,216,218,223,224,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4727,4812,7293,7397,7495,12130,12214,16248,16333,16666,16746,17063,17381,17613,17756,18170,18254,18394", "endColumns": "84,79,103,97,87,83,82,84,86,79,84,75,73,71,70,83,65,117", "endOffsets": "4807,4887,7392,7490,7578,12209,12292,16328,16415,16741,16826,17134,17450,17680,17822,18249,18315,18507"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f0ad3059e848ec2624bad4e9e6fc7d3\\transformed\\browser-1.6.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,152,249,382", "endColumns": "96,96,132,99", "endOffsets": "147,244,377,477"}, "to": {"startLines": "83,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7196,11504,11601,11734", "endColumns": "96,96,132,99", "endOffsets": "7288,11596,11729,11829"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7460a359c4c281b49833222722dfee74\\transformed\\play-services-wallet-18.1.3\\res\\values-km\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "72", "endOffsets": "274"}, "to": {"startLines": "231", "startColumns": "4", "startOffsets": "18851", "endColumns": "76", "endOffsets": "18923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4d414adee14b24510c264c21c70961f0\\transformed\\appcompat-1.7.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "873,975,1074,1184,1271,1374,1495,1573,1649,1740,1833,1925,2019,2119,2212,2307,2401,2492,2583,2666,2770,2874,2974,3083,3192,3301,3463,16979", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "970,1069,1179,1266,1369,1490,1568,1644,1735,1828,1920,2014,2114,2207,2302,2396,2487,2578,2661,2765,2869,2969,3078,3187,3296,3458,3556,17058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b84b30b80c5949bd0f3610f30992b047\\transformed\\play-services-base-18.3.0\\res\\values-km\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,442,559,660,818,938,1055,1160,1314,1427,1594,1715,1856,2010,2070,2124", "endColumns": "97,150,116,100,157,119,116,104,153,112,166,120,140,153,59,53,72", "endOffsets": "290,441,558,659,817,937,1054,1159,1313,1426,1593,1714,1855,2009,2069,2123,2196"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4975,5077,5232,5353,5458,5620,5744,5865,6123,6281,6398,6569,6694,6839,6997,7061,7119", "endColumns": "101,154,120,104,161,123,120,108,157,116,170,124,144,157,63,57,76", "endOffsets": "5072,5227,5348,5453,5615,5739,5860,5969,6276,6393,6564,6689,6834,6992,7056,7114,7191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b14b43fc1759186e6ca5ed92b9460e08\\transformed\\foundation-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,146", "endColumns": "90,91", "endOffsets": "141,233"}, "to": {"startLines": "229,230", "startColumns": "4,4", "startOffsets": "18668,18759", "endColumns": "90,91", "endOffsets": "18754,18846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6591935515ee6cd5ec7881ab1d3c64e\\transformed\\core-1.13.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "52,53,54,55,56,57,58,221", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3714,3809,3912,4010,4110,4211,4323,17990", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "3804,3907,4005,4105,4206,4318,4430,18086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2d12819c5c8831c86b34929884541b49\\transformed\\play-services-basement-18.3.0\\res\\values-km\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "144", "endOffsets": "339"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "5974", "endColumns": "148", "endOffsets": "6118"}}]}]}