export class DashboardModel implements DashboardEntity {
  status: number;
  weekly_earned: number;
  past_earned: number;
  distance_traveled: number;

  constructor({
    status,
    weekly_earned,
    past_earned,
    distance_traveled,
  }: {
    status: number;
    weekly_earned: number;
    past_earned: number;
    distance_traveled: number;
  }) {
    this.status = status;
    this.weekly_earned = weekly_earned;
    this.past_earned = past_earned;
    this.distance_traveled = distance_traveled;
  }
}
