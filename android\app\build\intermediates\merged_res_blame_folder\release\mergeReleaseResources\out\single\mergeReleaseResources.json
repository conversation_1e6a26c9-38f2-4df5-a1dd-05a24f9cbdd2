[{"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_node_modules_reactnavigation_elements_lib_module_assets_backiconmask.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/node_modules_reactnavigation_elements_lib_module_assets_backiconmask.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-xhdpi_splashscreen_logo.png.flat", "source": "com.mohandhass2.myapp-main-86:/drawable-xhdpi/splashscreen_logo.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-xxxhdpi_node_modules_reactnavigation_elements_lib_module_assets_closeicon.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-xxxhdpi/node_modules_reactnavigation_elements_lib_module_assets_closeicon.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_node_modules_reactnativeelementdropdown_src_assets_close.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/node_modules_reactnativeelementdropdown_src_assets_close.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-xxhdpi_node_modules_reactnavigation_elements_lib_module_assets_clearicon.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-xxhdpi/node_modules_reactnavigation_elements_lib_module_assets_clearicon.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-xhdpi_node_modules_reactnavigation_elements_lib_module_assets_closeicon.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-xhdpi/node_modules_reactnavigation_elements_lib_module_assets_closeicon.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-hdpi_splashscreen_logo.png.flat", "source": "com.mohandhass2.myapp-main-86:/drawable-hdpi/splashscreen_logo.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_assets_profileasser_sectionicon_refer.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/assets_profileasser_sectionicon_refer.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_node_modules_exporouter_assets_file.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/node_modules_exporouter_assets_file.png"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-xhdpi/ic_launcher_foreground.webp"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_node_modules_exporouter_assets_pkg.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/node_modules_exporouter_assets_pkg.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_node_modules_exporouter_assets_error.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/node_modules_exporouter_assets_error.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-xhdpi_node_modules_reactnavigation_elements_lib_module_assets_backicon.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-xhdpi/node_modules_reactnavigation_elements_lib_module_assets_backicon.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_assets_images_slidimage_frame1321317908.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/assets_images_slidimage_frame1321317908.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_node_modules_reactnavigation_elements_lib_module_assets_backicon.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/node_modules_reactnavigation_elements_lib_module_assets_backicon.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-xxhdpi_node_modules_reactnavigation_elements_lib_module_assets_searchicon.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-xxhdpi/node_modules_reactnavigation_elements_lib_module_assets_searchicon.png"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_splashscreen_logo.png.flat", "source": "com.mohandhass2.myapp-main-86:/drawable-mdpi/splashscreen_logo.png"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-xxxhdpi_node_modules_reactnavigation_elements_lib_module_assets_searchicon.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-xxxhdpi/node_modules_reactnavigation_elements_lib_module_assets_searchicon.png"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-xxhdpi_node_modules_reactnavigation_elements_lib_module_assets_closeicon.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-xxhdpi/node_modules_reactnavigation_elements_lib_module_assets_closeicon.png"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_assets_images_image.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/assets_images_image.png"}, {"merged": "com.mohandhass2.myapp-release-85:/raw_assets_font_poppinsregular.ttf.flat", "source": "com.mohandhass2.myapp-res-79:/raw/assets_font_poppinsregular.ttf"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable_ic_launcher_background.xml.flat", "source": "com.mohandhass2.myapp-main-86:/drawable/ic_launcher_background.xml"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_node_modules_reactnavigation_elements_lib_module_assets_closeicon.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/node_modules_reactnavigation_elements_lib_module_assets_closeicon.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-xxxhdpi_node_modules_reactnavigation_elements_lib_module_assets_backicon.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-xxxhdpi/node_modules_reactnavigation_elements_lib_module_assets_backicon.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_assets_hometab_qrimage.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/assets_hometab_qrimage.png"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-xxxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_node_modules_reactnativeelementdropdown_src_assets_down.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/node_modules_reactnativeelementdropdown_src_assets_down.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_node_modules_reactnavigation_elements_lib_module_assets_searchicon.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/node_modules_reactnavigation_elements_lib_module_assets_searchicon.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_assets_loading_ordalanetransparentlogo03.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/assets_loading_ordalanetransparentlogo03.png"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-xhdpi_node_modules_reactnavigation_elements_lib_module_assets_searchicon.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-xhdpi/node_modules_reactnavigation_elements_lib_module_assets_searchicon.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_node_modules_exporouter_assets_unmatched.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/node_modules_exporouter_assets_unmatched.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable_rn_edit_text_material.xml.flat", "source": "com.mohandhass2.myapp-main-86:/drawable/rn_edit_text_material.xml"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_assets_profile_bg.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/assets_profile_bg.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-xxhdpi_node_modules_reactnavigation_elements_lib_module_assets_backicon.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-xxhdpi/node_modules_reactnavigation_elements_lib_module_assets_backicon.png"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-hdpi/ic_launcher_foreground.webp"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_node_modules_reactnavigation_elements_lib_module_assets_clearicon.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/node_modules_reactnavigation_elements_lib_module_assets_clearicon.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_node_modules_exporouter_assets_forward.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/node_modules_exporouter_assets_forward.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_assets_images_slidimage_layer_2.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/assets_images_slidimage_layer_2.png"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-mdpi/ic_launcher_foreground.webp"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-xxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-xhdpi_node_modules_reactnavigation_elements_lib_module_assets_clearicon.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-xhdpi/node_modules_reactnavigation_elements_lib_module_assets_clearicon.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_assets_paymentimg_success.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/assets_paymentimg_success.png"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_assets_profile_calenderisocolor.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/assets_profile_calenderisocolor.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-xxxhdpi_node_modules_reactnavigation_elements_lib_module_assets_clearicon.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-xxxhdpi/node_modules_reactnavigation_elements_lib_module_assets_clearicon.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_assets_profile_contactlogo.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/assets_profile_contactlogo.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-mdpi_node_modules_exporouter_assets_sitemap.png.flat", "source": "com.mohandhass2.myapp-res-79:/drawable-mdpi/node_modules_exporouter_assets_sitemap.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-xxhdpi_splashscreen_logo.png.flat", "source": "com.mohandhass2.myapp-main-86:/drawable-xxhdpi/splashscreen_logo.png"}, {"merged": "com.mohandhass2.myapp-release-85:/drawable-xxxhdpi_splashscreen_logo.png.flat", "source": "com.mohandhass2.myapp-main-86:/drawable-xxxhdpi/splashscreen_logo.png"}, {"merged": "com.mohandhass2.myapp-release-85:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.mohandhass2.myapp-main-86:/mipmap-xhdpi/ic_launcher.webp"}]