import { View, Text, TouchableOpacity, Sc<PERSON>View } from "react-native";
import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import BackIcon from "@/assets/icons/BackArrow.svg";
import { SlideComponent } from ".";

const FAQs = () => {
  return (
    <SafeAreaView className="flex-1">
      <ScrollView>
        <View className="items-center justify-center relative mt-6">
          <TouchableOpacity
            className="absolute left-[3%]"
            onPress={() => {
              router.back();
            }}
          >
            <BackIcon />
          </TouchableOpacity>
          <View>
            <Text className="font-[600] text-[20px] leading-[30px]">Help</Text>
          </View>
        </View>
        <View className="mt-8">
          <SlideComponent
            text={
              "I entered the wrong CVV. Why did my transaction still go through ?"
            }
          />
        </View>
        <View className="">
          <SlideComponent text={"I want to provide feedback"} />
        </View>
        <View className="">
          <SlideComponent text={"Can I edit my order ?"} />
        </View>
        <View className="">
          <SlideComponent text={"I want to cancel my order"} />
        </View>
        <View className="">
          <SlideComponent
            text={"Will Ordalane be accountable for quality and quantity"}
          />
        </View>
        <View className="">
          <SlideComponent text={"Is there a minimum order value ?"} />
        </View>
        <View className="">
          <SlideComponent text={"Do you charge for delivery ?"} />
        </View>
        <View className="">
          <SlideComponent text={"How long do you take to deliver ?"} />
        </View>
        <View className="">
          <SlideComponent text={"What are your delivery hours ?"} />
        </View>
        <View className="">
          <SlideComponent text={"Can I order from any location ?"} />
        </View>
        <View className="">
          <SlideComponent text={"Do you support bulk orders ?"} />
        </View>
        <View className="">
          <SlideComponent text={"Can I order in advance ?"} />
        </View>
        <View className="">
          <SlideComponent text={"Can I change the address/numbers ?"} />
        </View>
        <View className="">
          <SlideComponent text={"Unable to view the details in my profile"} />
        </View>
        <View className="">
          <SlideComponent text={"Did not receive referral coupon"} />
        </View>
        <View className="">
          <SlideComponent text={"Deactivate my account"} />
        </View>
        <View className="">
          <SlideComponent text={"Unable to view the details in my profile"} />
        </View>
        <View className="">
          <SlideComponent text={"Do you accept Sodexo?"} />
        </View>
        <View className="">
          <SlideComponent text={"Unable to make payment using Sodexo card"} />
        </View>
        <View className="mb-4">
          <SlideComponent
            text={
              "My Ordalane money wallet has been misused by someone else, what do I do ?"
            }
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default FAQs;
