import AsyncStorage from "@react-native-async-storage/async-storage";
import CalendarIcon from "../../../../assets/profile/calendar.svg";
import DateTimePicker from "@react-native-community/datetimepicker";
import FileUploder from "../../../../component/FileUploder";
import React, { useContext, useEffect, useState } from "react";
import TextinputFile from "../../../../component/TextinputFile";
import useTenStackMutate from "@/hooks/useTenStackMutate/useTenStackMutate";
import { router } from "expo-router";
import { useForm } from "react-hook-form";
import { Alert } from "react-native";
import { Dropdown } from "react-native-element-dropdown";

import {
  View,
  Text,
  Button,
  TextInput,
  TouchableOpacity,
  ScrollView,
} from "react-native";

const index = () => {
  const {
    control,
    setValue,
    handleSubmit,
    formState: { isValid, errors },
  } = useForm();
  const [gendervalue, setgenderv] = useState("Male");
  const [gender, setgender] = useState("1");
  const [date, setdate] = useState(new Date());
  const [showpicker, setshowpicker] = useState(false);
  const [datevale, setdatevale] = useState("");

  const toggledatepicker = () => {
    setshowpicker(!showpicker);
  };

  const change = ({ type }, selectedDate) => {
    if (type == "set") {
      const currentdate = selectedDate;
      const currentdatestring = selectedDate.toDateString();
      const currentdatestringt = `
      ${new Date(selectedDate).getDate()}/${
        new Date(selectedDate).getMonth() + 1
      }/${new Date(selectedDate).getFullYear()}
      `.trim();
      setdate(currentdate);
      setdatevale(currentdatestringt);
      setshowpicker(false);
    } else {
      setshowpicker(false);
    }
  };

  const { mutate, isLoading } = useTenStackMutate({
    invalidateQueriesKey: ["profile"],
    endpoint: "auth/createProfile",
  });

  const DataFormat = (date = new Date()) => {
    let day = String(date.getDate()).padStart(2, "0");
    let month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero based
    let year = date.getFullYear();

    return `${year}-${month}-${day}`;
  };
  const Submit = () => {
    handleSubmit(async (data) => {
      try {
        if (!data?.productimage) {
          Alert.alert("Error", "Please upload a profile picture");
          return;
        }
        if (datevale === "" || date === null) {
          Alert.alert("Error", "Please select a date of birth");
          return;
        }
        if (gender === "") {
          Alert.alert("Error", "Please select a gender");
          return;
        }

        const formData = new FormData();

        // Add basic details
        formData.append("first_name", data?.firstname);
        formData.append("last_name", data?.lastname);
        formData.append("dob", DataFormat(new Date(date)));
        formData.append("gender", gender);

        // Add profile image
        formData.append("image", {
          uri: data?.productimage,
          type: data?.type,
          name: data?.productimage,
        } as any);

        console.log("Submitting form data:", formData);
        console.log("Submitting form data image:", formData.get("image"));

        mutate(formData, {
          onSuccess: (response) => {
            console.log("Profile creation success:", response);
            if (response?.msg === "Profile created successfully!") {
              router.push("/auth/createprofile/addprofile/page1");
            } else {
              Alert.alert(
                "Error",
                response?.message || "Failed to create profile"
              );
            }
          },
          onError: (error) => {
            console.error("Profile creation error:", error);
            Alert.alert("Error", "Failed to create profile. Please try again.");
          },
        });
      } catch (error) {
        console.error("Submit error:", error);
        Alert.alert("Error", "An unexpected error occurred");
      }
    })();
  };

  return (
    <View className="flex-1 ">
      <View className="">
        {/* First Name TextField Area */}
        <Text className="font-[500] text-[17px] text-[#4D4D4D] mt-4">
          Enter some basic details
        </Text>
        <View className="mt-4 items-start">
          <Text className="font-[400] text-[16px] text-[#4D4D4D]">
            First Name
          </Text>
          <TextinputFile
            rules={{
              required: {
                value: true,
                message: "First name is required",
              },
            }}
            placeholder={""}
            control={control}
            name={"firstname"}
            editable={true}
          />
          {errors.firstname && (
            <Text className="text-red-500 text-center">
              {(errors as any).firstname.message}
            </Text>
          )}
        </View>

        {/* Last Name TextField Area */}
        <View className="mt-4 items-start">
          <Text className="font-[400] text-[16px] text-[#4D4D4D]">
            Last Name
          </Text>
          <TextinputFile
            placeholder={""}
            control={control}
            name={"lastname"}
            rules={{
              required: {
                value: true,
                message: "Last name is required",
              },
            }}
          />
          {errors.lastname && (
            <Text className="text-red-500 text-center">
              {(errors as any).lastname.message}
            </Text>
          )}
        </View>
        {/* Gender TextField Area */}
        <View className="mt-4">
          <Text className="font-[400] text-[16px] text-[#4D4D4D]">Gender</Text>
          <DropDownComponent2
            data={[
              { label: "Male", value: "1" },
              { label: "Female", value: "2" },
              { label: "Other", value: "3" },
            ]}
            value={gender}
            setValue={setgender}
            set={setgenderv}
            style={{ maxWidth: "100%", marginRight: 10 }}
          />
        </View>
        <View className="mt-4" style={{}}>
          <Text>Date of Birth</Text>
          {showpicker && (
            <DateTimePicker
              value={date}
              mode="date"
              maximumDate={new Date(Date.now())}
              onChange={change}
            />
          )}
          <View className="rounded-[4px] px-2 flex-row justify-center items-center mt-2 border-[1px] h-[40px] border-[#ACB9D5]">
            <TouchableOpacity
              onPress={() => {
                toggledatepicker();
              }}
              className="flex-1"
            >
              <TextInput
                className="flex-1 text-[#000] font-Pop"
                editable={false}
                value={datevale}
              />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                toggledatepicker();
              }}
            >
              <CalendarIcon />
            </TouchableOpacity>
          </View>
        </View>
        <View className="mt-2">
          <FileUploder
            title={"Upload Profile Picture (Compulsory)"}
            text={"Browse Files"}
            setValue={setValue}
          />
        </View>
      </View>
      <View className="m-2 px-4 mt-24">
        <TouchableOpacity
          style={{
            opacity: isValid && !isLoading ? 1 : 0.7,
          }}
          disabled={isLoading}
          onPress={Submit}
          className="h-[44px] items-center justify-center bg-[#00660A]"
        >
          <Text className="font-[400] text-[#fff] leading-[24px] font-Pop">
            {isLoading ? "Creating Profile..." : "Continue"}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export const DropDownComponent2 = ({
  text,
  placeholder,
  style,
  data,
  defaul,
  value,
  setValue,
}) => {
  return (
    <View className="" style={style}>
      <Dropdown
        data={data}
        placeholder={placeholder}
        placeholderStyle={{
          color: "#B3B3B3",
        }}
        style={{ minHeight: 40 }}
        labelField="label"
        valueField="label"
        value={value}
        onChange={(item) => {
          setValue(item.label);
        }}
        className="border-[1px] border-[#ACB9D5] rounded-[4px] px-3 mt-2"
      />
    </View>
  );
};

export default index;
