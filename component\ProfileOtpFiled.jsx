import { View, Text, TextInput, TouchableOpacity } from "react-native";
import React from "react";
import { Controller } from "react-hook-form";

const ProfileOtpFiled = (props) => {
  return (
    <View className="h-[40px] mt-3 mb-3 flex-row items-center">
      <Controller
        control={props?.control}
        name={props?.name}
        rules={{
          required: {
            value: true,
          },
          minLength: 4,
          ...props?.rules,
        }}
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            value={value}
            onChangeText={(value) => onChange(value)}
            className="w-[40%] border-b-[1px] border-[#E9EAE9] font-[400] text-[16px] leading-[24px] pl-2"
            placeholder="Enter your OTP here"
          />
        )}
      />
      <>
        <TouchableOpacity
          onPress={() => {}}
          className="w-[124px] h-full bg-[#00660A] items-center justify-center rounded-[4px] ml-3"
        >
          <Text className="font-[400] text-[16px] text-[#FFFFFF]">Verify </Text>
        </TouchableOpacity>
      </>
    </View>
  );
};

export default ProfileOtpFiled;
