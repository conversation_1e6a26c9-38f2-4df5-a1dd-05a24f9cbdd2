{"name": "my-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start --port 8003", "start-clear": "expo start --clear --port 8003", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "android:clean": "cd android && .\\gradlew.bat clean", "build": "cd android && .\\gradlew.bat assembleRelease", "prebuild": "npx expo prebuild", "postinstall": "node ./scripts/fix-windows-paths.js", "fix-windows": "node ./scripts/fix-windows-paths.js"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-community/slider": "4.5.5", "@react-navigation/native": "^7.0.14", "@reduxjs/toolkit": "^2.6.0", "@rneui/themed": "^4.0.0-rc.8", "@shopify/react-native-skia": "1.5.0", "@tanstack/react-query": "^5.72.1", "@twotalltotems/react-native-otp-input": "^1.3.11", "axios": "^1.7.2", "base-64": "^1.0.0", "cli": "^1.0.1", "deprecated-react-native-prop-types": "^5.0.0", "expo": "^52.0.46", "expo-application": "~6.0.2", "expo-av": "~15.0.2", "expo-barcode-generator": "^3.0.2", "expo-calendar": "~14.0.6", "expo-camera": "~16.0.18", "expo-checkbox": "~4.0.1", "expo-constants": "~17.0.8", "expo-device": "~7.0.3", "expo-document-picker": "~13.0.3", "expo-font": "~13.0.4", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-location": "~18.0.10", "expo-media-library": "~17.0.6", "expo-notifications": "~0.29.14", "expo-router": "~4.0.17", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-storage": "^48.1.0", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "latest-version": "^9.0.0", "nativewind": "^2.0.11", "postinstall-postinstall": "^2.1.0", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.51.4", "react-native": "0.76.9", "react-native-element-dropdown": "^2.12.0", "react-native-event-listeners": "^1.0.7", "react-native-gesture-handler": "~2.20.2", "react-native-maps": "1.18.0", "react-native-otp-inputs": "^7.4.0", "react-native-quick-base64": "^2.1.2", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-razorpay": "^2.3.0", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-svg-transformer": "^1.3.0", "react-native-text-input-otp": "^1.0.7", "react-native-textarea": "^1.0.4", "react-native-web": "~0.19.6", "react-redux": "^9.2.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "svg-path-properties": "^1.3.0", "toggle-switch-react-native": "^3.3.0", "zustand": "^4.5.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.3.12", "jest": "^29.2.1", "jest-expo": "~52.0.6", "patch-package": "^8.0.0", "react-test-renderer": "18.2.0", "tailwindcss": "^3.3.2", "typescript": "^5.1.3"}, "private": true}