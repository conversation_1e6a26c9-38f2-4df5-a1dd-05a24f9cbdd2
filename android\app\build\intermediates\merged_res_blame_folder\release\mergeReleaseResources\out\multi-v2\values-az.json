{"logs": [{"outputFile": "com.mohandhass2.myapp-mergeReleaseResources-83:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,136,205,287,355,423,498", "endColumns": "80,68,81,67,67,74,74", "endOffsets": "131,200,282,350,418,493,568"}, "to": {"startLines": "63,143,144,148,161,204,206", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4870,11913,11982,12292,13254,16687,16835", "endColumns": "80,68,81,67,67,74,74", "endOffsets": "4946,11977,12059,12355,13317,16757,16905"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a39c8a492fe916611935c0d3835604e6\\transformed\\material-1.6.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,300,396,512,592,656,750,818,877,972,1036,1095,1162,1225,1279,1394,1452,1514,1568,1639,1771,1855,1935,2039,2115,2191,2275,2342,2408,2478,2556,2639,2709,2785,2863,2934,3020,3103,3196,3289,3362,3434,3528,3582,3649,3733,3821,3885,3950,4014,4116,4213,4309,4406", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,95,115,79,63,93,67,58,94,63,58,66,62,53,114,57,61,53,70,131,83,79,103,75,75,83,66,65,69,77,82,69,75,77,70,85,82,92,92,72,71,93,53,66,83,87,63,64,63,101,96,95,96,79", "endOffsets": "215,295,391,507,587,651,745,813,872,967,1031,1090,1157,1220,1274,1389,1447,1509,1563,1634,1766,1850,1930,2034,2110,2186,2270,2337,2403,2473,2551,2634,2704,2780,2858,2929,3015,3098,3191,3284,3357,3429,3523,3577,3644,3728,3816,3880,3945,4009,4111,4208,4304,4401,4481"}, "to": {"startLines": "19,50,58,59,60,86,138,142,147,149,150,151,152,153,154,155,156,157,158,159,160,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,197", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "710,3583,4390,4486,4602,7582,11436,11845,12233,12360,12455,12519,12578,12645,12708,12762,12877,12935,12997,13051,13122,13322,13406,13486,13590,13666,13742,13826,13893,13959,14029,14107,14190,14260,14336,14414,14485,14571,14654,14747,14840,14913,14985,15079,15133,15200,15284,15372,15436,15501,15565,15667,15764,15860,16131", "endLines": "22,50,58,59,60,86,138,142,147,149,150,151,152,153,154,155,156,157,158,159,160,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,197", "endColumns": "12,79,95,115,79,63,93,67,58,94,63,58,66,62,53,114,57,61,53,70,131,83,79,103,75,75,83,66,65,69,77,82,69,75,77,70,85,82,92,92,72,71,93,53,66,83,87,63,64,63,101,96,95,96,79", "endOffsets": "870,3658,4481,4597,4677,7641,11525,11908,12287,12450,12514,12573,12640,12703,12757,12872,12930,12992,13046,13117,13249,13401,13481,13585,13661,13737,13821,13888,13954,14024,14102,14185,14255,14331,14409,14480,14566,14649,14742,14835,14908,14980,15074,15128,15195,15279,15367,15431,15496,15560,15662,15759,15855,15952,16206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b84b30b80c5949bd0f3610f30992b047\\transformed\\play-services-base-18.3.0\\res\\values-az\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,449,578,684,826,955,1071,1173,1335,1441,1586,1719,1859,2011,2071,2132", "endColumns": "104,150,128,105,141,128,115,101,161,105,144,132,139,151,59,60,76", "endOffsets": "297,448,577,683,825,954,1070,1172,1334,1440,1585,1718,1858,2010,2070,2131,2208"}, "to": {"startLines": "64,65,66,67,68,69,70,71,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4951,5060,5215,5348,5458,5604,5737,5857,6125,6291,6401,6550,6687,6831,6987,7051,7116", "endColumns": "108,154,132,109,145,132,119,105,165,109,148,136,143,155,63,64,80", "endOffsets": "5055,5210,5343,5453,5599,5732,5852,5958,6286,6396,6545,6682,6826,6982,7046,7111,7192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7460a359c4c281b49833222722dfee74\\transformed\\play-services-wallet-18.1.3\\res\\values-az\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "70", "endOffsets": "272"}, "to": {"startLines": "213", "startColumns": "4", "startOffsets": "17458", "endColumns": "74", "endOffsets": "17528"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2d12819c5c8831c86b34929884541b49\\transformed\\play-services-basement-18.3.0\\res\\values-az\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "157", "endOffsets": "352"}, "to": {"startLines": "72", "startColumns": "4", "startOffsets": "5963", "endColumns": "161", "endOffsets": "6120"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4d414adee14b24510c264c21c70961f0\\transformed\\appcompat-1.7.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,2892"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "875,985,1086,1196,1284,1391,1505,1587,1665,1756,1849,1943,2042,2142,2235,2330,2424,2515,2607,2692,2797,2903,3003,3112,3217,3319,3477,16381", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "980,1081,1191,1279,1386,1500,1582,1660,1751,1844,1938,2037,2137,2230,2325,2419,2510,2602,2687,2792,2898,2998,3107,3212,3314,3472,3578,16460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6591935515ee6cd5ec7881ab1d3c64e\\transformed\\core-1.13.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "51,52,53,54,55,56,57,207", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3663,3764,3866,3969,4073,4174,4279,16910", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "3759,3861,3964,4068,4169,4274,4385,17006"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eeb7a35c1d2dda21edaace2253c1f099\\transformed\\exoplayer-ui-2.18.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,476,660,748,837,914,1006,1094,1170,1234,1325,1416,1481,1546,1608,1676,1804,1936,2062,2133,2214,2284,2360,2456,2553,2622,2688,2741,2799,2847,2908,2972,3044,3103,3166,3229,3289,3355,3419,3485,3537,3595,3667,3739", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,88,76,91,87,75,63,90,90,64,64,61,67,127,131,125,70,80,69,75,95,96,68,65,52,57,47,60,63,71,58,62,62,59,65,63,65,51,57,71,71,53", "endOffsets": "280,471,655,743,832,909,1001,1089,1165,1229,1320,1411,1476,1541,1603,1671,1799,1931,2057,2128,2209,2279,2355,2451,2548,2617,2683,2736,2794,2842,2903,2967,3039,3098,3161,3224,3284,3350,3414,3480,3532,3590,3662,3734,3788"}, "to": {"startLines": "2,11,15,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,526,7646,7734,7823,7900,7992,8080,8156,8220,8311,8402,8467,8532,8594,8662,8790,8922,9048,9119,9200,9270,9346,9442,9539,9608,10331,10384,10442,10490,10551,10615,10687,10746,10809,10872,10932,10998,11062,11128,11180,11238,11310,11382", "endLines": "10,14,18,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137", "endColumns": "17,12,12,87,88,76,91,87,75,63,90,90,64,64,61,67,127,131,125,70,80,69,75,95,96,68,65,52,57,47,60,63,71,58,62,62,59,65,63,65,51,57,71,71,53", "endOffsets": "330,521,705,7729,7818,7895,7987,8075,8151,8215,8306,8397,8462,8527,8589,8657,8785,8917,9043,9114,9195,9265,9341,9437,9534,9603,9669,10379,10437,10485,10546,10610,10682,10741,10804,10867,10927,10993,11057,11123,11175,11233,11305,11377,11431"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b14b43fc1759186e6ca5ed92b9460e08\\transformed\\foundation-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,93", "endOffsets": "139,233"}, "to": {"startLines": "211,212", "startColumns": "4,4", "startOffsets": "17275,17364", "endColumns": "88,93", "endOffsets": "17359,17453"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\00636ad238812e00d92433007e026a91\\transformed\\exoplayer-core-2.18.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,186,249,314,392,459,548,641", "endColumns": "69,60,62,64,77,66,88,92,70", "endOffsets": "120,181,244,309,387,454,543,636,707"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9674,9744,9805,9868,9933,10011,10078,10167,10260", "endColumns": "69,60,62,64,77,66,88,92,70", "endOffsets": "9739,9800,9863,9928,10006,10073,10162,10255,10326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e9aa3803274a6cf3eaa3a68ef58f1ff5\\transformed\\ui-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,293,390,491,582,663,751,843,925,1006,1095,1167,1241,1317,1390,1471,1537", "endColumns": "99,87,96,100,90,80,87,91,81,80,88,71,73,75,72,80,65,116", "endOffsets": "200,288,385,486,577,658,746,838,920,1001,1090,1162,1236,1312,1385,1466,1532,1649"}, "to": {"startLines": "61,62,83,84,85,145,146,195,196,198,199,201,202,203,205,208,209,210", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4682,4782,7293,7390,7491,12064,12145,15957,16049,16211,16292,16465,16537,16611,16762,17011,17092,17158", "endColumns": "99,87,96,100,90,80,87,91,81,80,88,71,73,75,72,80,65,116", "endOffsets": "4777,4865,7385,7486,7577,12140,12228,16044,16126,16287,16376,16532,16606,16682,16830,17087,17153,17270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f0ad3059e848ec2624bad4e9e6fc7d3\\transformed\\browser-1.6.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,255,363", "endColumns": "95,103,107,102", "endOffsets": "146,250,358,461"}, "to": {"startLines": "82,139,140,141", "startColumns": "4,4,4,4", "startOffsets": "7197,11530,11634,11742", "endColumns": "95,103,107,102", "endOffsets": "7288,11629,11737,11840"}}]}]}