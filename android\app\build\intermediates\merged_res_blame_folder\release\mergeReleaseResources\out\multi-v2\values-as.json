{"logs": [{"outputFile": "com.mohandhass2.myapp-mergeReleaseResources-83:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e9aa3803274a6cf3eaa3a68ef58f1ff5\\transformed\\ui-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,378,476,563,660,759,848,938,1021,1106,1185,1260,1335,1409,1484,1550", "endColumns": "94,84,92,97,86,96,98,88,89,82,84,78,74,74,73,74,65,117", "endOffsets": "195,280,373,471,558,655,754,843,933,1016,1101,1180,1255,1330,1404,1479,1545,1663"}, "to": {"startLines": "44,45,65,66,67,74,75,122,123,125,126,128,129,130,131,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4151,4246,6561,6654,6752,7378,7475,11114,11203,11374,11457,11625,11704,11779,11854,12029,12104,12170", "endColumns": "94,84,92,97,86,96,98,88,89,82,84,78,74,74,73,74,65,117", "endOffsets": "4241,4326,6649,6747,6834,7470,7569,11198,11288,11452,11537,11699,11774,11849,11923,12099,12165,12283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b84b30b80c5949bd0f3610f30992b047\\transformed\\play-services-base-18.3.0\\res\\values-as\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,446,565,671,797,915,1024,1132,1271,1376,1522,1643,1772,1921,1977,2039", "endColumns": "103,148,118,105,125,117,108,107,138,104,145,120,128,148,55,61,81", "endOffsets": "296,445,564,670,796,914,1023,1131,1270,1375,1521,1642,1771,1920,1976,2038,2120"}, "to": {"startLines": "46,47,48,49,50,51,52,53,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4331,4439,4592,4715,4825,4955,5077,5190,5428,5571,5680,5830,5955,6088,6241,6301,6367", "endColumns": "107,152,122,109,129,121,112,111,142,108,149,124,132,152,59,65,85", "endOffsets": "4434,4587,4710,4820,4950,5072,5185,5297,5566,5675,5825,5950,6083,6236,6296,6362,6448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b14b43fc1759186e6ca5ed92b9460e08\\transformed\\foundation-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,87", "endOffsets": "135,223"}, "to": {"startLines": "136,137", "startColumns": "4,4", "startOffsets": "12288,12373", "endColumns": "84,87", "endOffsets": "12368,12456"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4d414adee14b24510c264c21c70961f0\\transformed\\appcompat-1.7.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,615,735,812,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1910,2023,2131,2234,2343,2459,2579,2746,2848", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,610,730,807,882,973,1066,1161,1255,1355,1448,1543,1637,1728,1819,1905,2018,2126,2229,2338,2454,2574,2741,2843,2926"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,385,484,591,682,787,907,984,1059,1150,1243,1338,1432,1532,1625,1720,1814,1905,1996,2082,2195,2303,2406,2515,2631,2751,2918,11542", "endColumns": "107,98,106,90,104,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "380,479,586,677,782,902,979,1054,1145,1238,1333,1427,1527,1620,1715,1809,1900,1991,2077,2190,2298,2401,2510,2626,2746,2913,3015,11620"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6591935515ee6cd5ec7881ab1d3c64e\\transformed\\core-1.13.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "34,35,36,37,38,39,40,132", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3097,3198,3301,3409,3514,3618,3718,11928", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "3193,3296,3404,3509,3613,3713,3842,12024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2d12819c5c8831c86b34929884541b49\\transformed\\play-services-basement-18.3.0\\res\\values-as\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "121", "endOffsets": "316"}, "to": {"startLines": "54", "startColumns": "4", "startOffsets": "5302", "endColumns": "125", "endOffsets": "5423"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a39c8a492fe916611935c0d3835604e6\\transformed\\material-1.6.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,304,406,529,608,673,762,827,886,972,1036,1099,1169,1233,1287,1392,1450,1512,1566,1638,1755,1842,1925,2035,2112,2193,2284,2351,2417,2487,2564,2651,2722,2799,2868,2937,3028,3100,3189,3278,3352,3424,3510,3560,3626,3706,3790,3852,3916,3979,4079,4176,4268,4367", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,101,122,78,64,88,64,58,85,63,62,69,63,53,104,57,61,53,71,116,86,82,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,80", "endOffsets": "222,299,401,524,603,668,757,822,881,967,1031,1094,1164,1228,1282,1387,1445,1507,1561,1633,1750,1837,1920,2030,2107,2188,2279,2346,2412,2482,2559,2646,2717,2794,2863,2932,3023,3095,3184,3273,3347,3419,3505,3555,3621,3701,3785,3847,3911,3974,4074,4171,4263,4362,4443"}, "to": {"startLines": "2,33,41,42,43,68,69,73,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3020,3847,3949,4072,6839,6904,7313,7574,7633,7719,7783,7846,7916,7980,8034,8139,8197,8259,8313,8385,8502,8589,8672,8782,8859,8940,9031,9098,9164,9234,9311,9398,9469,9546,9615,9684,9775,9847,9936,10025,10099,10171,10257,10307,10373,10453,10537,10599,10663,10726,10826,10923,11015,11293", "endLines": "5,33,41,42,43,68,69,73,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,124", "endColumns": "12,76,101,122,78,64,88,64,58,85,63,62,69,63,53,104,57,61,53,71,116,86,82,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,80", "endOffsets": "272,3092,3944,4067,4146,6899,6988,7373,7628,7714,7778,7841,7911,7975,8029,8134,8192,8254,8308,8380,8497,8584,8667,8777,8854,8935,9026,9093,9159,9229,9306,9393,9464,9541,9610,9679,9770,9842,9931,10020,10094,10166,10252,10302,10368,10448,10532,10594,10658,10721,10821,10918,11010,11109,11369"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f0ad3059e848ec2624bad4e9e6fc7d3\\transformed\\browser-1.6.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,269,377", "endColumns": "107,105,107,105", "endOffsets": "158,264,372,478"}, "to": {"startLines": "64,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6453,6993,7099,7207", "endColumns": "107,105,107,105", "endOffsets": "6556,7094,7202,7308"}}]}]}