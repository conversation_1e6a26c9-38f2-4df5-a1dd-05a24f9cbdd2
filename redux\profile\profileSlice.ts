import { createSlice } from "@reduxjs/toolkit";
import { loadProfile } from "./asyncReducers";
import { reducers } from "./reducers";
import { UserModel } from "@/data/model/user_model/user_model";

export interface ProfileState {
  profile: UserModel;
}

const initialState: ProfileState = {
  profile: new UserModel({
    id: 0,
    name: "",
    email: "",
    phone: "",
    image: "",
    dob: "",
    gender: "",
    is_phone_verified: 0,
    is_email_verified: 0,
    date_of_joinning: "",
    is_online: 0,
  }),
};

const ProfileSlice = createSlice({
  name: "profile",
  initialState: initialState,
  reducers: {
    ...reducers,
  },
  extraReducers: (builder) => {
    builder.addCase(loadProfile.fulfilled, (state, action) => {
      state.profile = action.payload;
    });
  },
});

export const {} = ProfileSlice.actions;
export default ProfileSlice.reducer;
