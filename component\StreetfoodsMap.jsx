import {
  View,
  Image,
  StyleSheet,
  TouchableOpacity,
  Text,
  TextInput,
  ScrollView,
} from "react-native";
import React, { useEffect, useRef, useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { LinearGradient } from "expo-linear-gradient";
import { Dropdown } from "react-native-element-dropdown";
import Loactionicon from "../assets/Icon/Vector.svg";
import SideMenu from "../assets/Icon/sidemenuIcon.svg";
import {
  useListOrMap2,
  useSellerDataStreetFoods,
  useuserLocation,
} from "../store";
import BannerImg from "../assets/Img/bannerIma.png";
import MapView, { Marker } from "react-native-maps";
import * as Location from "expo-location";
import SearchIcon from "../assets/Icon/search.svg";
import MicIcon from "../assets/Icon/mic.svg";
import CardComponent from "./CardComponent";
import { router } from "expo-router";

const SellerMap = () => {
  const [value, setValue] = useState("1");
  const { ShowMapList, setShowMap } = useListOrMap2((state) => state);
  const { userLocation, userCity } = useuserLocation((state) => state);

  return (
    <SafeAreaView
      style={{
        flex: 1,
      }}
    >
      {/* Banner Area  */}
      <View className="flex-1 relative py-5">
        <LinearGradient
          colors={["#00660A", "#002E05"]}
          // colors={["#00660A", "#fff"]}
          start={[1, 0]}
          end={[0, 1.9]}
          locations={[0, 1]}
          style={{
            position: "absolute",
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            zIndex: 1,
            flex: 1,
          }}
        />
        <View className="mt-2 z-20 flex-row relative items-center justify-center">
          <Image
            source={BannerImg}
            className="w-full z-20"
            style={{ objectFit: "contain" }}
          />
          <TouchableOpacity
            className="right-3 top-[-5] absolute h-[40px] w-[40px] items-center justify-center bg-[#039e15] rounded-full z-50"
            style={{
              alignSelf: "flex-start",
            }}
            onPress={() => {
              router.push("home/sidemenu");
            }}
          >
            <SideMenu fill={"#fff"} />
          </TouchableOpacity>
        </View>
        {/* Location Area */}
        <View className="z-20 px-4 mt-6 flex-row items-center justify-between">
          <View>
            <View className="flex-row items-center space-x-2">
              <TouchableOpacity
                className="flex-row items-center space-x-2"
                onPress={() => {
                  router.push("home/mapscreen");
                }}
              >
                <Loactionicon />
                <Text className="font-[500] text-[16px] text-[#fff]">
                  {userCity}
                </Text>
              </TouchableOpacity>
            </View>
            <View className="mt-2">
              <Text className="text-[#fff]">{userLocation}</Text>
            </View>
          </View>
        </View>
      </View>
      {/* Map Area  */}
      <View className={`w-full ${ShowMapList ? "flex-1" : "h-[30vh]"}`}>
        <MapAndSideBar />
      </View>
    </SafeAreaView>
  );
};

export const MapAndSideBar = () => {
  const [region, setRegion] = useState(null);
  const mapRef = useRef(null);
  const [PinCoords, setPinCoord] = useState(null);

  const requestLocationPermission = async () => {
    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Permission denied",
        "Allow location access to use this feature."
      );
      return;
    }

    let location = await Location.getCurrentPositionAsync({});
    const { latitude, longitude } = location.coords;
    const newRegion = {
      latitude,
      longitude,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    };
    setRegion(newRegion);
    if (mapRef.current) {
      mapRef.current.animateToRegion(newRegion, 0);
    }
  };

  const { ShowMapList, setShowMap } = useListOrMap2((state) => state);
  const { SellerData } = useSellerDataStreetFoods((state) => state);
  const [Markerregion, setRegionMarker] = useState(null);
  const { logdata } = useuserLocation((state) => state);

  useEffect(() => {
    const { latitude, longitude } = logdata;
    const newRegion = {
      latitude,
      longitude,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    };
    setRegionMarker(newRegion);
    setRegion(newRegion);
  }, [logdata]);
  useEffect(() => {
    requestLocationPermission();
  }, []);

  return (
    <View className="flex-1 relative">
      <View
        className="flex-row items-center justify-between px-2 mx-4 bg-[#FFF] border-[1px] border-[#E9EAE9] mt-5 rounded-[4px] absolute"
        style={{
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.36,
          shadowRadius: 4,
          elevation: 5,
          zIndex: 40,
          top: ShowMapList ? -10 : 0,
          position: ShowMapList ? "relative" : "absolute",
        }}
      >
        <View className="flex-row items-center space-x-2">
          <SearchIcon stroke={"#627164"} />
          <TextInput
            onFocus={() => {
              router.push("home/search");
            }}
            placeholder="Search here"
            className="flex-1 max-w-[80%]"
          />
        </View>
        <TouchableOpacity>
          <MicIcon />
        </TouchableOpacity>
      </View>
      {ShowMapList ? (
        <MapListComponent />
      ) : (
        <>
          <MapView
            style={StyleSheet.absoluteFill}
            region={region}
            provider="google"
            ref={mapRef}
            showsUserLocation
            zoomEnabled={true}
            onRegionChange={(re) => {
              setPinCoord(re);
            }}
          >
            <Marker coordinate={Markerregion} pinColor="#C6FACC" />
            {SellerData.map((item, index) => {
              return (
                <Marker
                  onPress={() => {
                    router.push({
                      pathname: `home/cart/${item?.id}`,
                      params: {
                        name: item?.resname,
                        reating: item?.reating,
                        ...item,
                      },
                    });
                  }}
                  coordinate={item.location}
                  key={index}
                >
                  {item.icon}
                </Marker>
              );
            })}
          </MapView>
        </>
      )}
      <TouchableOpacity
        onPress={() => {
          setShowMap();
        }}
        style={{
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.36,
          shadowRadius: 4,
          elevation: 5,
          zIndex: 40,
        }}
        className="absolute bottom-2 right-[7%] z-50 w-[106px] h-[32px] rounded-[12px] bg-[#FFF] items-center justify-center"
      >
        <Text className="font-[500] text-[16px] text-[#00660A]">
          {ShowMapList ? "Show Map" : "Show List"}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export const MapListComponent = () => {
  const { SellerData } = useSellerDataStreetFoods((state) => state);
  return (
    <>
      <View>
        {SellerData.map(
          (
            {
              id,
              image,
              resname,
              reating,
              time,
              descripition,
              km,
              amount,
              address,
            },
            index
          ) => {
            return (
              <View key={id}>
                <CardComponent
                  id={`${id}${index}`}
                  CardImage={image}
                  resname={resname}
                  reating={reating}
                  time={time}
                  descripition={descripition}
                  km={km}
                  amount={amount}
                  address={address}
                />
              </View>
            );
          }
        )}
      </View>
    </>
  );
};

export default SellerMap;
