import { View, Text, TouchableOpacity, Image, ScrollView } from "react-native";
import React, { useState } from "react";
import BackArrow from "@/assets/icons/BackArrow.svg";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import StarIcon from "../../../../../assets/Hometab/GreenStar.svg";
import useGetApiData from "../../../../../hooks/useGetApiData";
import { MonthlyRatingComponent, WeeklyRatingComponent } from "./_layout";

const index = () => {
  const [active, setactive] = useState("Weekly Ratings");
  const { data, isLoading, triggerreFresh } = useGetApiData({
    endpoint: "auth/rating",
  });
  return (
    <SafeAreaView>
      <View className="px-4">
        {/* Title Area */}
        <View className="flex-row relative items-center justify-start mt-9">
          <TouchableOpacity
            onPress={() => {
              router.back();
            }}
            className="absolute"
          >
            <View>
              <BackArrow />
            </View>
          </TouchableOpacity>
          <View className="flex-1 items-center justify-center">
            <Text className="font-[400] text-[20px] leading-[39px]">
              Your Rating
            </Text>
          </View>
        </View>
        {!isLoading ? (
          <>
            <View className="p-5 border-[1px] border-[#E9EAE9] items-center justify-center mt-10 rounded-[4ox]">
              <View className="">
                <Text className="">Overall Rating</Text>
              </View>
              <View className="">
                <Text className="mt-2 font-[600] text-[32px] leading-[48px] font-Pop">
                  {data?.data?.avg_rating}
                </Text>
              </View>
              <View className="flex-row space-x-2">
                <StarIcon
                  fill={Number(data?.data?.avg_rating) > 0 ? "#00660A" : "#fff"}
                />
                <StarIcon
                  fill={Number(data?.data?.avg_rating) > 1 ? "#00660A" : "#fff"}
                />
                <StarIcon
                  fill={Number(data?.data?.avg_rating) > 2 ? "#00660A" : "#fff"}
                />
                <StarIcon
                  fill={Number(data?.data?.avg_rating) > 3 ? "#00660A" : "#fff"}
                />
                <StarIcon
                  fill={Number(data?.data?.avg_rating) > 4 ? "#00660A" : "#fff"}
                />
              </View>
            </View>
          </>
        ) : (
          <></>
        )}
      </View>
      <View className="flex-row mt-10">
        <View className="flex-1 items-center justify-start px-4 relative">
          <TouchableOpacity
            onPress={() => {
              setactive("Weekly Ratings");
            }}
          >
            <Text className="font-[400] text-[16px] leading-[24px] font-Pop ">
              Weekly Ratings
            </Text>
          </TouchableOpacity>
          {active === "Weekly Ratings" && (
            <>
              <View className="bg-[#00660A] h-[5px] w-full my-4" />
            </>
          )}
        </View>
        <View className="flex-1 items-center justify-start px-4">
          <TouchableOpacity
            onPress={() => {
              setactive("Monthly Ratings");
            }}
          >
            <Text className="font-[400] text-[16px] leading-[24px] font-Pop ">
              Monthly Ratings
            </Text>
          </TouchableOpacity>
          {active === "Monthly Ratings" && (
            <>
              <View className="bg-[#00660A] h-[5px] w-full my-4" />
            </>
          )}
        </View>
      </View>
      <ScrollView className="">
        {isLoading ? (
          <></>
        ) : (
          <>
            <View className="px-4 flex-1">
              {active === "Weekly Ratings" && (
                <>
                  {data?.data?.weekly.map((data, index) => {
                    return (
                      <View key={index}>
                        <WeeklyRatingComponent data={data} />
                      </View>
                    );
                  })}
                </>
              )}
              {active === "Monthly Ratings" && (
                <>
                  {data?.data?.monthly.map((data, index) => {
                    return (
                      <View key={index}>
                        <MonthlyRatingComponent data={data} />
                      </View>
                    );
                  })}
                </>
              )}
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default index;
