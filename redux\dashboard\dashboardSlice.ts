import { createSlice } from "@reduxjs/toolkit";
import { loadDashboard } from "./asyncReducers";
import { reducers } from "./reducers";
import { DashboardModel } from "@/data/model/dashboard_model/dashboard_model";

export interface DashboardState {
  dashboard: DashboardModel;
}

const initialState: DashboardState = {
  dashboard: new DashboardModel({
    status: 0,
    weekly_earned: 0,
    past_earned: 0,
    distance_traveled: 0,
  }),
};

const DashboardSlice = createSlice({
  name: "dashboard",
  initialState: initialState,
  reducers: {
    ...reducers,
  },
  extraReducers: (builder) => {
    builder.addCase(loadDashboard.fulfilled, (state, action) => {
      state.dashboard = action.payload;
    });
  },
});

export const {} = DashboardSlice.actions;
export default DashboardSlice.reducer;
