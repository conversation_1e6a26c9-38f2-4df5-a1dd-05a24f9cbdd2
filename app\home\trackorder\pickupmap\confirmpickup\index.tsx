import BackArrow from "@/assets/icons/BackArrow.svg";
import PopUpComponent from "../../../../../component/PopUpModle/PopUpComponent";
import React, { useState } from "react";
import RsIcon from "@/assets/icons/RsIcon.svg";
import useTenStackHook from "@/hooks/TenStackHook/TenStackHook";
import { router, useLocalSearchParams } from "expo-router";
import { Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const index = () => {
  const [modle, setmodle] = useState(false);
  const { id, shop_name, orderNumber, invoice_no } = useLocalSearchParams();
  const { data, isLoading } = useTenStackHook<
    { invoice_no: string | string[] },
    any
  >({
    endpoint: "order/invoice_details",
    params: {
      invoice_no: invoice_no,
    },
    canSave: true,
    key: "invoice_details",
  });
  return (
    <SafeAreaView className="flex-1 justify-between">
      <View className="px-4 mt-4">
        <View className="flex-row relative items-center justify-start mt-4">
          <TouchableOpacity
            onPress={() => {
              router.back();
            }}
            className="absolute"
          >
            <View>
              <BackArrow />
            </View>
          </TouchableOpacity>
          <View className="flex-1 items-center justify-center">
            <Text className="font-[400] text-[19px] leading-[39px]">
              Pickup Location
            </Text>
            <Text className="">Order ID: {orderNumber}</Text>
          </View>
        </View>
      </View>
      {isLoading ? (
        <></>
      ) : (
        <>
          <View className="mt-4">
            <PickUpItems data={data} shop_name={shop_name} />
          </View>
        </>
      )}
      <PopUpComponent
        modle={modle}
        setmodle={setmodle}
        text={"pickUp"}
        onpressfun={() => {
          router.push({
            // pathname: "home/trackorder/pickupmap/confirmpickup/camara",
            pathname: "/home/<USER>/pickupmap/confirmpickup/videopage",
            params: {
              id: id,
              invoice_no: invoice_no,
              orderNumber: orderNumber,
              from: "pickup",
            },
          });
        }}
      />
      <View className="px-4 my-4">
        <TouchableOpacity
          onPress={() => {
            setmodle(true);
          }}
          className="h-[44px] items-center justify-center bg-[#00660A] rounded-[4px] "
        >
          <Text className="font-[400] font-Pop leading-[24px] text-[16px] text-[#fff]">
            Pickup Complete
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export const PickUpItems = ({ data, shop_name }) => {
  console.log(JSON.stringify(data, null, 2));
  return (
    <>
      <View className="px-4 my-4">
        <View className="flex-row items-center justify-between">
          <Text className="">Bill Details</Text>
          <View className="flex-row items-center space-x-2">
            <RsIcon />
            <Text className="">{data?.info?.order_amnt}</Text>
          </View>
        </View>

        <View className="mt-6 ">
          <Text className="">{shop_name}</Text>
        </View>
        {/* <View className="flex-row items-center justify-between">
          <View className="flex-row items-center space-x-4 mt-4">
            <VegIcon />
            <Text className="">French Vanilla x 1</Text>
          </View>
          <View className="flex-row items-center space-x-2">
            <RsIcon />
            <Text className="">20</Text>
          </View>
        </View>
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center space-x-4 mt-4">
            <VegIcon />
            <Text className="">Chocolate Waffles x 1</Text>
          </View>
          <View className="flex-row items-center space-x-2">
            <RsIcon />
            <Text className="">60</Text>
          </View>
        </View>
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center space-x-4 mt-4">
            <VegIcon />
            <Text className="">Vanilla pinenut x 1</Text>
          </View>
          <View className="flex-row items-center space-x-2">
            <RsIcon />
            <Text className="">40</Text>
          </View>
        </View> */}
      </View>
    </>
  );
};

export default index;
