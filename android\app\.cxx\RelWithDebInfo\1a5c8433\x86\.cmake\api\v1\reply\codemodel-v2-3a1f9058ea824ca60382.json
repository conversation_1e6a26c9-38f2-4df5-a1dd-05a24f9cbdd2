{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8], "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-RelWithDebInfo-7a3e52a2da6e6e8ce736.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "RNDateTimePickerCGen_autolinked_build", "jsonFile": "directory-RNDateTimePickerCGen_autolinked_build-RelWithDebInfo-9a459ddfe2e1214203ad.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "RNCSlider_autolinked_build", "jsonFile": "directory-RNCSlider_autolinked_build-RelWithDebInfo-a09313b85873b40dfdfe.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni", "targetIndexes": [1]}, {"build": "rnskia_autolinked_build", "jsonFile": "directory-rnskia_autolinked_build-RelWithDebInfo-f7980a8102badebfb321.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni", "targetIndexes": [6]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-RelWithDebInfo-f7a93162b07b7dc97cc5.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-RelWithDebInfo-e7303d48fae91d22c2c8.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [8]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-RelWithDebInfo-047f7df80b50d1b7ca8a.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [5]}, {"build": "rnsvg_autolinked_build", "jsonFile": "directory-rnsvg_autolinked_build-RelWithDebInfo-d021d353df082cd8394d.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni", "targetIndexes": [7]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-RelWithDebInfo-5d4604e86ee25d77699a.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_RNCSlider::@4898bc4726ecf1751b6a", "jsonFile": "target-react_codegen_RNCSlider-RelWithDebInfo-fecef304330a897e04f1.json", "name": "react_codegen_RNCSlider", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2", "jsonFile": "target-react_codegen_RNDateTimePickerCGen-RelWithDebInfo-dab5e3dfa9fce137c578.json", "name": "react_codegen_RNDateTimePickerCGen", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-RelWithDebInfo-448755ea135110894063.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-RelWithDebInfo-0390eb452c5e78ac6851.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-RelWithDebInfo-b7fef8c52c53cef9c16c.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_rnskia::@376d8504f62839611b97", "jsonFile": "target-react_codegen_rnskia-RelWithDebInfo-10deec448d0032260bde.json", "name": "react_codegen_rnskia", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "jsonFile": "target-react_codegen_rnsvg-RelWithDebInfo-3e9a590a54602a007f4f.json", "name": "react_codegen_rnsvg", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-RelWithDebInfo-5b580d5d996ea4adbf78.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86", "source": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}