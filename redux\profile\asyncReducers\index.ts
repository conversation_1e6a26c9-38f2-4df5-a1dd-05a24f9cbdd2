import { createAsyncThunk } from "@reduxjs/toolkit";
import { axiosInstance } from "@/api/axios";
import { UserRepositoryimpl } from "@/data/repository/user_impl";
import { ApiImpl } from "@/data/source";
import { UserUseCase } from "@/domain/usecase/userusecase";

const profileusecase = new UserUseCase(
  new UserRepositoryimpl(new ApiImpl(axiosInstance))
);

export const loadProfile = createAsyncThunk(
  "profile/loadProfile",
  async (_, { dispatch }) => {
    try {
      const data = await profileusecase.getUserData();
      return data;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
);
