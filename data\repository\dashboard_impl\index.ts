import { Api } from "@/data/source/network/api";
import { DashboardRepository } from "@/domain/repository/dashboard";

export class DashboardRepositoryimpl implements DashboardRepository {
  api: Api;
  constructor(api: Api) {
    this.api = api;
  }
  async getDashboardData(): Promise<DashboardEntity> {
    try {
      const data = await this.api.gatData<DashboardEntity>("my_dashboard");
      if (data.status) {
        return data.data;
      }
      throw new Error("Dashboard data status is false");
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
