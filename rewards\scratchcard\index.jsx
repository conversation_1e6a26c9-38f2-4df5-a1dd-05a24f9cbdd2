import {
  View,
  Text,
  Dimensions,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import React, { useEffect, useState } from "react";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { SafeAreaView } from "react-native-safe-area-context";
import ScratchCardComponent from "../../../../../component/ScratchCardComponent/ScratchCardComponent";
import BackIcon from "@/assets/icons/BackArrow.svg";
import { router } from "expo-router";

const windowInitial = Dimensions.get("window");

const index = () => {
  const [dimensions, setDimensions] = useState({ window: windowInitial });

  useEffect(() => {
    const subscription = Dimensions.addEventListener("change", ({ window }) => {
      setDimensions({ window });
    });
    return () => subscription?.remove();
  });
  return (
    <SafeAreaView>
      <View className="items-center justify-center relative mt-8">
        <TouchableOpacity
          className="absolute left-[3%]"
          onPress={() => {
            router.back();
          }}
        >
          <BackIcon />
        </TouchableOpacity>
      </View>
      <GestureHandlerRootView>
        <View
          style={[
            styles.root,
            {
              width: dimensions.window.width,
              height: dimensions.window.height,
            },
          ]}
        >
          <View className="items-center justify-center my-14">
            <Text className="mb-4 font-600 font-Pop text-[30px] leading-[45px] ">
              Hooray!
            </Text>
            <Text className="">You have won a scratch card</Text>
          </View>
          <ScratchCardComponent />
          <View className="items-center justify-center my-14">
            <Text className="mb-4 font-[500] font-Pop text-[18px] leading-[27px] text-[#00660A]">Scratch the card to reveal your reward </Text>
            <Text className="">
              Reward for completing 5 delivery orders on Ordalane
            </Text>
          </View>
        </View>
      </GestureHandlerRootView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  root: {
    justifyContent: "flex-start",
    alignItems: "center",
    marginTop: 120,
  },
});

export default index;
