import { View, Text, TouchableOpacity } from "react-native";
import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import BackIcon from "@/assets/icons/BackArrow.svg";
import RightArrow from "../../../../../assets/ProfileAsser/SectionIcon/UserprofileAssert/RightArrow.svg";

const index = () => {
  return (
    <SafeAreaView className="flex-1">
      <View className="items-center justify-center relative mt-6">
        <TouchableOpacity
          className="absolute left-[3%]"
          onPress={() => {
            router.back();
          }}
        >
          <BackIcon />
        </TouchableOpacity>
        <View>
          <Text className="font-[600] text-[20px] leading-[30px]">Help</Text>
        </View>
      </View>
      <View>
        <SlideComponent text={"Legal Terms & Conditions"} />
      </View>
      <View className="mt-8">
        <SlideComponent
          text={"Issues with previous orders"}
          pressfun={() => {}}
        />
      </View>
      <View>
        <SlideComponent text={"General Issues"} pressfun={() => {}} />
      </View>
      <View>
        <SlideComponent text={"FAQs"} pressfun={() => {}} />
      </View>
    </SafeAreaView>
  );
};

export const SlideComponent = ({ text, pressfun }) => {
  return (
    <View className="h-[53px] justify-center border-b-[1px] border-[#E9EAE9]">
      <TouchableOpacity
        className="flex-row items-center px-4 justify-between"
        onPress={pressfun}
      >
        <Text className="max-w-[300px] font-[500] text-[14px] text-[#001A03] leading-[21px]">
          {text}
        </Text>
        <RightArrow />
      </TouchableOpacity>
    </View>
  );
};

export default index;
