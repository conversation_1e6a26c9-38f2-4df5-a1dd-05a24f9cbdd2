import { DeliveryAddressEntity } from "./delivery_address_entity";
import { PickupAddressEntity } from "./pickup_address_entity";

export interface OrderListEntity {
  id: number;
  order_number: string;
  order_status: string;
  invoice_no: string;
  customer_id: number;
  product_mode: number;
  delivery_address_id: number;
  pickup_address: PickupAddressEntity[];
  delivery_address: DeliveryAddressEntity;
  edt: number;
  deliver_partner_id: string;
  delivery_fees: string;
  order_otp: number;
  platform_fees: string;
  cash_handling_charges: string;
  order_amnt: string;
  coupon_code: string;
  coupon_discounted_amnt: string;
  gst_total: string;
  status_id: number;
  order_status_id: number;
  created_at: string;
}
