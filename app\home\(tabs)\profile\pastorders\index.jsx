import React, { forwardRef, useEffect, useRef, useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import BackArrow from "@/assets/icons/BackArrow.svg";
import RsIcon from "@/assets/Hometab/rs.svg";
import ScooterIcon from "@/assets/Hometab/ph_scooter-bold.svg";
import ClockIcon from "@/assets/Hometab/zondicons_time.svg";
import ArrowIcon from "@/assets/Hometab/tabler_arrow-right.svg";
import LocationMarkIcon1 from "@/assets/Hometab/drop_location.svg";
import LocationMarkIcon2 from "@/assets/Hometab/gridicons_location.svg";
import { ScrollView } from "react-native";
import RBSheet from "react-native-raw-bottom-sheet";
import useGetApiData from "../../../../../hooks/useGetApiData";
import { OrderDetailsBottomSheet } from "../../../../../component/OrdersReceiveComponent/OrdersReceiveComponent";

const index = () => {
  const { data, isLoading, triggerreFresh } = useGetApiData({
    endpoint: "order/past_order_list",
  });
  return (
    <SafeAreaView>
      {isLoading ? (
        <></>
      ) : (
        <>
          <ScrollView>
            <View className="px-4 mt-3">
              <View className="flex-row relative items-center justify-start mt-4">
                <TouchableOpacity
                  onPress={() => {
                    router.back();
                  }}
                  className="absolute"
                >
                  <View>
                    <BackArrow />
                  </View>
                </TouchableOpacity>
                <View className="flex-1 items-center justify-center">
                  <Text className="font-[400] text-[19px] leading-[39px]">
                    Past Orders
                  </Text>
                </View>
              </View>
            </View>

            {isLoading ? (
              <></>
            ) : (
              <>
                {data?.data?.map((pastOrders) => {
                  console.log(pastOrders);
                  return (
                    <View className="px-4 mt-5" key={pastOrders?.id}>
                      <PastOrderComponent
                        pastOrders={pastOrders}
                        key={pastOrders?.id}
                      />
                    </View>
                  );
                })}
              </>
            )}
          </ScrollView>
        </>
      )}
    </SafeAreaView>
  );
};

const PastOrderComponent = (orders) => {
  const OrderDetailsBottomSheetRef = useRef();
  const [C_Date, setDate] = useState("");

  useEffect(() => {
    setDate(
      String(orders?.pastOrders?.delivery_related_data?.created_at).split(" ")
    );
  }, []);

  console.log(orders)

  return (
    <>
      <View className="p-4 rounded-[8px] border-[1px] border-[#E9EAE9]">
        <View className="flex-row mt-4 justify-between">
          <View className="items-center justify-center">
            <View className="">
              <Text className="font-[400] text-[12px] leading-[16px] font-Pop text-[#001A03]">
                Order Amount
              </Text>
              <View className="flex-row items-center mt-2 space-x-2">
                <RsIcon />
                <Text className="font-[600] leading-[27px] font-Pop text-[18px] ">
                  {orders?.pastOrders?.item_total}
                </Text>
              </View>
            </View>
          </View>
          <View className="h-full w-[2px] bg-[#939D94]"></View>
          <View className="items-center justify-center">
            <View className="flex-row items-center space-x-2">
              <ScooterIcon />
              <Text className="pt-1 font-Pop font-[500] leading-[16px] text-[16px]">
                8.12km
              </Text>
            </View>
          </View>
          <View className="h-full w-[2px] bg-[#939D94]"></View>
          <View className="items-center justify-center">
            <View className="flex-row items-center space-x-2">
              <ClockIcon />
              <Text className="pt-1 font-Pop font-[500] leading-[16px] text-[16px]">
                {orders?.pastOrders?.edt} min
              </Text>
            </View>
          </View>
        </View>
        <TouchableOpacity
          onPress={() => {
            OrderDetailsBottomSheetRef.current.open();
          }}
          className="mt-5 flex-row items-center space-x-3"
        >
          <Text className="font-[500] font-Pop leading-[24px] text-[16px]">
            View Details
          </Text>
          <ArrowIcon />
        </TouchableOpacity>
        {orders?.pastOrders?.pickup_address?.map((orders) => {
          return (
            <>
              <View className="flex-row space-x-2 mt-4">
                <View className="">
                  <LocationMarkIcon2 />
                </View>
                <View className="">
                  <Text className="">Pickup Location</Text>
                  <Text className="">{orders?.shop_name}</Text>
                  <Text className="pr-4">{orders?.address}</Text>
                </View>
              </View>
            </>
          );
        })}

        <View className="flex-row space-x-2 mt-5">
          <View className="">
            <LocationMarkIcon1 />
          </View>
          <View className="">
            <Text className="">Dropoff Location</Text>
            <Text className="">
              {orders?.pastOrders?.delivery_address?.receiver_name}
            </Text>
            <Text className="pr-4">
              {orders?.pastOrders?.delivery_address?.address}
            </Text>
          </View>
        </View>

        <View className="flex-row items-center justify-between mt-5 px-2">
          <View className="">
            <View className="">
              <Text className="">{C_Date[0]}</Text>
            </View>
            <View className="">
              <Text className="">{`${C_Date[1]} ${C_Date[2]}`}</Text>
            </View>
          </View>
          <View className="h-[26px] w-[89px] items-center justify-center bg-[#A4F4AC] border-[1px] border-[#00660A] rounded-[16px]">
            <Text className="">Delivered</Text>
          </View>
        </View>
      </View>
      <OrderDetailsBottomSheet
        ref={OrderDetailsBottomSheetRef}
        invoice_no={orders?.pastOrders?.invoice_no}
      />
    </>
  );
};

// const OrderDetailsBottomSheet = forwardRef((props, ref) => {
//   return (
//     <>
//       <RBSheet
//         ref={ref}
//         customStyles={{
//           container: {
//             borderRadius: 20,
//             height: 700,
//           },
//           draggableIcon: {
//             backgroundColor: "#000",
//           },
//         }}
//         useNativeDriver={true}
//         customModalProps={{
//           statusBarTranslucent: true,
//         }}
//         customAvoidingViewProps={{
//           enabled: false,
//         }}
//       >
//         <View className="px-8">
//           <View className="flex-row mt-8 justify-between">
//             <View className="items-center justify-center">
//               <View className="">
//                 <Text className="font-[400] text-[12px] leading-[16px] font-Pop text-[#001A03]">
//                   Order Amount
//                 </Text>
//                 <View className="flex-row items-center mt-2 space-x-2">
//                   <RsIcon />
//                   <Text className="font-[600] leading-[27px] font-Pop text-[18px] ">
//                     2478
//                   </Text>
//                 </View>
//               </View>
//             </View>
//             <View className="h-full w-[2px] bg-[#939D94]"></View>
//             <View className="items-center justify-center">
//               <View className="flex-row items-center space-x-2">
//                 <ScooterIcon />
//                 <Text className="pt-1 font-Pop font-[500] leading-[16px] text-[16px]">
//                   8.12km
//                 </Text>
//               </View>
//             </View>
//             <View className="h-full w-[2px] bg-[#939D94]"></View>
//             <View className="items-center justify-center">
//               <View className="flex-row items-center space-x-2">
//                 <ClockIcon />
//                 <Text className="pt-1 font-Pop font-[500] leading-[16px] text-[16px]">
//                   30 mins
//                 </Text>
//               </View>
//             </View>
//           </View>
//           <View className="">
//             <View className="flex-row space-x-2 mt-8">
//               <View className="">
//                 <LocationMarkIcon2 />
//               </View>
//               <View className="">
//                 <Text className="">Pickup Location</Text>
//                 <Text className="">Banana Leaf @ Komala Vilas</Text>
//                 <Text className="">73 &75, Lake Market Area Kolkata</Text>
//               </View>
//             </View>
//             <View className="flex-row space-x-2 mt-5">
//               <View className="">
//                 <LocationMarkIcon1 />
//               </View>
//               <View className="">
//                 <Text className="">Dropoff Location</Text>
//                 <Text className="">Narmadha</Text>
//                 <Text className="">Radhanath Chowdhary Road, Seal Lane</Text>
//               </View>
//             </View>
//           </View>

//           <View className="mt-6">
//             <View className="">
//               <Text className="">Earnings Details</Text>
//             </View>
//             <View className="px-4">
//               <View className="flex-row justify-between my-4">
//                 <View className="">
//                   <Text className="">Total</Text>
//                 </View>
//                 <View className="">
//                   <Text className="">₹ 120</Text>
//                 </View>
//               </View>
//               <View className="flex-row justify-between mb-4">
//                 <View className="">
//                   <Text className="">Order Mode</Text>
//                 </View>
//                 <View className="">
//                   <Text className="">COD</Text>
//                 </View>
//               </View>
//               <View className="flex-row justify-between mt-4">
//                 <View className="">
//                   <Text className="">Order Earning</Text>
//                 </View>
//                 <View className="">
//                   <Text className="">₹ 120</Text>
//                 </View>
//               </View>
//               <View className="flex-row justify-between my-4">
//                 <View className="">
//                   <Text className="">First Mile Earning</Text>
//                 </View>
//                 <View className="">
//                   <Text className="">₹ 2.0</Text>
//                 </View>
//               </View>
//               <View className="flex-row justify-between ">
//                 <View className="">
//                   <Text className="">Last Mile Earning</Text>
//                 </View>
//                 <View className="">
//                   <Text className="">₹ 39</Text>
//                 </View>
//               </View>
//               <View className="flex-row justify-between my-4">
//                 <View className="">
//                   <Text className="">Order Completion Bonus</Text>
//                 </View>
//                 <View className="">
//                   <Text className="">₹ 3.0</Text>
//                 </View>
//               </View>
//               <View className="flex-row justify-between">
//                 <View className="">
//                   <Text className="">Wait Time Pay</Text>
//                 </View>
//                 <View className="">
//                   <Text className="">₹ 1.0</Text>
//                 </View>
//               </View>
//               <View className="flex-row justify-between mt-4">
//                 <View className="">
//                   <Text className="">Long Distance return Bonus</Text>
//                 </View>
//                 <View className="">
//                   <Text className="">₹ 19.0</Text>
//                 </View>
//               </View>
//             </View>
//           </View>
//           <View className="flex-row mt-8 justify-between">
//             <View className="items-center justify-center">
//               <Text className="font-[400] text-[12px] leading-[16px] font-Pop text-[#001A03]">
//                 First Mile
//               </Text>
//               <Text className="font-[600] leading-[27px] font-Pop text-[18px] ">
//                 0.371 KM
//               </Text>
//             </View>
//             <View className="h-full w-[2px] bg-[#939D94]"></View>
//             <View className="items-center justify-center">
//               <View className="">
//                 <Text className="font-[400] text-[12px] leading-[16px] font-Pop text-[#001A03]">
//                   Last mile
//                 </Text>
//                 <Text className="font-[600] leading-[27px] font-Pop text-[18px] ">
//                   7.75 KM
//                 </Text>
//               </View>
//             </View>
//             <View className="h-full w-[2px] bg-[#939D94]"></View>
//             <View className="items-center justify-center">
//               <View className="">
//                 <Text className="font-[400] text-[12px] leading-[16px] font-Pop text-[#001A03]">
//                   Total
//                 </Text>
//                 <Text className="font-[600] leading-[27px] font-Pop text-[18px] ">
//                   8.121 KM
//                 </Text>
//               </View>
//             </View>
//           </View>
//         </View>
//       </RBSheet>
//     </>
//   );
// });

export default index;
