{"artifacts": [{"path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libappmodules.so"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "target_compile_options", "target_include_directories"], "files": ["D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake", "CMakeLists.txt", "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt", "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 31, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 56, "parent": 2}, {"command": 2, "file": 0, "line": 101, "parent": 2}, {"command": 2, "file": 0, "line": 87, "parent": 2}, {"command": 3, "file": 0, "line": 63, "parent": 2}, {"command": 4, "file": 0, "line": 58, "parent": 2}, {"file": 2}, {"command": 4, "file": 2, "line": 83, "parent": 8}, {"file": 3}, {"command": 4, "file": 3, "line": 82, "parent": 10}, {"file": 4}, {"command": 4, "file": 4, "line": 80, "parent": 12}, {"file": 5}, {"command": 4, "file": 5, "line": 83, "parent": 14}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC"}, {"backtrace": 6, "fragment": "-Wall"}, {"backtrace": 6, "fragment": "-Werror"}, {"backtrace": 6, "fragment": "-Wno-error=cpp"}, {"backtrace": 6, "fragment": "-fexceptions"}, {"backtrace": 6, "fragment": "-frtti"}, {"backtrace": 6, "fragment": "-std=c++20"}, {"backtrace": 6, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 6, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 4, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 4, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 4, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"define": "appmodules_EXPORTS"}], "includes": [{"backtrace": 7, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, {"backtrace": 7, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni"}, {"backtrace": 9, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni"}, {"backtrace": 11, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni"}, {"backtrace": 13, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni"}, {"backtrace": 15, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni"}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage"}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen"}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/."}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider"}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia"}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen"}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/."}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext"}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/."}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens"}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/."}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "dependencies": [{"backtrace": 4, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe"}, {"backtrace": 4, "id": "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2"}, {"backtrace": 4, "id": "react_codegen_RNCSlider::@4898bc4726ecf1751b6a"}, {"backtrace": 4, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad"}, {"backtrace": 4, "id": "react_codegen_rnskia::@376d8504f62839611b97"}, {"backtrace": 4, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec"}, {"backtrace": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a"}, {"backtrace": 4, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65"}], "id": "appmodules::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections", "role": "flags"}, {"backtrace": 4, "fragment": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433\\obj\\armeabi-v7a\\libreact_codegen_RNCSlider.so", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433\\obj\\armeabi-v7a\\libreact_codegen_rnsvg.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6dd910b94089a928dab663afb856f4b\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "appmodules", "nameOnDisk": "libappmodules.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "Object Libraries", "sourceIndexes": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "OnLoad.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/RNDateTimePickerCGen-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/rnskiaJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/rnskia-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}