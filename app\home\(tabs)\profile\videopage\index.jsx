import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, TouchableOpacity, Alert } from "react-native";
import { Camera } from "expo-camera";
import { useLocalSearchParams } from "expo-router";

const VideoCaptureScreen = () => {
  const { id, invoice_no, orderNumber, from } = useLocalSearchParams();
  const [hasCameraPermission, setHasCameraPermission] = useState(null);
  const [cameraRef, setCameraRef] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const [type, setType] = useState(Camera.Constants.Type.back);
  const [isCameraReady, setIsCameraReady] = useState(false);

  useEffect(() => {
    (async () => {
      const {
        status: cameraStatus,
      } = await Camera.requestCameraPermissionsAsync();
      const {
        status: audioStatus,
      } = await Camera.requestMicrophonePermissionsAsync();

      setHasCameraPermission(
        cameraStatus === "granted" && audioStatus === "granted"
      );
    })();
  }, []);

  const handleRecordVideo = async () => {
    if (!isCameraReady) {
      Alert.alert("Error", "Camera is not ready yet!");
      return;
    }
    if (cameraRef) {
      if (isRecording) {
        setIsRecording(false);
        const video = await cameraRef.stopRecording();
        Alert.alert("Video Recorded!", `File saved to: ${video.uri}`);
      } else {
        setIsRecording(true);
        try {
          await cameraRef.recordAsync({
            quality: Camera.Constants.VideoQuality["1080p"],
            maxDuration: 60, // Limit to 60 seconds
          });
        } catch (e) {
          Alert.alert("Error", "Could not record video: " + e.message);
          setIsRecording(false);
        }
      }
    }
  };

  if (hasCameraPermission === null) {
    return <View />;
  }
  if (hasCameraPermission === false) {
    return <Text>No access to camera or microphone</Text>;
  }

  return (
    <View style={styles.container}>
      <Camera
        style={styles.camera}
        type={type}
        ref={(ref) => setCameraRef(ref)}
        onCameraReady={() => setIsCameraReady(true)}
      >
        <View style={styles.controls}>
          <TouchableOpacity
            style={styles.button}
            onPress={() =>
              setType(
                type === Camera.Constants.Type.back
                  ? Camera.Constants.Type.front
                  : Camera.Constants.Type.back
              )
            }
          >
            <Text style={styles.text}> Flip </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.recordButton,
              { backgroundColor: isRecording ? "red" : "white" },
            ]}
            onPress={handleRecordVideo}
          >
            <Text style={styles.text}>{isRecording ? "Stop" : "Record"}</Text>
          </TouchableOpacity>
        </View>
      </Camera>
    </View>
  );
};

export default VideoCaptureScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  controls: {
    flex: 1,
    flexDirection: "row",
    backgroundColor: "transparent",
    justifyContent: "space-around",
    alignItems: "flex-end",
    marginBottom: 20,
  },
  button: {
    backgroundColor: "#fff",
    padding: 10,
    borderRadius: 5,
  },
  recordButton: {
    padding: 20,
    borderRadius: 30,
  },
  text: {
    fontSize: 16,
    color: "#000",
  },
});
