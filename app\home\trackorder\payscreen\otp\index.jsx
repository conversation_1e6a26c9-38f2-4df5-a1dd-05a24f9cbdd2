import {
  View,
  Text,
  TouchableOpacity,
  Keyboard,
  TouchableWithoutFeedback,
} from "react-native";
import React, { useEffect, useState } from "react";
import OtpTextInput from "react-native-text-input-otp";
import LinearGradientComponent from "@/component/LinearGradientComponent";
import { SafeAreaView } from "react-native-safe-area-context";
import ButtonComponent from "@/component/ButtonComponent";
import { router, useLocalSearchParams } from "expo-router";
import PrivacyPolicyComponent from "@/component/PrivacyPolicyComponent";
import { useLogin } from "@/store";

const otp = () => {
  const { number } = useLocalSearchParams();
  const [otp, setOtp] = useState("");
  const [Isotpfilled, setotpfilled] = useState(false);
  const check = () => {
    if (otp.length === 6) {
      setotpfilled(true);
    } else {
      setotpfilled(false);
    }
  };
  useEffect(() => {
    check();
  }, [otp]);
  const [sendOtp, setsendOtp] = useState(false);

  const [resendOtp, setresendOtp] = useState(false);
  const [count, setcount] = useState(0);
  const inerval = () => {
    setInterval(() => {
      setcount((count) => {
        if (count > 0) {
          return count - 1;
        } else {
          clearInterval();
          setresendOtp(false);
        }
      });
    }, 1000);
  };

  useEffect(() => {
    check();
  }, [otp]);
  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <SafeAreaView
        style={{
          flex: 1,
          position: "relative",
        }}
      >
        {/* LinearGradient Component Area*/}
        <LinearGradientComponent />
        <View className="z-10 px-6 justify-center">
          {/* Login Title Area */}
          <View className="items-center justify-end h-[24%] mb-8 gap-3">
            <View>
              <Text className="font-[400] text-[26px] text-white">
                Enter OTP
              </Text>
            </View>
            <View className="flex-row items-center justify-center">
              <Text className="font-[400] text-[18px] text-white">
                We’ve sent a text on {number}
              </Text>
              <TouchableOpacity
                onPress={() => {
                  router.back();
                }}
              >
                <Text className="font-[400] text-[18px] text-[#FFD401]">
                  edit
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          {/* Otp Field Area */}
          <View className="items-center justify-center">
            <View
              style={{
                maxWidth: "90%",
              }}
            >
              <OtpTextInput
                otp={otp}
                setOtp={setOtp}
                digits={6}
                style={{
                  borderRadius: 0,
                  borderTopWidth: 0,
                  borderRightWidth: 0,
                  borderLeftWidth: 0,
                  height: 45,
                }}
                fontStyle={{ fontSize: 26, fontWeight: "bold", color: "#fff" }}
                focusedStyle={{ borderColor: "#5cb85c", borderBottomWidth: 2 }}
              />
            </View>
          </View>
          {/* Otp resend text Area */}
          <View className="flex-row items-center justify-center mt-14 space-x-1">
            <Text className="font-[400] text-[18px] text-[#fff]">
              {resendOtp ? <>Resend the OTP in </> : <>Didn’t get it? </>}
            </Text>
            {resendOtp ? (
              <>
                <Text className="font-[400] text-[18px] text-[#FFD401]">
                  {count}
                </Text>
              </>
            ) : (
              <TouchableOpacity
                onPress={() => {
                  inerval();
                  setcount(60);
                  setresendOtp(true);
                }}
              >
                <Text className="font-[400] text-[18px] text-[#FFD401]">
                  Resend
                </Text>
              </TouchableOpacity>
            )}
          </View>
          {/* Terms of services and Privacy Policy Area */}
          <View className="mt-36">
            <View clsassName="items-center justify-center">
              <PrivacyPolicyComponent />
            </View>
          </View>
          {/* Button Area */}
          <View className="mt-4">
            <ButtonComponent
              text="Verify OTP"
              value={Isotpfilled}
              pressfun={() => {
                if (Isotpfilled) {
                  router.push("home/trackorder/payscreen/otp/paymentsuccess");
                }
              }}
            />
          </View>
        </View>
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
};

export default otp;
