import BackArrow from "../../../assets/icons/BackArrow.svg";
import LocationMarkIcon1 from "../../../assets/Hometab/drop_location.svg";
import LocationMarkIcon2 from "../../../assets/Hometab/gridicons_location.svg";
import React from "react";
import useTenStackHook from "@/hooks/TenStackHook/TenStackHook";
import { router, useLocalSearchParams } from "expo-router";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { _Log } from "@/utils";

const Index = () => {
  const { invoice_no, orderNumber } = useLocalSearchParams();
  const { data, isLoading } = useTenStackHook<
    { invoice_no: string | string[] },
    any
  >({
    endpoint: "order/accept_order_live_task",
    params: {
      invoice_no: invoice_no,
    },
    canSave: true,
    key: "accept_order_live_task",
    id: invoice_no as string,
  });
  const { data: invoice_detailsData, isLoading: invoice_detailsIsLoding } =
    useTenStackHook<{ invoice_no: string | string[] }, any>({
      endpoint: "order/invoice_details",
      params: {
        invoice_no: invoice_no,
      },
      canSave: true,
      key: "invoice_details",
      id: invoice_no as string,
    });

  return (
    <SafeAreaView>
      <ScrollView>
        <View className="px-4 mt-3">
          <View className="flex-row relative items-center justify-start mt-4">
            <TouchableOpacity
              onPress={() => {
                router.back();
              }}
              className="absolute"
            >
              <View>
                <BackArrow />
              </View>
            </TouchableOpacity>
            <View className="flex-1 items-center justify-center">
              <Text className="font-[400] text-[19px] leading-[39px]">
                Live Task
              </Text>
              <Text className="">Order ID: {orderNumber}</Text>
            </View>
          </View>
        </View>
        {isLoading ? (
          <></>
        ) : (
          <>
            {data?.pickupAddress?.map(
              ({
                id,
                shop_name,
                logo,
                banner_image,
                address,
                latitude,
                longitude,
                distance,
                is_pickup_done,
              }: any) => {
                return (
                  <>
                    <PickupLocationCard
                      key={id}
                      id={id}
                      shop_name={shop_name}
                      logo={logo}
                      banner_image={banner_image}
                      address={address}
                      latitude={latitude}
                      longitude={longitude}
                      orderNumber={orderNumber}
                      distance={distance}
                      otp={data?.order_otp}
                      invoice_no={data?.invoice_no}
                      IsOrderPickedUp={is_pickup_done == 1 ? true : false}
                    />
                  </>
                );
              }
            )}

            {!data?.pickupAddress
              ?.map((items: any) => {
                return items.is_pickup_done == 1;
              })
              .includes(false) && (
              <>
                <DropOffLocationCard
                  isPickup={
                    data?.deliveryAddress?.is_delivery_done === 1 ? true : false
                  }
                  dropOffLocation={data?.deliveryAddress}
                  invoice_no={invoice_no}
                  orderNumber={orderNumber}
                  key={1}
                />
              </>
            )}
            {invoice_detailsIsLoding ? (
              <></>
            ) : (
              <>
                {data?.deliveryAddress?.is_delivery_done === 1 &&
                  invoice_detailsData?.info?.product_mode === 1 && (
                    <PayMentCard
                      info={invoice_detailsData?.info}
                      data={data?.deliveryAddress}
                    />
                  )}
              </>
            )}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export const PickupLocationCard = ({
  id,
  shop_name,
  logo,
  banner_image,
  address,
  orderNumber,
  latitude,
  longitude,
  distance,
  IsOrderPickedUp,
  otp,
  invoice_no,
}: any) => {
  return (
    <>
      <View className="p-4 border-[1px] border-[#E9EAE9] rounded-[8px] m-5">
        <View className="flex-row items-center justify-between">
          <View className="flex-row space-x-2 mt-4">
            <View className="">
              <LocationMarkIcon2 />
            </View>
            <View className="">
              <Text className="">Pickup Location</Text>
              <Text className="">{shop_name}</Text>
              <Text className="pr-5">{address}</Text>
            </View>
          </View>
        </View>
        <>
          <TouchableOpacity
            disabled={IsOrderPickedUp}
            onPress={() => {
              router.push({
                pathname: "/home/<USER>/pickupmap",
                params: {
                  id: id,
                  shop_name: shop_name,
                  logo: logo,
                  banner_image: banner_image,
                  address: address,
                  latitude: latitude,
                  longitude: longitude,
                  orderNumber: orderNumber,
                  distance: distance,
                  IsOrderPickedUp: IsOrderPickedUp,
                  otp: otp,
                  invoice_no: invoice_no,
                },
              });
            }}
            className="bg-[#00660A] h-[48px] items-center justify-center mt-6 rounded-[8px] "
          >
            <Text className="font-[500] font-Pop leading-[24px] text-[#fff] ">
              {IsOrderPickedUp ? "Completed" : "Go to Pickup"}
            </Text>
          </TouchableOpacity>
        </>
      </View>
    </>
  );
};

export const DropOffLocationCard = ({
  isPickup,
  dropOffLocation,
  invoice_no,
  orderNumber,
}: any) => {
  return (
    <>
      <View className="p-4 border-[1px] border-[#E9EAE9] rounded-[8px] m-5">
        <View className="flex-row items-center justify-between">
          <View className="flex-row space-x-2 mt-4">
            <View className="">
              <LocationMarkIcon1 />
            </View>
            <View className="">
              <Text className="">Dropoff Location</Text>
              <Text className="">{dropOffLocation?.receiver_name}</Text>
              <Text className="pr-5">{dropOffLocation?.address}</Text>
            </View>
          </View>
        </View>
        <TouchableOpacity
          disabled={isPickup}
          onPress={() => {
            router.push({
              pathname: "/home/<USER>/dropoffmap",
              params: {
                id: dropOffLocation?.id,
                user_Id: dropOffLocation?.user_id,
                address: dropOffLocation?.address,
                type: dropOffLocation?.type,
                receiver_name: dropOffLocation?.receiver_name,
                receiver_contact: dropOffLocation?.receiver_contact,
                area_sector: dropOffLocation?.area_sector,
                latitude: dropOffLocation?.latitude,
                longitude: dropOffLocation?.longitude,
                is_delivery_done: dropOffLocation?.is_delivery_done,
                invoice_no: invoice_no,
                orderNumber: orderNumber,
              },
            });
          }}
          className="bg-[#00660A] h-[48px] items-center justify-center mt-6 rounded-[8px] "
        >
          <Text className="font-[500] font-Pop leading-[24px] text-[#fff] ">
            {isPickup ? "Completed" : "Go to Dropoff"}
          </Text>
        </TouchableOpacity>
      </View>
    </>
  );
};

export const PayMentCard = ({ info, data }: any) => {
  return (
    <View className="p-4 border-[1px] border-[#E9EAE9] rounded-[8px] ">
      <View className="flex-row items-center justify-between">
        <View className="flex-row space-x-2 mt-4">
          <View className="">
            <LocationMarkIcon2 />
          </View>
          <View className="">
            <Text className="">Confirm Payment</Text>
            <Text className="">{data?.receiver_name}</Text>
            <Text className="max-w-[200px]" numberOfLines={2}>
              {data?.address}
            </Text>
          </View>
        </View>
        <View className="h-[26px] items-center justify-center rounded-[60px] border-[#F79009] border-[2px] bg-[#FFE49D]">
          <Text className="text-[#F79009]">COD: ₹{info?.order_amnt}</Text>
        </View>
      </View>
      <View className="flex-row justify-between mt-6">
        <TouchableOpacity
          onPress={() => {
            router.push({
              pathname: "/home/<USER>/payscreen",
              params: { amount: info?.order_amnt },
            });
          }}
          className="bg-[#00660A] min-w-[150px] h-[48px] items-center justify-center rounded-[8px] px-2"
        >
          <Text className="font-[500] font-Pop text-[#fff] ">
            Online Payment (UPI)
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            router.push("/home/<USER>/payscreen/otp/paymentsuccess");
          }}
          className="bg-[#00660A] min-w-[150px] h-[48px] items-center justify-center rounded-[8px] px-2"
        >
          <Text className="font-[500] font-Pop text-[#fff] ">Cash Payment</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default Index;
