import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Dimensions,
} from "react-native";
import React, { useState } from "react";
import { router } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import BackArrow from "@/assets/icons/BackArrow.svg";
import Rsicon from "@/assets/Hometab/rs.svg";
import useGetApiData from "../../../../../../hooks/useGetApiData";

const addmoney = () => {
  const { data, isLoading, triggerreFresh } = useGetApiData({
    endpoint: "wallet_balance",
  });
  const [value, setvalue] = useState("0");

  return (
    <SafeAreaView className="flex-1">
      <View className="px-4 bg-[#FFFFFF] flex-1 justify-between">
        <View>
          <View className="justify-between mt-3">
            <View className="flex-row relative items-center justify-start mt-4">
              <TouchableOpacity
                onPress={() => {
                  router.back();
                }}
                className="absolute"
              >
                <View>
                  <BackArrow />
                </View>
              </TouchableOpacity>
              <View className="flex-1 items-center justify-center">
                <Text className="font-[400] text-[26px] leading-[39px]">
                  Add Money
                </Text>
              </View>
            </View>
          </View>
          {/* Amount Details Area */}
          <View className="items-center justify-center mt-6 mb-14">
            <Text className="font-[500] text-[16px] leading-[24px] text-[#627164]">
              Balance Available
            </Text>
            <View className="mt-1 flex-row items-center">
              <View>
                <Rsicon width={16} height={25} />
              </View>
              {isLoading ? (
                <></>
              ) : (
                <>
                  <Text className="font-[400] text-[24px] leading-[36px]">
                    {data?.data}
                  </Text>
                </>
              )}
            </View>
          </View>
          {/* Enter Amount Area */}
          <View className="flex-row items-center justify-center">
            <View className="mr-4">
              <Rsicon width={16} height={25} />
            </View>
            <TextInput
              className="font-[400] text-[24px] leading-[36px] text-[#627164]"
              cursorColor={"#000"}
              value={value}
              onChangeText={setvalue}
              keyboardType="numeric"
            />
          </View>
        </View>
        <View className="">
          <TouchableOpacity
            onPress={() => {
              if (value > 10) {
                router.push({
                  pathname: "home/profile/wallet/addmoney/paymentmode",
                  params: {
                    amount: value,
                  },
                });
              }
            }}
            className="items-center justify-center h-[44px] bg-[#00660A] mb-4"
          >
            <Text className="font-[400] text-[16px] leading-[24px] text-[#FFFFFF]">
              Add Money
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default addmoney;
