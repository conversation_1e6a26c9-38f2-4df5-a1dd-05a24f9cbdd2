import * as Location from "expo-location";
import BackArrow from "../../../../assets/icons/BackArrow.svg";
import DirectionsIcon from "../../../../assets/Hometab/ic_outline-directions.svg";
import DotIcon from "../../../../assets/Hometab/doticon.svg";
import HeadSetIcon from "../../../../assets/Hometab/lucide_headset.svg";
import LocationIcon from "../../../../assets/Hometab/gridicons_location.svg";
import MapView, { Marker } from "react-native-maps";
import MessageIcon from "../../../../assets/Hometab/ic_round-message.svg";
import PhoneIcon from "../../../../assets/Hometab/mingcute_phone-fill.svg";
import PopUpComponent from "../../../../component/PopUpModle/PopUpComponent";
import React, { useEffect, useRef, useState } from "react";
import VegIcon from "../../../../assets/Hometab/mdi_lacto-vegetarian.svg";
import { router, useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";

import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from "react-native";

const index = () => {
  const [region, setRegion] = useState(null);
  const mapRef = useRef(null);
  const [showissueMenu, setshowissueMenu] = useState(false);

  const { user_Id, address, receiver_name, invoice_no, orderNumber } =
    useLocalSearchParams();

  const requestLocationPermission = async () => {
    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Permission denied",
        "Allow location access to use this feature."
      );
      return;
    }

    let location = await Location.getCurrentPositionAsync({});
    const { latitude, longitude } = location.coords;
    const newRegion = {
      latitude,
      longitude,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    };
    setRegion(newRegion);
    if (mapRef.current) {
      mapRef.current.animateToRegion(newRegion, 0);
    }
  };

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const [modle, setmodle] = useState(false);

  return (
    <SafeAreaView className="flex-1 justify-between">
      <ScrollView>
        <View className="px-4 mt-3">
          <View className="flex-row relative items-center justify-start mt-4">
            <TouchableOpacity
              onPress={() => {
                router.back();
              }}
              className="absolute"
            >
              <View>
                <BackArrow />
              </View>
            </TouchableOpacity>
            <View className="flex-1 items-center justify-center">
              <Text className="font-[400] text-[19px] leading-[39px]">
                Go to Dropoff
              </Text>
              <Text className="">Order ID: {orderNumber}</Text>
            </View>
            <View className="relative">
              <TouchableOpacity
                onPress={() => {
                  setshowissueMenu(!showissueMenu);
                }}
              >
                <DotIcon />
              </TouchableOpacity>
              {showissueMenu && (
                <TouchableOpacity
                  onPress={() => {
                    router.push("home/trackorder/pickupmap/issue");
                  }}
                  className="right-3 z-10 top-6 absolute flex-row space-x-2 h-[45px] w-[138px] items-center justify-center bg-[#000] rounded-[8px]"
                >
                  <HeadSetIcon />
                  <Text className="font-[400] font-Pop text-[#fff] leading-[21px] text-[14px]">
                    Report issue
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
        <View className="relative h-[400px] mt-7">
          <MapView
            style={StyleSheet.absoluteFill}
            region={region}
            provider={"google"}
            ref={mapRef}
            showsUserLocation
            zoomEnabled={true}
            onRegionChangeComplete={setRegion}
            key={1}
          ></MapView>
        </View>
        <View className="mt-6 px-4 flex-row items-start justify-between">
          <View
            className="flex-row space-x-6 flex-1"
            style={{
              alignItems: "flex-start",
            }}
          >
            <View className="">
              <LocationIcon />
            </View>
            <View className="space-y-4 flex-1">
              <Text className="">{receiver_name}</Text>
              <View className="flex-row">
                <Text className="leading-5 flex-1">{address}</Text>
              </View>
              <View className="flex-row items-center space-x-6 mt-4">
                <PhoneIcon />
                <MessageIcon />
              </View>
            </View>
          </View>
          <View className="items-center justify-center space-y-2">
            <DirectionsIcon />
            <Text className="">Get Directions</Text>
          </View>
        </View>

        <PopUpComponent
          modle={modle}
          setmodle={setmodle}
          text={"DropOff"}
          onpressfun={() => {
            router.push({
              // pathname: "home/trackorder/dropoffmap/camara",
              pathname: "home/trackorder/dropoffmap/videopage",
              params: {
                id: user_Id,
                invoice_no: invoice_no,
                orderNumber: orderNumber,
                from: "drop",
              },
            });
          }}
        />
      </ScrollView>
      <View className="my-4 px-4">
        <TouchableOpacity
          onPress={() => {
            setmodle(true);
          }}
          className="h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
        >
          <Text className="font-[400] font-Pop text-[16px] leading-[24px] text-[#fff]">
            Reached DropOff Location
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default index;
