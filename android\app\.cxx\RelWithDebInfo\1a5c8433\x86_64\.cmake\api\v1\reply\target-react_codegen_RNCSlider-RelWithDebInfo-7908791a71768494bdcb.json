{"artifacts": [{"path": "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/x86_64/libreact_codegen_RNCSlider.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_compile_options", "target_compile_options", "target_include_directories"], "files": ["D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 24, "parent": 0}, {"command": 1, "file": 0, "line": 43, "parent": 0}, {"command": 2, "file": 0, "line": 12, "parent": 0}, {"command": 3, "file": 0, "line": 73, "parent": 0}, {"command": 4, "file": 0, "line": 31, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC"}, {"backtrace": 3, "fragment": "-fexceptions"}, {"backtrace": 3, "fragment": "-frtti"}, {"backtrace": 3, "fragment": "-std=c++20"}, {"backtrace": 3, "fragment": "-Wall"}, {"backtrace": 3, "fragment": "-Wpedantic"}, {"backtrace": 3, "fragment": "-Wno-gnu-zero-variadic-macro-arguments"}, {"backtrace": 4, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 0, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 0, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 0, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 0, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"define": "react_codegen_RNCSlider_EXPORTS"}], "includes": [{"backtrace": 5, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/."}, {"backtrace": 5, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 5, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 5, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "react_codegen_RNCSlider::@4898bc4726ecf1751b6a", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections", "role": "flags"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6dd910b94089a928dab663afb856f4b\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "react_codegen_RNCSlider", "nameOnDisk": "libreact_codegen_RNCSlider.so", "paths": {"build": "RNCSlider_autolinked_build", "source": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/common/cpp/react/renderer/components/RNCSlider/RNCSliderMeasurementsManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/common/cpp/react/renderer/components/RNCSlider/RNCSliderShadowNode.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/RNCSlider-generated.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/ComponentDescriptors.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/EventEmitters.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/Props.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/RNCSliderJSI-generated.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/ShadowNodes.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/States.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}