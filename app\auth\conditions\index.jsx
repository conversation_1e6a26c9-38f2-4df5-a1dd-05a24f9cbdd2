import { View, Text } from "react-native";
import React from "react";
import LinearGradientComponent from "../../../component/LinearGradientComponent";
import { SafeAreaView } from "react-native-safe-area-context";

const index = () => {
  return (
    <SafeAreaView
      style={{
        flex: 1,
        position: "relative",
      }}
    >
      {/* LinearGradient Component Area*/}
      <LinearGradientComponent />

      <View className="flex-1 z-50 flex-row items-center justify-center">
        <Text
          className="font-[800] text-[14px] text-center leading-[21px] text-[#fff]"
          style={{
            fontFamily: "Poppins Regular",
          }}
        >
          By joining Ordalane you agree with our Terms of services and Privacy
          Policy
        </Text>
      </View>
    </SafeAreaView>
  );
};

export default index;
