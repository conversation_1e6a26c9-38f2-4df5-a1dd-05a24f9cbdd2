{"logs": [{"outputFile": "com.mohandhass2.myapp-mergeReleaseResources-83:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a39c8a492fe916611935c0d3835604e6\\transformed\\material-1.6.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,299,397,516,601,666,764,845,904,997,1060,1118,1189,1251,1305,1426,1483,1544,1598,1669,1802,1886,1969,2072,2154,2232,2322,2389,2455,2526,2604,2690,2765,2843,2923,3006,3094,3173,3263,3356,3430,3500,3591,3645,3712,3796,3881,3943,4007,4070,4174,4280,4377,4482", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,78,97,118,84,64,97,80,58,92,62,57,70,61,53,120,56,60,53,70,132,83,82,102,81,77,89,66,65,70,77,85,74,77,79,82,87,78,89,92,73,69,90,53,66,83,84,61,63,62,103,105,96,104,83", "endOffsets": "215,294,392,511,596,661,759,840,899,992,1055,1113,1184,1246,1300,1421,1478,1539,1593,1664,1797,1881,1964,2067,2149,2227,2317,2384,2450,2521,2599,2685,2760,2838,2918,3001,3089,3168,3258,3351,3425,3495,3586,3640,3707,3791,3876,3938,4002,4065,4169,4275,4372,4477,4561"}, "to": {"startLines": "19,50,58,59,60,86,138,142,147,149,150,151,152,153,154,155,156,157,158,159,160,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,197", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "722,3579,4393,4491,4610,7544,11517,11932,12343,12472,12565,12628,12686,12757,12819,12873,12994,13051,13112,13166,13237,13436,13520,13603,13706,13788,13866,13956,14023,14089,14160,14238,14324,14399,14477,14557,14640,14728,14807,14897,14990,15064,15134,15225,15279,15346,15430,15515,15577,15641,15704,15808,15914,16011,16284", "endLines": "22,50,58,59,60,86,138,142,147,149,150,151,152,153,154,155,156,157,158,159,160,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,197", "endColumns": "12,78,97,118,84,64,97,80,58,92,62,57,70,61,53,120,56,60,53,70,132,83,82,102,81,77,89,66,65,70,77,85,74,77,79,82,87,78,89,92,73,69,90,53,66,83,84,61,63,62,103,105,96,104,83", "endOffsets": "882,3653,4486,4605,4690,7604,11610,12008,12397,12560,12623,12681,12752,12814,12868,12989,13046,13107,13161,13232,13365,13515,13598,13701,13783,13861,13951,14018,14084,14155,14233,14319,14394,14472,14552,14635,14723,14802,14892,14985,15059,15129,15220,15274,15341,15425,15510,15572,15636,15699,15803,15909,16006,16111,16363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4d414adee14b24510c264c21c70961f0\\transformed\\appcompat-1.7.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "887,1001,1101,1210,1296,1402,1516,1599,1680,1771,1864,1959,2055,2152,2245,2339,2431,2522,2612,2692,2799,2902,2999,3106,3208,3321,3480,16534", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "996,1096,1205,1291,1397,1511,1594,1675,1766,1859,1954,2050,2147,2240,2334,2426,2517,2607,2687,2794,2897,2994,3101,3203,3316,3475,3574,16610"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6591935515ee6cd5ec7881ab1d3c64e\\transformed\\core-1.13.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "51,52,53,54,55,56,57,207", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3658,3756,3858,3959,4057,4162,4274,17073", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "3751,3853,3954,4052,4157,4269,4388,17169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eeb7a35c1d2dda21edaace2253c1f099\\transformed\\exoplayer-ui-2.18.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,496,672,758,847,931,1023,1114,1190,1255,1344,1437,1508,1576,1637,1705,1860,2018,2172,2239,2321,2392,2472,2563,2657,2723,2788,2841,2899,2947,3008,3070,3146,3208,3272,3333,3394,3458,3523,3589,3641,3705,3783,3861", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,88,83,91,90,75,64,88,92,70,67,60,67,154,157,153,66,81,70,79,90,93,65,64,52,57,47,60,61,75,61,63,60,60,63,64,65,51,63,77,77,57", "endOffsets": "280,491,667,753,842,926,1018,1109,1185,1250,1339,1432,1503,1571,1632,1700,1855,2013,2167,2234,2316,2387,2467,2558,2652,2718,2783,2836,2894,2942,3003,3065,3141,3203,3267,3328,3389,3453,3518,3584,3636,3700,3778,3856,3914"}, "to": {"startLines": "2,11,15,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,546,7609,7695,7784,7868,7960,8051,8127,8192,8281,8374,8445,8513,8574,8642,8797,8955,9109,9176,9258,9329,9409,9500,9594,9660,10386,10439,10497,10545,10606,10668,10744,10806,10870,10931,10992,11056,11121,11187,11239,11303,11381,11459", "endLines": "10,14,18,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137", "endColumns": "17,12,12,85,88,83,91,90,75,64,88,92,70,67,60,67,154,157,153,66,81,70,79,90,93,65,64,52,57,47,60,61,75,61,63,60,60,63,64,65,51,63,77,77,57", "endOffsets": "330,541,717,7690,7779,7863,7955,8046,8122,8187,8276,8369,8440,8508,8569,8637,8792,8950,9104,9171,9253,9324,9404,9495,9589,9655,9720,10434,10492,10540,10601,10663,10739,10801,10865,10926,10987,11051,11116,11182,11234,11298,11376,11454,11512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2d12819c5c8831c86b34929884541b49\\transformed\\play-services-basement-18.3.0\\res\\values-mn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "72", "startColumns": "4", "startOffsets": "5961", "endColumns": "149", "endOffsets": "6106"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7460a359c4c281b49833222722dfee74\\transformed\\play-services-wallet-18.1.3\\res\\values-mn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "77", "endOffsets": "279"}, "to": {"startLines": "213", "startColumns": "4", "startOffsets": "17609", "endColumns": "81", "endOffsets": "17686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\00636ad238812e00d92433007e026a91\\transformed\\exoplayer-core-2.18.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,244,308,382,456,547,635", "endColumns": "64,58,64,63,73,73,90,87,80", "endOffsets": "115,174,239,303,377,451,542,630,711"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9725,9790,9849,9914,9978,10052,10126,10217,10305", "endColumns": "64,58,64,63,73,73,90,87,80", "endOffsets": "9785,9844,9909,9973,10047,10121,10212,10300,10381"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b14b43fc1759186e6ca5ed92b9460e08\\transformed\\foundation-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "211,212", "startColumns": "4,4", "startOffsets": "17434,17520", "endColumns": "85,88", "endOffsets": "17515,17604"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b84b30b80c5949bd0f3610f30992b047\\transformed\\play-services-base-18.3.0\\res\\values-mn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,452,580,683,816,938,1063,1169,1309,1412,1576,1701,1838,2002,2059,2117", "endColumns": "105,152,127,102,132,121,124,105,139,102,163,124,136,163,56,57,72", "endOffsets": "298,451,579,682,815,937,1062,1168,1308,1411,1575,1700,1837,2001,2058,2116,2189"}, "to": {"startLines": "64,65,66,67,68,69,70,71,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4953,5063,5220,5352,5459,5596,5722,5851,6111,6255,6362,6530,6659,6800,6968,7029,7091", "endColumns": "109,156,131,106,136,125,128,109,143,106,167,128,140,167,60,61,76", "endOffsets": "5058,5215,5347,5454,5591,5717,5846,5956,6250,6357,6525,6654,6795,6963,7024,7086,7163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f0ad3059e848ec2624bad4e9e6fc7d3\\transformed\\browser-1.6.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,264,369", "endColumns": "104,103,104,107", "endOffsets": "155,259,364,472"}, "to": {"startLines": "82,139,140,141", "startColumns": "4,4,4,4", "startOffsets": "7168,11615,11719,11824", "endColumns": "104,103,104,107", "endOffsets": "7268,11714,11819,11927"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e9aa3803274a6cf3eaa3a68ef58f1ff5\\transformed\\ui-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,283,375,471,554,640,734,821,902,985,1068,1141,1218,1298,1375,1452,1518", "endColumns": "91,85,91,95,82,85,93,86,80,82,82,72,76,79,76,76,65,116", "endOffsets": "192,278,370,466,549,635,729,816,897,980,1063,1136,1213,1293,1370,1447,1513,1630"}, "to": {"startLines": "61,62,83,84,85,145,146,195,196,198,199,201,202,203,205,208,209,210", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4695,4787,7273,7365,7461,12163,12249,16116,16203,16368,16451,16615,16688,16765,16921,17174,17251,17317", "endColumns": "91,85,91,95,82,85,93,86,80,82,82,72,76,79,76,76,65,116", "endOffsets": "4782,4868,7360,7456,7539,12244,12338,16198,16279,16446,16529,16683,16760,16840,16993,17246,17312,17429"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,135,204,285,355,421,497", "endColumns": "79,68,80,69,65,75,74", "endOffsets": "130,199,280,350,416,492,567"}, "to": {"startLines": "63,143,144,148,161,204,206", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4873,12013,12082,12402,13370,16845,16998", "endColumns": "79,68,80,69,65,75,74", "endOffsets": "4948,12077,12158,12467,13431,16916,17068"}}]}]}