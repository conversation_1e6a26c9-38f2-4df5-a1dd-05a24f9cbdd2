import AsyncStorage from "@react-native-async-storage/async-storage";
import LinearGradientComponent from "../../../component/LinearGradientComponent";
import PrivacyPolicyComponent from "../../../component/PrivacyPolicyComponent";
import React, { useEffect, useState } from "react";
import useLoginApi from "../../../hooks/useLoginApi";
import { Redirect, router } from "expo-router";
import { Controller, useForm } from "react-hook-form";
import { Alert, Text, TextInput, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useLogin } from "../../../store";

const index = () => {
  const {
    control,
    watch,
    handleSubmit,
    reset,
    formState: { errors, isValid },
  } = useForm();

  const { SetFunction: SendOtp } = useLoginApi({
    endpoint: "sendOtp",
  });
  const Submit = (data) => {
    SendOtp({
      phone: Number(data.phone),
    }).then((response) => {
      console.log(response?.data?.otp);
      reset();
      router.push({
        pathname: "/auth/createaccount/otp/",
        params: { number: data.phone, otp: response?.data?.otp },
      });
    });
  };
  const [Login, setLogin] = useState(false);
  const CheckLogin = async () => {
    try {
      let Token = await AsyncStorage.getItem("login");
      if (Token === null) {
        setLogin(false);
      } else {
        setLogin(true);
      }
    } catch (error) {
      console.log(error);
    }
  };
  useEffect(() => {
    CheckLogin();
  }, []);
  return (
    <SafeAreaView
      className="relative justify-start"
      style={{
        flex: 1,
      }}
    >
      <LinearGradientComponent />
      <View className="z-50 items-center mt-20">
        {/* Title Area Field */}
        <View>
          <Text className="font-Pop font-[400] text-[26px] leading-[39px] text-[#E9EAE9]">
            Signup or Login
          </Text>
        </View>
        {/* Text Area  */}
        <View>
          <Text className="font-Pop font-[400] text-[18px] leading-[24px] text-[#FFF] mt-2">
            Enter your 10 digit mobile number
          </Text>
        </View>
        {/* Mobile Number Field Area */}
        <View className="mt-4 min-w-[50%]">
          <PhoneNumberField
            control={control}
            name={"phone"}
            rules={{
              minLength: {
                value: 10,
                message: "Enter 10 digit number",
              },
              required: {
                value: true,
                message: "Enter your number",
              },
              maxLength: {
                value: 10,
                message: "Enter 10 digit number",
              },
            }}
          />
        </View>
        {/* Terms of services and Privacy Policy Area */}
        <View className="mt-36">
          <View className="items-center justify-center">
            <PrivacyPolicyComponent />
          </View>
        </View>

        {/* Button Area */}
        <View
          className="px-4 mt-4"
          style={{
            opacity: isValid ? 1 : 0.5,
          }}
        >
          <TouchableOpacity
            onPress={handleSubmit(Submit)}
            className="h-[51px] rounded-[4px] bg-[#00660A] min-w-[100%] items-center justify-center"
          >
            <Text className="font-Pop font-[400] text-[#FFF] text-[18px] leading-[27px]">
              Next
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export const PhoneNumberField = ({ control, name, rules }) => {
  const [countrycode, setcountrycode] = useState("+91");
  return (
    <>
      <View>
        <Controller
          name={name}
          control={control}
          render={({ field: { onChange, name, value } }) => (
            <View className="border-b-[1px] border-[#DEDEDE] flex-row items-center">
              <Text className="font-[500] text-[28px] text-[#000] leading-[28]">
                {countrycode}
              </Text>
              <TextInput
                value={value}
                onChangeText={(value) => onChange(value)}
                className="font-[500] text-[28px] text-[#C5C5C5] pl-2 min-w-[40%]"
                keyboardType="numeric"
              />
            </View>
          )}
          rules={{ ...rules, required: true }}
        />
      </View>
    </>
  );
};

export default index;
