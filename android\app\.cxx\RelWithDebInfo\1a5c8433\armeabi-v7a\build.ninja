# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: RelWithDebInfo
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = RelWithDebInfo
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || cmake_object_order_depends_target_react_codegen_RNCSlider cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen cmake_object_order_depends_target_react_codegen_rnasyncstorage cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen cmake_object_order_depends_target_react_codegen_rnscreens cmake_object_order_depends_target_react_codegen_rnskia cmake_object_order_depends_target_react_codegen_rnsvg cmake_object_order_depends_target_react_codegen_safeareacontext

build CMakeFiles/appmodules.dir/D_/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\D_\App\Delivery-Partner\Delivery-Partner-App\android\app\build\generated\autolinking\src\main\jni\autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir\D_\App\Delivery-Partner\Delivery-Partner-App\android\app\build\generated\autolinking\src\main\jni

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\intermediates\cxx\RelWithDebInfo\1a5c8433\obj\armeabi-v7a\libappmodules.so

build D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_RelWithDebInfo rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/RNDateTimePickerCGen-generated.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/ComponentDescriptors.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/EventEmitters.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/Props.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/ShadowNodes.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/States.cpp.o rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/ComponentDescriptors.cpp.o rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/EventEmitters.cpp.o rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/Props.cpp.o rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/ShadowNodes.cpp.o rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/States.cpp.o rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/rnskiaJSI-generated.cpp.o rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/rnskia-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o CMakeFiles/appmodules.dir/D_/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_RNCSlider.so D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_safeareacontext.so D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_rnscreens.so D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_rnsvg.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so || D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_RNCSlider.so D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_rnscreens.so D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_rnsvg.so D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_safeareacontext.so RNDateTimePickerCGen_autolinked_build/react_codegen_RNDateTimePickerCGen rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen rnskia_autolinked_build/react_codegen_rnskia
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_RNCSlider.so  D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_safeareacontext.so  D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_rnscreens.so  D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_rnsvg.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  -latomic -lm
  OBJECT_DIR = CMakeFiles\appmodules.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\intermediates\cxx\RelWithDebInfo\1a5c8433\obj\armeabi-v7a\libappmodules.so
  TARGET_PDB = appmodules.so.dbg
  RSP_FILE = CMakeFiles\appmodules.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnasyncstorage


#############################################
# Order-only phony target for react_codegen_rnasyncstorage

build cmake_object_order_depends_target_react_codegen_rnasyncstorage: phony || rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\rnasyncstorageJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\rnasyncstorage-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir



#############################################
# Object library react_codegen_rnasyncstorage

build rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a\rnasyncstorage_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnasyncstorage_autolinked_build/edit_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a\rnasyncstorage_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/rebuild_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNDateTimePickerCGen


#############################################
# Order-only phony target for react_codegen_RNDateTimePickerCGen

build cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen: phony || RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/RNDateTimePickerCGen-generated.cpp.o: CXX_COMPILER__react_codegen_RNDateTimePickerCGen_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/RNDateTimePickerCGen-generated.cpp || cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen
  DEP_FILE = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\RNDateTimePickerCGen-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir
  OBJECT_FILE_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNDateTimePickerCGen_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen
  DEP_FILE = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\13e5db451150cc1628879224ca4ec527\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir
  OBJECT_FILE_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\13e5db451150cc1628879224ca4ec527

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNDateTimePickerCGen_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen
  DEP_FILE = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\13e5db451150cc1628879224ca4ec527\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir
  OBJECT_FILE_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\13e5db451150cc1628879224ca4ec527

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/Props.cpp.o: CXX_COMPILER__react_codegen_RNDateTimePickerCGen_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/Props.cpp || cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen
  DEP_FILE = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\react\renderer\components\RNDateTimePickerCGen\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir
  OBJECT_FILE_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\react\renderer\components\RNDateTimePickerCGen

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNDateTimePickerCGen_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen
  DEP_FILE = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\react\renderer\components\RNDateTimePickerCGen\RNDateTimePickerCGenJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir
  OBJECT_FILE_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\react\renderer\components\RNDateTimePickerCGen

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNDateTimePickerCGen_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen
  DEP_FILE = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\13e5db451150cc1628879224ca4ec527\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir
  OBJECT_FILE_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\13e5db451150cc1628879224ca4ec527

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/States.cpp.o: CXX_COMPILER__react_codegen_RNDateTimePickerCGen_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/States.cpp || cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen
  DEP_FILE = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\react\renderer\components\RNDateTimePickerCGen\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir
  OBJECT_FILE_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\react\renderer\components\RNDateTimePickerCGen



#############################################
# Object library react_codegen_RNDateTimePickerCGen

build RNDateTimePickerCGen_autolinked_build/react_codegen_RNDateTimePickerCGen: phony RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/RNDateTimePickerCGen-generated.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/ComponentDescriptors.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/EventEmitters.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/Props.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/ShadowNodes.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/States.cpp.o


#############################################
# Utility command for edit_cache

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a\RNDateTimePickerCGen_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNDateTimePickerCGen_autolinked_build/edit_cache: phony RNDateTimePickerCGen_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a\RNDateTimePickerCGen_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNDateTimePickerCGen_autolinked_build/rebuild_cache: phony RNDateTimePickerCGen_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_RNCSlider


#############################################
# Order-only phony target for react_codegen_RNCSlider

build cmake_object_order_depends_target_react_codegen_RNCSlider: phony || RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/d7be5d48d9e03d9efc32378cf1c65072/RNCSlider/RNCSliderMeasurementsManager.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/common/cpp/react/renderer/components/RNCSlider/RNCSliderMeasurementsManager.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\d7be5d48d9e03d9efc32378cf1c65072\RNCSlider\RNCSliderMeasurementsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\d7be5d48d9e03d9efc32378cf1c65072\RNCSlider

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/003b49a80570ffb3c0571bc833857a58/components/RNCSlider/RNCSliderShadowNode.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/common/cpp/react/renderer/components/RNCSlider/RNCSliderShadowNode.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\003b49a80570ffb3c0571bc833857a58\components\RNCSlider\RNCSliderShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\003b49a80570ffb3c0571bc833857a58\components\RNCSlider

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/561c6a8558f7f35a8c408a4148ab3526/source/codegen/jni/RNCSlider-generated.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/RNCSlider-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\561c6a8558f7f35a8c408a4148ab3526\source\codegen\jni\RNCSlider-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\561c6a8558f7f35a8c408a4148ab3526\source\codegen\jni

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/dfc4e1350bd27d7d0e423e38a8fd1f3a/components/RNCSlider/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\dfc4e1350bd27d7d0e423e38a8fd1f3a\components\RNCSlider\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\dfc4e1350bd27d7d0e423e38a8fd1f3a\components\RNCSlider

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/08d7e72103038234790cf2d9aa38e8e7/renderer/components/RNCSlider/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\08d7e72103038234790cf2d9aa38e8e7\renderer\components\RNCSlider\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\08d7e72103038234790cf2d9aa38e8e7\renderer\components\RNCSlider

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/77fd0a957a73c497cf9ef944d6bb91cd/jni/react/renderer/components/RNCSlider/Props.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/Props.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\77fd0a957a73c497cf9ef944d6bb91cd\jni\react\renderer\components\RNCSlider\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\77fd0a957a73c497cf9ef944d6bb91cd\jni\react\renderer\components\RNCSlider

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/dfc4e1350bd27d7d0e423e38a8fd1f3a/components/RNCSlider/RNCSliderJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/RNCSliderJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\dfc4e1350bd27d7d0e423e38a8fd1f3a\components\RNCSlider\RNCSliderJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\dfc4e1350bd27d7d0e423e38a8fd1f3a\components\RNCSlider

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/08d7e72103038234790cf2d9aa38e8e7/renderer/components/RNCSlider/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\08d7e72103038234790cf2d9aa38e8e7\renderer\components\RNCSlider\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\08d7e72103038234790cf2d9aa38e8e7\renderer\components\RNCSlider

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/c838027c612a797883778a7030e7c603/react/renderer/components/RNCSlider/States.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/States.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\c838027c612a797883778a7030e7c603\react\renderer\components\RNCSlider\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\c838027c612a797883778a7030e7c603\react\renderer\components\RNCSlider


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_RNCSlider


#############################################
# Link the shared library D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\intermediates\cxx\RelWithDebInfo\1a5c8433\obj\armeabi-v7a\libreact_codegen_RNCSlider.so

build D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_RNCSlider.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_RNCSlider_RelWithDebInfo RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/d7be5d48d9e03d9efc32378cf1c65072/RNCSlider/RNCSliderMeasurementsManager.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/003b49a80570ffb3c0571bc833857a58/components/RNCSlider/RNCSliderShadowNode.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/561c6a8558f7f35a8c408a4148ab3526/source/codegen/jni/RNCSlider-generated.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/dfc4e1350bd27d7d0e423e38a8fd1f3a/components/RNCSlider/ComponentDescriptors.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/08d7e72103038234790cf2d9aa38e8e7/renderer/components/RNCSlider/EventEmitters.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/77fd0a957a73c497cf9ef944d6bb91cd/jni/react/renderer/components/RNCSlider/Props.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/dfc4e1350bd27d7d0e423e38a8fd1f3a/components/RNCSlider/RNCSliderJSI-generated.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/08d7e72103038234790cf2d9aa38e8e7/renderer/components/RNCSlider/ShadowNodes.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/c838027c612a797883778a7030e7c603/react/renderer/components/RNCSlider/States.cpp.o | C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  -latomic -lm
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_RNCSlider.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\intermediates\cxx\RelWithDebInfo\1a5c8433\obj\armeabi-v7a\libreact_codegen_RNCSlider.so
  TARGET_PDB = react_codegen_RNCSlider.so.dbg


#############################################
# Utility command for edit_cache

build RNCSlider_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a\RNCSlider_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNCSlider_autolinked_build/edit_cache: phony RNCSlider_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNCSlider_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a\RNCSlider_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNCSlider_autolinked_build/rebuild_cache: phony RNCSlider_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnskia


#############################################
# Order-only phony target for react_codegen_rnskia

build cmake_object_order_depends_target_react_codegen_rnskia: phony || rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir

build rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnskia_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnskia
  DEP_FILE = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir\react\renderer\components\rnskia\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir
  OBJECT_FILE_DIR = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir\react\renderer\components\rnskia

build rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnskia_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnskia
  DEP_FILE = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir\react\renderer\components\rnskia\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir
  OBJECT_FILE_DIR = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir\react\renderer\components\rnskia

build rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/Props.cpp.o: CXX_COMPILER__react_codegen_rnskia_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia/Props.cpp || cmake_object_order_depends_target_react_codegen_rnskia
  DEP_FILE = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir\react\renderer\components\rnskia\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir
  OBJECT_FILE_DIR = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir\react\renderer\components\rnskia

build rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnskia_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnskia
  DEP_FILE = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir\react\renderer\components\rnskia\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir
  OBJECT_FILE_DIR = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir\react\renderer\components\rnskia

build rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/States.cpp.o: CXX_COMPILER__react_codegen_rnskia_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia/States.cpp || cmake_object_order_depends_target_react_codegen_rnskia
  DEP_FILE = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir\react\renderer\components\rnskia\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir
  OBJECT_FILE_DIR = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir\react\renderer\components\rnskia

build rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/rnskiaJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnskia_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia/rnskiaJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnskia
  DEP_FILE = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir\react\renderer\components\rnskia\rnskiaJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir
  OBJECT_FILE_DIR = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir\react\renderer\components\rnskia

build rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/rnskia-generated.cpp.o: CXX_COMPILER__react_codegen_rnskia_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/rnskia-generated.cpp || cmake_object_order_depends_target_react_codegen_rnskia
  DEP_FILE = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir\rnskia-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir
  OBJECT_FILE_DIR = rnskia_autolinked_build\CMakeFiles\react_codegen_rnskia.dir



#############################################
# Object library react_codegen_rnskia

build rnskia_autolinked_build/react_codegen_rnskia: phony rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/ComponentDescriptors.cpp.o rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/EventEmitters.cpp.o rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/Props.cpp.o rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/ShadowNodes.cpp.o rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/States.cpp.o rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/react/renderer/components/rnskia/rnskiaJSI-generated.cpp.o rnskia_autolinked_build/CMakeFiles/react_codegen_rnskia.dir/rnskia-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnskia_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a\rnskia_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnskia_autolinked_build/edit_cache: phony rnskia_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnskia_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a\rnskia_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnskia_autolinked_build/rebuild_cache: phony rnskia_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rngesturehandler_codegen


#############################################
# Order-only phony target for react_codegen_rngesturehandler_codegen

build cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen: phony || rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\rngesturehandler_codegenJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\rngesturehandler_codegen-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir



#############################################
# Object library react_codegen_rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o


#############################################
# Utility command for edit_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a\rngesturehandler_codegen_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rngesturehandler_codegen_autolinked_build/edit_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a\rngesturehandler_codegen_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rngesturehandler_codegen_autolinked_build/rebuild_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Order-only phony target for react_codegen_safeareacontext

build cmake_object_order_depends_target_react_codegen_safeareacontext: phony || safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/782825489b2de6dd24b2ca840b493cca/RNCSafeAreaViewShadowNode.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\782825489b2de6dd24b2ca840b493cca\RNCSafeAreaViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\782825489b2de6dd24b2ca840b493cca

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/782825489b2de6dd24b2ca840b493cca/RNCSafeAreaViewState.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\782825489b2de6dd24b2ca840b493cca\RNCSafeAreaViewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\782825489b2de6dd24b2ca840b493cca

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8443cc8dc8ba497e2ecc8392eacca0ba/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\8443cc8dc8ba497e2ecc8392eacca0ba\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\8443cc8dc8ba497e2ecc8392eacca0ba

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0dfb3abc2da69bf3c1b6c958ae2ef096/safeareacontext/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\0dfb3abc2da69bf3c1b6c958ae2ef096\safeareacontext\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\0dfb3abc2da69bf3c1b6c958ae2ef096\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/90a06d5e9d2845d55ca8b5b4feec886a/components/safeareacontext/Props.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\90a06d5e9d2845d55ca8b5b4feec886a\components\safeareacontext\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\90a06d5e9d2845d55ca8b5b4feec886a\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0dfb3abc2da69bf3c1b6c958ae2ef096/safeareacontext/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\0dfb3abc2da69bf3c1b6c958ae2ef096\safeareacontext\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\0dfb3abc2da69bf3c1b6c958ae2ef096\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/90a06d5e9d2845d55ca8b5b4feec886a/components/safeareacontext/States.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\90a06d5e9d2845d55ca8b5b4feec886a\components\safeareacontext\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\90a06d5e9d2845d55ca8b5b4feec886a\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8443cc8dc8ba497e2ecc8392eacca0ba/safeareacontextJSI-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\8443cc8dc8ba497e2ecc8392eacca0ba\safeareacontextJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\8443cc8dc8ba497e2ecc8392eacca0ba

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/da0ceb25a6d18069770839fd3cb31944/jni/safeareacontext-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\da0ceb25a6d18069770839fd3cb31944\jni\safeareacontext-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\da0ceb25a6d18069770839fd3cb31944\jni


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Link the shared library D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\intermediates\cxx\RelWithDebInfo\1a5c8433\obj\armeabi-v7a\libreact_codegen_safeareacontext.so

build D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_safeareacontext.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_safeareacontext_RelWithDebInfo safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/782825489b2de6dd24b2ca840b493cca/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/782825489b2de6dd24b2ca840b493cca/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8443cc8dc8ba497e2ecc8392eacca0ba/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0dfb3abc2da69bf3c1b6c958ae2ef096/safeareacontext/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/90a06d5e9d2845d55ca8b5b4feec886a/components/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0dfb3abc2da69bf3c1b6c958ae2ef096/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/90a06d5e9d2845d55ca8b5b4feec886a/components/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8443cc8dc8ba497e2ecc8392eacca0ba/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/da0ceb25a6d18069770839fd3cb31944/jni/safeareacontext-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  -latomic -lm
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_safeareacontext.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\intermediates\cxx\RelWithDebInfo\1a5c8433\obj\armeabi-v7a\libreact_codegen_safeareacontext.so
  TARGET_PDB = react_codegen_safeareacontext.so.dbg


#############################################
# Utility command for edit_cache

build safeareacontext_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a\safeareacontext_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build safeareacontext_autolinked_build/edit_cache: phony safeareacontext_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a\safeareacontext_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/rebuild_cache: phony safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Order-only phony target for react_codegen_rnscreens

build cmake_object_order_depends_target_react_codegen_rnscreens: phony || rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/566b5fde03bdfc9ae37b7c403d9c407e/components/rnscreens/RNSModalScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\566b5fde03bdfc9ae37b7c403d9c407e\components\rnscreens\RNSModalScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\566b5fde03bdfc9ae37b7c403d9c407e\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/566b5fde03bdfc9ae37b7c403d9c407e/components/rnscreens/RNSScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\566b5fde03bdfc9ae37b7c403d9c407e\components\rnscreens\RNSScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\566b5fde03bdfc9ae37b7c403d9c407e\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ec60e197c268c25094c5f3e3f0fb63d4/RNSScreenStackHeaderConfigShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ec60e197c268c25094c5f3e3f0fb63d4\RNSScreenStackHeaderConfigShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ec60e197c268c25094c5f3e3f0fb63d4

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/503213f527c2ef03fceffc79113385ff/rnscreens/RNSScreenStackHeaderConfigState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\503213f527c2ef03fceffc79113385ff\rnscreens\RNSScreenStackHeaderConfigState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\503213f527c2ef03fceffc79113385ff\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ec60e197c268c25094c5f3e3f0fb63d4/RNSScreenStackHeaderSubviewShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ec60e197c268c25094c5f3e3f0fb63d4\RNSScreenStackHeaderSubviewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ec60e197c268c25094c5f3e3f0fb63d4

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/503213f527c2ef03fceffc79113385ff/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\503213f527c2ef03fceffc79113385ff\rnscreens\RNSScreenStackHeaderSubviewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\503213f527c2ef03fceffc79113385ff\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9a5103abd33c845dd2828a71ebbe6d85/renderer/components/rnscreens/RNSScreenState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\9a5103abd33c845dd2828a71ebbe6d85\renderer\components\rnscreens\RNSScreenState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\9a5103abd33c845dd2828a71ebbe6d85\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\rnscreens.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/13a34cfcd3b2d637eb9e74cb747ec491/components/rnscreens/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\13a34cfcd3b2d637eb9e74cb747ec491\components\rnscreens\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\13a34cfcd3b2d637eb9e74cb747ec491\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9a67e56fa1c110143ff4774556ca52c8/renderer/components/rnscreens/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\9a67e56fa1c110143ff4774556ca52c8\renderer\components\rnscreens\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\9a67e56fa1c110143ff4774556ca52c8\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/24af7573a39b142b5a15253f8ee44411/jni/react/renderer/components/rnscreens/Props.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\24af7573a39b142b5a15253f8ee44411\jni\react\renderer\components\rnscreens\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\24af7573a39b142b5a15253f8ee44411\jni\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9a67e56fa1c110143ff4774556ca52c8/renderer/components/rnscreens/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\9a67e56fa1c110143ff4774556ca52c8\renderer\components\rnscreens\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\9a67e56fa1c110143ff4774556ca52c8\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2f99b1dcdb07b3fe88f257f8e5cd06ce/react/renderer/components/rnscreens/States.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\2f99b1dcdb07b3fe88f257f8e5cd06ce\react\renderer\components\rnscreens\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\2f99b1dcdb07b3fe88f257f8e5cd06ce\react\renderer\components\rnscreens

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/13a34cfcd3b2d637eb9e74cb747ec491/components/rnscreens/rnscreensJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\13a34cfcd3b2d637eb9e74cb747ec491\components\rnscreens\rnscreensJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\13a34cfcd3b2d637eb9e74cb747ec491\components\rnscreens


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Link the shared library D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\intermediates\cxx\RelWithDebInfo\1a5c8433\obj\armeabi-v7a\libreact_codegen_rnscreens.so

build D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_rnscreens.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnscreens_RelWithDebInfo rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/566b5fde03bdfc9ae37b7c403d9c407e/components/rnscreens/RNSModalScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/566b5fde03bdfc9ae37b7c403d9c407e/components/rnscreens/RNSScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ec60e197c268c25094c5f3e3f0fb63d4/RNSScreenStackHeaderConfigShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/503213f527c2ef03fceffc79113385ff/rnscreens/RNSScreenStackHeaderConfigState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ec60e197c268c25094c5f3e3f0fb63d4/RNSScreenStackHeaderSubviewShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/503213f527c2ef03fceffc79113385ff/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9a5103abd33c845dd2828a71ebbe6d85/renderer/components/rnscreens/RNSScreenState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/13a34cfcd3b2d637eb9e74cb747ec491/components/rnscreens/ComponentDescriptors.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9a67e56fa1c110143ff4774556ca52c8/renderer/components/rnscreens/EventEmitters.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/24af7573a39b142b5a15253f8ee44411/jni/react/renderer/components/rnscreens/Props.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9a67e56fa1c110143ff4774556ca52c8/renderer/components/rnscreens/ShadowNodes.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2f99b1dcdb07b3fe88f257f8e5cd06ce/react/renderer/components/rnscreens/States.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/13a34cfcd3b2d637eb9e74cb747ec491/components/rnscreens/rnscreensJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnscreens.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\intermediates\cxx\RelWithDebInfo\1a5c8433\obj\armeabi-v7a\libreact_codegen_rnscreens.so
  TARGET_PDB = react_codegen_rnscreens.so.dbg


#############################################
# Utility command for edit_cache

build rnscreens_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a\rnscreens_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnscreens_autolinked_build/edit_cache: phony rnscreens_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a\rnscreens_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnscreens_autolinked_build/rebuild_cache: phony rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnsvg


#############################################
# Order-only phony target for react_codegen_rnsvg

build cmake_object_order_depends_target_react_codegen_rnsvg: phony || rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/7dfc6fdabe19c0d1e15c86d58337c85d/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\7dfc6fdabe19c0d1e15c86d58337c85d\react\renderer\components\rnsvg\RNSVGImageShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\7dfc6fdabe19c0d1e15c86d58337c85d\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/18b74893c726cdc94dbebd11f4529888/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\18b74893c726cdc94dbebd11f4529888\cpp\react\renderer\components\rnsvg\RNSVGImageState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\18b74893c726cdc94dbebd11f4529888\cpp\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/rnsvg.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\rnsvg.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/b467fa61720208f37ec4f3ff690d223b/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\b467fa61720208f37ec4f3ff690d223b\react\renderer\components\rnsvg\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\b467fa61720208f37ec4f3ff690d223b\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/72ce3b6f28029d404cd484e756fc3511/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\72ce3b6f28029d404cd484e756fc3511\jni\react\renderer\components\rnsvg\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\72ce3b6f28029d404cd484e756fc3511\jni\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/3ca25319e7abcd7beabd5a427bc75b84/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\3ca25319e7abcd7beabd5a427bc75b84\codegen\jni\react\renderer\components\rnsvg\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\3ca25319e7abcd7beabd5a427bc75b84\codegen\jni\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/72ce3b6f28029d404cd484e756fc3511/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\72ce3b6f28029d404cd484e756fc3511\jni\react\renderer\components\rnsvg\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\72ce3b6f28029d404cd484e756fc3511\jni\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/3ca25319e7abcd7beabd5a427bc75b84/codegen/jni/react/renderer/components/rnsvg/States.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\3ca25319e7abcd7beabd5a427bc75b84\codegen\jni\react\renderer\components\rnsvg\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\3ca25319e7abcd7beabd5a427bc75b84\codegen\jni\react\renderer\components\rnsvg

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/b467fa61720208f37ec4f3ff690d223b/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\b467fa61720208f37ec4f3ff690d223b\react\renderer\components\rnsvg\rnsvgJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/. -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\b467fa61720208f37ec4f3ff690d223b\react\renderer\components\rnsvg


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnsvg


#############################################
# Link the shared library D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\intermediates\cxx\RelWithDebInfo\1a5c8433\obj\armeabi-v7a\libreact_codegen_rnsvg.so

build D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_rnsvg.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnsvg_RelWithDebInfo rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/7dfc6fdabe19c0d1e15c86d58337c85d/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/18b74893c726cdc94dbebd11f4529888/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/b467fa61720208f37ec4f3ff690d223b/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/72ce3b6f28029d404cd484e756fc3511/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/3ca25319e7abcd7beabd5a427bc75b84/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/72ce3b6f28029d404cd484e756fc3511/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/3ca25319e7abcd7beabd5a427bc75b84/codegen/jni/react/renderer/components/rnsvg/States.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/b467fa61720208f37ec4f3ff690d223b/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/b0cc9e4ae2bfcac04a12a08baf379016/transformed/react-android-0.76.9-release/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/a6dd910b94089a928dab663afb856f4b/transformed/fbjni-0.6.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnsvg.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\intermediates\cxx\RelWithDebInfo\1a5c8433\obj\armeabi-v7a\libreact_codegen_rnsvg.so
  TARGET_PDB = react_codegen_rnsvg.so.dbg


#############################################
# Utility command for edit_cache

build rnsvg_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a\rnsvg_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnsvg_autolinked_build/edit_cache: phony rnsvg_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnsvg_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a\rnsvg_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\App\Delivery-Partner\Delivery-Partner-App\android\app\.cxx\RelWithDebInfo\1a5c8433\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnsvg_autolinked_build/rebuild_cache: phony rnsvg_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libappmodules.so

build libappmodules.so: phony D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libappmodules.so

build libreact_codegen_RNCSlider.so: phony D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_RNCSlider.so

build libreact_codegen_rnscreens.so: phony D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_rnscreens.so

build libreact_codegen_rnsvg.so: phony D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_rnsvg.so

build libreact_codegen_safeareacontext.so: phony D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_safeareacontext.so

build react_codegen_RNCSlider: phony D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_RNCSlider.so

build react_codegen_RNDateTimePickerCGen: phony RNDateTimePickerCGen_autolinked_build/react_codegen_RNDateTimePickerCGen

build react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

build react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

build react_codegen_rnscreens: phony D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_rnscreens.so

build react_codegen_rnskia: phony rnskia_autolinked_build/react_codegen_rnskia

build react_codegen_rnsvg: phony D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_rnsvg.so

build react_codegen_safeareacontext: phony D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_safeareacontext.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a

build all: phony D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libappmodules.so rnasyncstorage_autolinked_build/all RNDateTimePickerCGen_autolinked_build/all RNCSlider_autolinked_build/all rnskia_autolinked_build/all rngesturehandler_codegen_autolinked_build/all safeareacontext_autolinked_build/all rnscreens_autolinked_build/all rnsvg_autolinked_build/all

# =============================================================================

#############################################
# Folder: D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/RNCSlider_autolinked_build

build RNCSlider_autolinked_build/all: phony D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_RNCSlider.so

# =============================================================================

#############################################
# Folder: D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/RNDateTimePickerCGen_autolinked_build

build RNDateTimePickerCGen_autolinked_build/all: phony RNDateTimePickerCGen_autolinked_build/react_codegen_RNDateTimePickerCGen

# =============================================================================

#############################################
# Folder: D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnasyncstorage_autolinked_build

build rnasyncstorage_autolinked_build/all: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

# =============================================================================

#############################################
# Folder: D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rngesturehandler_codegen_autolinked_build

build rngesturehandler_codegen_autolinked_build/all: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

# =============================================================================

#############################################
# Folder: D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnscreens_autolinked_build

build rnscreens_autolinked_build/all: phony D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_rnscreens.so

# =============================================================================

#############################################
# Folder: D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnskia_autolinked_build

build rnskia_autolinked_build/all: phony rnskia_autolinked_build/react_codegen_rnskia

# =============================================================================

#############################################
# Folder: D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/rnsvg_autolinked_build

build rnsvg_autolinked_build/all: phony D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_rnsvg.so

# =============================================================================

#############################################
# Folder: D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/safeareacontext_autolinked_build

build safeareacontext_autolinked_build/all: phony D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/intermediates/cxx/RelWithDebInfo/1a5c8433/obj/armeabi-v7a/libreact_codegen_safeareacontext.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/CMakeFiles/cmake.verify_globs | C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake D$:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt D$:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
