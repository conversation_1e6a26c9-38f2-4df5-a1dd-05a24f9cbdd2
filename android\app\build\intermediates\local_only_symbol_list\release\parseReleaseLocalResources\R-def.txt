R_DEF: Internal format may change without notice
local
color colorPrimary
color colorPrimaryDark
color iconBackground
color splashscreen_background
drawable assets_hometab_qrimage
drawable assets_images_image
drawable assets_images_slidimage_frame1321317908
drawable assets_images_slidimage_layer_2
drawable assets_loading_ordalanetransparentlogo03
drawable assets_paymentimg_success
drawable assets_profile_bg
drawable assets_profile_calenderisocolor
drawable assets_profile_contactlogo
drawable assets_profileasser_sectionicon_refer
drawable ic_launcher_background
drawable node_modules_exporouter_assets_error
drawable node_modules_exporouter_assets_file
drawable node_modules_exporouter_assets_forward
drawable node_modules_exporouter_assets_pkg
drawable node_modules_exporouter_assets_sitemap
drawable node_modules_exporouter_assets_unmatched
drawable node_modules_reactnativeelementdropdown_src_assets_close
drawable node_modules_reactnativeelementdropdown_src_assets_down
drawable node_modules_reactnavigation_elements_lib_module_assets_backicon
drawable node_modules_reactnavigation_elements_lib_module_assets_backiconmask
drawable node_modules_reactnavigation_elements_lib_module_assets_clearicon
drawable node_modules_reactnavigation_elements_lib_module_assets_closeicon
drawable node_modules_reactnavigation_elements_lib_module_assets_searchicon
drawable rn_edit_text_material
drawable splashscreen_logo
integer react_native_dev_server_port
mipmap ic_launcher
mipmap ic_launcher_foreground
mipmap ic_launcher_round
raw assets_font_poppinsregular
string app_name
string expo_splash_screen_resize_mode
string expo_splash_screen_status_bar_translucent
string expo_system_ui_user_interface_style
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string project_id
style AppTheme
style ResetEditText
style Theme.App.SplashScreen
