import { AxiosInstance, AxiosRequestConfig } from "axios";

export interface GetDataType<T> {
  data: T;
  status: boolean;
  msg: string;
}

export interface Api {
  _api: AxiosInstance;
  setData<T, P>(endpoint: string, pram?: P): Promise<GetDataType<T>>;
  gatData<T, P = any>(
    endpoint: string,
    pram?: P,
    headers?: AxiosRequestConfig<P>
  ): Promise<GetDataType<T>>;
}

export class ApiImpl implements Api {
  _api: AxiosInstance;
  constructor(axiosIns: AxiosInstance) {
    this._api = axiosIns;
  }
  async setData<T, P>(
    endpoint: string,
    pram?: P,
    headers?: AxiosRequestConfig<P> | undefined
  ): Promise<GetDataType<T>> {
    const responce = await this._api
      .post(endpoint, pram, headers)
      .then((val) => {
        if (val?.data?.status === 1 || val?.data?.status === 200) {
          return {
            data: val.data,
            status: true,
            msg: "Data Fetch Successfully",
          };
        }
        return { data: val.data, status: false, msg: "Data Fetch Failed" };
      })
      .catch((error) => {
        console.log(error, "set API error");
        return { data: error, status: false, msg: "Data Fetch Failed" };
      });
    return responce;
  }
  async gatData<T, P>(endpoint: string, pram?: P): Promise<GetDataType<T>> {
    const responce = await this._api
      .post(endpoint, pram, {
        headers: {
          "X-Powered-By": "Express",
          "Access-Control-Allow-Origin": "*",
          "Content-Type": "application/json",
        },
      })
      .then((val) => {
        if (val.data?.status === 1 || val?.status === 200) {
          return {
            data: val.data,
            status: true,
            msg: "Data Fetch Successfully",
          };
        }
        return { data: val.data, status: false, msg: "Data Fetch Failed" };
      })
      .catch((error) => {
        console.log(error, "Get APi error");
        return { data: error, status: false, msg: "Data Fetch Failed" };
      });
    return responce;
  }
}
