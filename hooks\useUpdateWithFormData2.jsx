import AsyncStorage from "@react-native-async-storage/async-storage";
import Constants from "expo-constants";
import axios from "axios";
import { useEffect, useState } from "react";
import { axiosInstance } from "@/api/axios";

const useUpdateWithFormData2 = ({ endpoint }) => {
  const [data, setdata] = useState(null);
  const [isLoading, setLoading] = useState(false);
  const UpdateBankDetails = async (datas) => {
    try {
      await setLoading(true);
      const token = await AsyncStorage.getItem("login");

      if (!token) {
        throw new Error("Authentication token is missing");
      }

      const response = await axiosInstance.post(
        `${Constants.expoConfig?.extra?.BASE_URL}${endpoint}`,
        datas,
        {
          headers: {
            "X-Powered-By": "Express",
            "Access-Control-Allow-Origin": "*",
            "content-type": "multipart/form-data",
            Authorization: token,
          },
        }
      );

      if (!response?.data) {
        throw new Error("Empty response from server");
      }
      console.log(response.data);
      setdata(response.data);
      return response.data;
    } catch (error) {
      console.error("API Error:", error.response?.data || error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return { isLoading, data, UpdateBankDetails };
};

export default useUpdateWithFormData2;
