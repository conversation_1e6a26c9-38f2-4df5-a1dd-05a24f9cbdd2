import { OrderListModel } from "@/data/model/order_model/order_list_model";
import { OrderRepository } from "@/domain/repository/order";

export class OrderUseCase {
  orderRepository: OrderRepository;
  constructor(orderRepository: OrderRepository) {
    this.orderRepository = orderRepository;
  }
  async getOrders(param: { id: number }): Promise<OrderListModel[]> {
    try {
      return await this.orderRepository.getOrders(param);
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
