import { create } from "zustand";

export const useOrder = create((set) => ({
  OrdersData: [
    {
      id: 1,
      mode: "Online",
      statusstate: "Pending",
    },
    {
      id: 2,
      mode: "COD",
      statusstate: "Pending",
    },
  ],
  setorderStatus: (id, status) => {
    set((state) => ({
      OrdersData: state.OrdersData.map((item) => {
        if (id == item.id) {
          return { ...item, statusstate: status };
        }
        return item;
      }),
    }));
  },
}));

export const useOrderState = create((set) => ({
  PickUpLocationImage: undefined,
  SetOrderPickedUpImage: (image) => {
    set(() => ({
      PickUpLocationImage: image,
    }));
  },
}));
export const useNotificationList = create((set) => ({
  notificationList: undefined,
  SetNotificationList: (notification) => {
    console.log(notification)
    set(() => ({
      notificationList: notification,
    }));
  },
}));
