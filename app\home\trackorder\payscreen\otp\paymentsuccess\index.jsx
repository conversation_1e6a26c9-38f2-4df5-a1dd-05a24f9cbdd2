import { View, Text, TouchableOpacity, Image } from "react-native";
import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import BackArrow from "@/assets/icons/BackArrow.svg";

const index = () => {
  return (
    <SafeAreaView className="flex-1">
      <View className="px-4 mt-3">
        <View className="flex-row relative items-center justify-start mt-4">
          <TouchableOpacity
            onPress={() => {
              router.back();
            }}
            className="absolute"
          >
            <View>
              <BackArrow />
            </View>
          </TouchableOpacity>
        </View>
      </View>
      <View className="px-6 flex-1 items-center justify-center">
        <Text className="font-[500] font-Pop text-[32px] text-[#001A03] leading-[48px]">
          Payment Confirmed !
        </Text>
        <View className="flex-row">
          <Text className="flex-1 text-center mt-4 font-[500] font-Pop text-[18px] leading-[27px]">
            You have completed step 2, payment has been successfully done
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default index;
