import DropDownComponent from "../../../../component/DropDownComponent";
import React, { useContext, useEffect, useState } from "react";
import TextinputFile from "../../../../component/TextinputFile";
import useGetApiData from "../../../../hooks/useGetApiData";
import useSetApiData from "../../../../hooks/useSetApiData";
import useTenStackHook from "@/hooks/TenStackHook/TenStackHook";
import useTenStackMutate from "@/hooks/useTenStackMutate/useTenStackMutate";
import { DropDownComponent2 } from ".";
import { router } from "expo-router";
import { useForm } from "react-hook-form";
import { Controller } from "react-hook-form";
import { Text, TextInput, View } from "react-native";
import { TouchableOpacity } from "react-native";
import { Alert } from "react-native";

const page2 = () => {
  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors, isValid },
    watch,
  } = useForm();

  const { mutate } = useTenStackMutate({
    invalidateQueriesKey: ["status"], // Update this to invalidate bank details cache
    endpoint: "auth/add_bank_details",
  });
  const { data: bank_list, isLoading } = useTenStackHook({
    endpoint: "bank/bank_list",
    key: "bank_list",
    canSave: true,
  });

  const Submit = () => {
    handleSubmit((data) => {
      mutate(
        {
          account_no: data?.account,
          bank_name: data?.bankname,
          ifsc_code: data?.IFSC,
        },
        {
          onSuccess: (response) => {
            console.log("Bank details response:", response); // Add logging
            if (response?.msg === "Bank details added successfully!") {
              router.push({
                pathname: "/auth/createprofile/profilecompleted/completed",
              });
            } else {
              // Handle error case
              Alert.alert(
                "Error",
                response?.message || "Failed to update bank details"
              );
            }
          },
          onError: (error) => {
            console.error("Bank details error:", error);
            Alert.alert("Error", "Failed to update bank details");
          },
        }
      );
    })();
  };
  console.log(errors?.reaccount?.message);
  return (
    <View className="">
      <View className="mt-6">
        <Text className="font-Pop font-[400] text-[18px] leading-[27px]">
          Provide your bank details
        </Text>
        <Text className="font-Pop font-[400] text-[14px] leading-[21px]">
          Provide your bank details
        </Text>
      </View>

      <View className="mt-4 items-start">
        {/* Account Number TextField Area */}
        <Text className="font-[400] text-[16px] leading-[20px] font-Pop">
          Account Number
        </Text>
        <TextinputFile2
          placeholder={""}
          control={control}
          name={"account"}
          rules={{
            required: {
              value: true,
              message: "Enter Account Number",
            },
          }}
        />
        {errors.account && (
          <Text className="text-red-500 text-center">
            {(errors as any).account.message}
          </Text>
        )}
      </View>

      <View className="mt-4 items-start">
        {/* Re-enter Account TextField Area */}
        <Text className="font-[400] text-[16px] leading-[20px] font-Pop">
          Re-enter Account Number
        </Text>

        <TextinputFile2
          placeholder={""}
          control={control}
          rules={{
            required: {
              value: true,
              message: "Enter Re-Account Number",
            },
            validate: (value) =>
              watch("account") == value || "Account Number do not match",
          }}
          name={"reaccount"}
        />
        {errors.reaccount && (
          <Text className="text-red-500 text-center">
            {(errors as any).reaccount.message}
          </Text>
        )}
      </View>

      <View className="">
        {/* Bank Name TextField Area */}
        {isLoading ? (
          <></>
        ) : (
          <>
            <View className="">
              <DropDownComponent
                control={control}
                data={bank_list?.data}
                name={"bankname"}
                placeholder={"Select Bank Name"}
                setvaluefun={(val) => {
                  setValue("bankname", val);
                }}
                text={"Bank Name"}
                key={1}
              />
            </View>
          </>
        )}
      </View>

      <View className="mt-6 items-start">
        {/* Pan card TextField Area */}
        <Text className="font-[400] text-[16px] text-[#4D4D4D]">
          Enter IFSC code
        </Text>
        <TextinputFile2
          placeholder={""}
          control={control}
          name={"IFSC"}
          rules={{
            required: {
              value: true,
              message: "IFSC code is required.",
            },
            minLength: {
              value: 11,
              message: "IFSC code must be 11 characters",
            },
            maxLength: {
              value: 11,
              message: "IFSC code must be 11 characters",
            },
            pattern: {
              value: /^[A-Z]{4}0[A-Z0-9]{6}$/,
              message: "Please enter a valid IFSC code (e.g., SBIN0123456)",
            },
          }}
        />
        {errors.IFSC && (
          <Text className="text-red-500 text-center">
            {(errors as any).IFSC.message}
          </Text>
        )}
      </View>
      <View className="m-2 px-4 mt-24">
        <TouchableOpacity
          style={{
            opacity: isValid ? 1 : 0.7,
          }}
          onPress={() => {
            Submit();
          }}
          className="h-[44px] items-center justify-center bg-[#00660A]"
        >
          <Text className="font-[400] text-[#fff] leading-[24px] font-Pop">
            Verify Bank Details
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default page2;

const TextinputFile2 = (props) => {
  return (
    <>
      <View
        className={
          "px-2 border-[1px] border-[#ACB9D5] h-[40px] items-center flex-row mt-2 rounded-[4px] justify-between " +
          props.containerstyle
        }
      >
        <Controller
          control={props?.control}
          name={props?.name}
          rules={{
            ...props?.rules,
          }}
          render={({ field: { onChange, onBlur, value } }) => (
            <TextInput
              value={value}
              onChangeText={(value) => onChange(value)}
              style={{
                color: "#000",
                flex: 1,
              }}
              className={props.inputstyle}
              defaultValue={props?.placeholder}
            />
          )}
        />
        <TouchableOpacity>{props.component}</TouchableOpacity>
      </View>
    </>
  );
};
