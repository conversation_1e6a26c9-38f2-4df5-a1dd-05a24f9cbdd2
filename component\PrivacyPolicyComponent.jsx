import { View, Text, TouchableOpacity } from "react-native";
import React from "react";
import { router } from "expo-router";

const PrivacyPolicyComponent = () => {
  return (
    <View className="flex-row flex-wrap text-center justify-center">
      <View>
        <Text className="font-[400] text-[14px] text-center leading-[21px] text-[#fff]">
          By joining Ordalane you agree to our
        </Text>
      </View>
      <View className="flex-row">
        <TouchableOpacity
          onPress={() => {
            router.push("auth/conditions");
          }}
        >
          <Text className="font-Pop font-[500] text-[14px] text-center leading-[21px] text-[#FFD401]">
            Terms of services
          </Text>
        </TouchableOpacity>
        <Text className="font-Pop font-[400] text-[14px] text-center leading-[21px] text-[#fff]">
          {" "}
          and{" "}
        </Text>
        <TouchableOpacity
          onPress={() => {
            router.push("auth/conditions");
          }}
        >
          <Text className="font-Pop font-[500] text-[14px] text-center leading-[21px] text-[#FFD401]">
            Privacy Policy
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default PrivacyPolicyComponent;
