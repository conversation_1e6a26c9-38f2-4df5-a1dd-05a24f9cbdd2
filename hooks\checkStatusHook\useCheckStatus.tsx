import React from "react";
import { useQuery } from "@tanstack/react-query";
import { axiosInstance } from "@/api/axios";
import { ApiImpl } from "@/data/source";

const useCheckStatus = () => {
  const api = new ApiImpl(axiosInstance);
  const CheckWorkStatus = async <T,>(endpoint: string) => {
    const data = await api.gatData<T, null>(endpoint);
    return data;
  };

  const statusData = useQuery({
    queryKey: ["status"],
    queryFn: async () => {
      const [workStatus, profileStatus] = await Promise.all([
        CheckWorkStatus<{
          message: string;
          status: number;
          emptyFields: string[] | null;
        }>("/work_status").then((val) => {
          console.log(val);
          return val;
        }),
        CheckWorkStatus<{
          message: string;
          status: number;
          emptyFields: string[] | null;
        }>("/profile_status").then((val) => {
          console.log(val);
          return val;
        }),
      ]);
      return {
        workStatus,
        profileStatus,
      };
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  });

  return statusData;
};

export default useCheckStatus;
