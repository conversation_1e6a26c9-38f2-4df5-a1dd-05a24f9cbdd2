import React from "react";
import { Text, TouchableOpacity, View } from "react-native";

const ButtonComponent = (props) => {
  return (
    <TouchableOpacity
      disabled={props?.disabled}
      onPress={props?.pressfun}
      style={{
        opacity: props?.value ? 1 : 0.5,
        ...props?.style,
      }}
      className="w-full h-[51px] justify-center items-center bg-[#00660A]"
    >
      <Text className="font-[400] text-[18px] leading-[27px] text-[#fff]">
        {props?.text}
      </Text>
    </TouchableOpacity>
  );
};

export default ButtonComponent;
