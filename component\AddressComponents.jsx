import { View, Text, TouchableOpacity } from "react-native";
import React from "react";

const AddressComponents = ({
  icon,
  km,
  name,
  address,
  number,
  forwordIcon,
}) => {
  return (
    <>
      <View className="px-4">
        <View className="flex-row items-start justify-between p-4 border-[#E9EAE9] border-[1px]">
          <View className="flex-row items-start space-x-3">
            <View>
              {icon}
              <Text>{km}</Text>
            </View>
            <View className="space-y-1">
              <Text className="font-[600] text-[16px] text-[#00660A]">
                {name}
              </Text>
              <View className="flex-row">
                <Text className="flex-2">{address}</Text>
              </View>
              {number && <Text className="mb-2">Phone Number: {number}</Text>}

              <TouchableOpacity className="">{forwordIcon}</TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </>
  );
};

export default AddressComponents;
