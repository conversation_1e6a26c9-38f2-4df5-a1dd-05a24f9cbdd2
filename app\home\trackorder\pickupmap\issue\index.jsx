import { View, Text, TouchableOpacity, Safe<PERSON>reaView } from "react-native";
import React from "react";
import { router } from "expo-router";
import BackArrow from "@/assets/icons/BackArrow.svg";
import HelperFieldComponent from "../../../../../component/HelperFieldComponent/HelperFieldComponent";

const index = () => {
  return (
    <SafeAreaView>
      <View className="px-4 mt-4">
        <View className="flex-row relative items-center justify-start mt-4">
          <TouchableOpacity
            onPress={() => {
              router.back();
            }}
            className="absolute"
          >
            <View>
              <BackArrow />
            </View>
          </TouchableOpacity>
          <View className="flex-1 items-center justify-center">
            <Text className="font-[400] text-[19px] leading-[39px]">
              Report issue for #87792759572
            </Text>
          </View>
        </View>
      </View>
      <View className="mt-8">
        <HelperFieldComponent
          text={"I want to unassign my order"}
          pressfun={() => {}}
        />
      </View>
      <View className="mt-2">
        <HelperFieldComponent
          text={"I am unable to locate the restaurant"}
          pressfun={() => {}}
        />
      </View>
      <View className="mt-2">
        <HelperFieldComponent
          text={"I am unable to mark arrive"}
          pressfun={() => {}}
        />
      </View>
      <View className="mt-2">
        <HelperFieldComponent
          text={"Customer asked to edit/cancel order"}
          pressfun={() => {}}
        />
      </View>
      <View className="mt-2">
        <HelperFieldComponent
          text={"Restaurant is closed"}
          pressfun={() => {}}
        />
      </View>
      <View className="mt-2">
        <HelperFieldComponent
          text={"Restaurant is delaying order"}
          pressfun={() => {}}
        />
      </View>
    </SafeAreaView>
  );
};

export default index;
