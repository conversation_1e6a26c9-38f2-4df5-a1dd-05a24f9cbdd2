import React, { useState, useEffect, useRef } from "react";
import { Text, View, TouchableOpacity, StyleSheet } from "react-native";
import { Camera } from "expo-camera";
import { SafeAreaView } from "react-native-safe-area-context";
import BackArrow from "@/assets/icons/BackArrow.svg";
import { router, useLocalSearchParams } from "expo-router";
import CameraIcon from "@/assets/Hometab/mdi_camera.svg";
import ArrowIcon from "@/assets/Hometab/ph_arrow-right-bold.svg";
import { useOrderState } from "../../../../../store/Order";

const index = () => {
  const [hasPermission, setHasPermission] = useState(null);
  const [type, setType] = useState(Camera.Constants.Type.back);
  const cameraRef = useRef(null);
  const { id, invoice_no, orderNumber } = useLocalSearchParams();
  const { SetOrderPickedUpImage } = useOrderState((state) => state);

  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === "granted");
    })();
  }, []);
  const takePicture = async () => {
    if (cameraRef.current) {
      const photo = await cameraRef.current.takePictureAsync();
      SetOrderPickedUpImage(photo);
    }
  };

  if (hasPermission === null) {
    return <View />;
  }
  if (hasPermission === false) {
    return <Text>No access to camera</Text>;
  }
  return (
    <SafeAreaView className="flex-1">
      <View className="px-4 my-4">
        <View className="flex-row relative items-center justify-start mt-4">
          <TouchableOpacity
            onPress={() => {
              router.back();
            }}
            className="absolute"
          >
            <View>
              <BackArrow />
            </View>
          </TouchableOpacity>
          <View className="flex-1 items-center justify-center">
            <Text className="font-[400] text-[19px] leading-[39px]">
              Take picture of drop off location
            </Text>
          </View>
        </View>
      </View>
      <View className="flex-1">
        <Camera style={styles.camera} type={type} ref={cameraRef}>
          <View
            style={styles.buttonContainer}
            className="justify-between items-end"
          >
            {/* <TouchableOpacity
              style={styles.flipButton}
              onPress={() => {
                setType(
                  type === Camera.Constants.Type.back
                    ? Camera.Constants.Type.front
                    : Camera.Constants.Type.back
                );
              }}
            >
              <Text style={styles.text}> Flip </Text>
            </TouchableOpacity> */}
            <TouchableOpacity
              className="w-[64px] h-[64px] items-center justify-center bg-[#424242] rounded-full"
              onPress={takePicture}
            >
              <CameraIcon />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                router.push({
                  pathname: "home/trackorder/dropoffmap/camara/otp",
                  params: {
                    id: id,
                    invoice_no: invoice_no,
                    orderNumber: orderNumber,
                  },
                });
              }}
              className="w-[104px] h-[56px] items-center justify-center rounded-[41px] bg-[#00660A]"
            >
              <ArrowIcon />
            </TouchableOpacity>
          </View>
        </Camera>
      </View>
    </SafeAreaView>
  );
};

export default index;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  buttonContainer: {
    flex: 1,
    backgroundColor: "transparent",
    flexDirection: "row",
    margin: 20,
    gap: 20,
  },
  button: {
    flex: 0.1,
    alignSelf: "flex-end",
    alignItems: "center",
  },
  text: {
    fontSize: 18,
    color: "#000",
  },
  flipButton: {
    flex: 0.3,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#fff",
    borderRadius: 5,
  },
  captureButton: {
    flex: 0.3,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#fff",
    borderRadius: 5,
  },
});
