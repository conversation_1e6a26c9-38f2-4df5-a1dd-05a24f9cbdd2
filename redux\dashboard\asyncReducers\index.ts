import { createAsyncThunk } from "@reduxjs/toolkit";
import { axiosInstance } from "@/api/axios";
import { DashboardRepositoryimpl } from "@/data/repository/dashboard_impl";
import { ApiImpl } from "@/data/source";
import { DashboardUseCase } from "@/domain/usecase/dashboardusecase";

const dashboardusecase = new DashboardUseCase(
  new DashboardRepositoryimpl(new ApiImpl(axiosInstance))
);

export const loadDashboard = createAsyncThunk(
  "dashboard/loadDashboard",
  async (_, { dispatch }) => {
    try {
      const data = await dashboardusecase.getDashboardData();
      return data;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
);
