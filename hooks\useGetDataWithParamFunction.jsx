import AsyncStorage from "@react-native-async-storage/async-storage";
import Constants from "expo-constants";
import axios from "axios";
import { useEffect, useState } from "react";

const useGetDataWithParam = (endpoint) => {
  const [isLoading, setLoading] = useState(false);
  let APIdata;
  const FetchFunction = async (param) => {
    setLoading(true);
    const token = await AsyncStorage.getItem("login");
    await axios
      .post(`${Constants.expoConfig?.extra?.BASE_URL}${endpoint}`, param, {
        headers: {
          "X-Powered-By": "Express",
          "Access-Control-Allow-Origin": "*",
          "Content-Type": "application/json",
          Authorization: token,
        },
      })
      .then((val) => {
        return val.data;
      })
      .then((datas) => {
        APIdata = datas;
        return datas;
      })
      .catch((error) => {
        console.error(error.response ? error.response.data : error.message);
      });
    setLoading(false);
    return APIdata;
  };

  return { isLoading, FetchFunction };
};

export default useGetDataWithParam;
