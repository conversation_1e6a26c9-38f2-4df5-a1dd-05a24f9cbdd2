import { View, Text, TouchableOpacity } from "react-native";
import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import BackIcon from "@/assets/icons/BackArrow.svg";
import { SlideComponent } from ".";

const generalissues = () => {
  return (
    <SafeAreaView className="flex-1">
      <View className="items-center justify-center relative mt-6">
        <TouchableOpacity
          className="absolute left-[3%]"
          onPress={() => {
            router.back();
          }}
        >
          <BackIcon />
        </TouchableOpacity>
        <View>
          <Text className="font-[600] text-[20px] leading-[30px]">
            General Issues
          </Text>
        </View>
      </View>
      <View className="mt-8">
        <SlideComponent text={"What is Ordalane customer care number "} />
      </View>
      <View className="">
        <SlideComponent
          text={"I am unable to find the restaurant i’m looking for"}
        />
      </View>
      <View className="">
        <SlideComponent
          text={"I am unable to place a cash on delivery order"}
        />
      </View>
      <View className="">
        <SlideComponent text={"I did not receive OTP on SMS"} />
      </View>
      <View className="">
        <SlideComponent
          text={"My payment was deducted but the order was not processed"}
        />
      </View>
      <View className="">
        <SlideComponent
          text={"I have a query/issue regarding the bill amount"}
        />
      </View>
      <View className="">
        <SlideComponent text={"I want an invoice for my order"} />
      </View>
    </SafeAreaView>
  );
};

export default generalissues;
