import { View, Text, ScrollView, TouchableOpacity } from "react-native";
import React from "react";
import LearnCardComponent from "../../../../../component/LearnCardComponent/LearnCardComponent";
import { SafeAreaView } from "react-native-safe-area-context";
import BackArrow from "@/assets/icons/BackArrow.svg";
import { router } from "expo-router";
import useGetApiData from "../../../../../hooks/useGetApiData";

const learn = () => {
  const { data, isLoading } = useGetApiData({
    endpoint: "auth/articleList",
  });
  console.log(data);
  return (
    <SafeAreaView>
      <ScrollView>
        <View className="px-4 mt-3">
          <View className="flex-row relative items-center justify-start mt-4">
            <TouchableOpacity
              onPress={() => {
                router.back();
              }}
              className="absolute"
            >
              <View>
                <BackArrow />
              </View>
            </TouchableOpacity>
            <View className="flex-1 items-center justify-center">
              <Text className="font-[400] text-[26px] leading-[39px]">
                Learn with us
              </Text>
            </View>
          </View>
        </View>
        <View>
          {isLoading ? (
            <></>
          ) : (
            <>
              <View className="mt-3 px-4">
                {data?.data?.map((article) => {
                  return (
                    <LearnCardComponent data={article} key={article?.id} />
                  );
                })}
              </View>
            </>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default learn;
