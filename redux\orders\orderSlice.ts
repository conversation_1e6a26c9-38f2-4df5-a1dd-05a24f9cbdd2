import { createSlice } from "@reduxjs/toolkit";
import { loadOrders } from "./asyncReducers";
import { reducers } from "./reducers";
import { OrderListModel } from "@/data/model/order_model/order_list_model";

export interface OrdersState {
  orders: OrderListModel[];
}

const initialState: OrdersState = {
  orders: [],
};

const OrdersSlice = createSlice({
  name: "orders",
  initialState: initialState,
  reducers: {
    ...reducers,
  },
  extraReducers: (builder) => {
    builder.addCase(loadOrders.fulfilled, (state, action) => {
      state.orders = action.payload;
    });
  },
});

export const {} = OrdersSlice.actions;
export default OrdersSlice.reducer;
