import ButtonComponent from "@/component/ButtonComponent";
import LinearGradientComponent from "@/component/LinearGradientComponent";
import OtpTextInput from "react-native-text-input-otp";
import PrivacyPolicyComponent from "@/component/PrivacyPolicyComponent";
import React, { useEffect, useState } from "react";
import useGetApiDatawithParam from "../../../../../../hooks/useGetApiDatawithParam";
import useUpdateWithFormData2 from "../../../../../../hooks/useUpdateWithFormData2";
import { Video } from "expo-av";
import { router, useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { useOrderState } from "../../../../../../store/Order";
import { useLogin } from "@/store";

import {
  View,
  Text,
  TouchableOpacity,
  Keyboard,
  TouchableWithoutFeedback,
} from "react-native";

const otp = () => {
  const { invoice_no, id, orderNumber } = useLocalSearchParams();
  const [otp, setOtp] = useState("");
  const [Isotpfilled, setotpfilled] = useState(false);
  const { PickUpLocationImage } = useOrderState((state) => state);

  const { data, isLoading, triggerreFresh } = useGetApiDatawithParam({
    endpoint: "order/invoice_details",
    param: {
      invoice_no: invoice_no,
    },
  });

  const { UpdateBankDetails } = useUpdateWithFormData2({
    endpoint: "order/dropoff_submit",
  });
  const formdatas = new FormData();

  const check = () => {
    if (otp.length == 4) {
      setotpfilled(true);
    } else {
      setotpfilled(false);
    }
  };

  useEffect(() => {
    check();
  }, [otp]);
  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <SafeAreaView
        style={{
          flex: 1,
          position: "relative",
          justifyContent: "space-between",
        }}
      >
        {/* LinearGradient Component Area*/}
        <LinearGradientComponent />
        <View className="z-10 px-6 flex-1">
          {/* Login Title Area */}
          <View className="items-center justify-end h-[24%] mb-8 gap-3">
            <View>
              <Text className="font-[400] text-[26px] text-white">
                Enter OTP
              </Text>
            </View>
            <View className="flex-row items-center justify-center">
              <Text className="font-[400] text-[18px] text-white">
                Enter OTP given by the customer
              </Text>
            </View>
          </View>

          {/* Otp Field Area */}
          <View className="items-center justify-center">
            <View
              style={{
                maxWidth: "90%",
              }}
            >
              <OtpTextInput
                otp={otp}
                setOtp={setOtp}
                digits={4}
                style={{
                  borderRadius: 0,
                  borderTopWidth: 0,
                  borderRightWidth: 0,
                  borderLeftWidth: 0,
                  height: 45,
                }}
                fontStyle={{ fontSize: 26, fontWeight: "bold", color: "#fff" }}
                focusedStyle={{ borderColor: "#5cb85c", borderBottomWidth: 2 }}
              />
            </View>
          </View>

          {/* Button Area */}
          <View
            className=""
            style={{
              flex: 0.4,
              justifyContent: "flex-end",
            }}
          >
            <ButtonComponent
              text="Verify OTP"
              value={Isotpfilled}
              pressfun={() => {
                formdatas.append("invoice_no", invoice_no.toString());
                formdatas.append("shop_id", id.toString());
                formdatas.append("otp", otp);
                formdatas.append(
                  "customer_id",
                  data?.deliveryAddress?.user_id.toString() ?? ""
                );
                formdatas.append("file", {
                  uri: videoUri,
                  type: "video/mp4",
                });
                if (Isotpfilled) {
                  UpdateBankDetails(formdatas).then((val) => {
                    if (val.msg === "Dropoff image submitted!!") {
                      router.replace({
                        pathname: "home/trackorder",
                        params: {
                          invoice_no: invoice_no,
                          orderNumber: orderNumber,
                        },
                      });
                    }
                  });
                }
              }}
            />
          </View>
        </View>
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
};

export default otp;
