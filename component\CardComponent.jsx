import { View, Text, Image, TouchableOpacity } from "react-native";
import React from "react";
import RsIcon from "@/assets/images/CardImages/RsIcon.svg";
import StarIcon from "@/assets/images/CardImages/stareIcon.svg";
import CardImage from "@/assets/images/CardImages/Rectangle 592762.png";
import { router } from "expo-router";

const CardComponent = (props) => {
  return (
    <TouchableOpacity
      onPress={() => {
        router.push({
          pathname: `home/cart/${props?.id}`,
          params: {
            name: props?.resname,
            reating: props?.reating,
            ...props,
          },
        });
      }}
      className="flex-row items-start justify-center space-x-5 mt-4"
    >
      <View>
        <Image source={props?.CardImage} />
      </View>
      <View className="space-y-1">
        <View>
          <Text>{props?.resname}</Text>
        </View>
        <View className="flex-row items-center space-x-4">
          <View className="flex-row items-center bg-[#00660A] p-1 space-x-1 rounded-[4px]">
            <View>
              <Text className="text-[#fff]">{props?.reating}</Text>
            </View>
            <StarIcon />
          </View>
          <View>
            <Text>{props?.time}</Text>
          </View>
        </View>
        <View className="flex-row min-w-[50%]">
          <Text
            style={{
              flex: 1,
            }}
          >
            {props?.descripition}
          </Text>
        </View>
        <View className="flex-row items-center space-x-6">
          <Text>{props?.address}</Text>
          <Text>{props?.km}</Text>
        </View>
        <View className="flex-row items-center space-x-2">
          <RsIcon />
          <Text>{props?.amount}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default CardComponent;
