import { createAsyncThunk } from "@reduxjs/toolkit";
import { axiosInstance } from "@/api/axios";
import { OrderRepositoryimpl } from "@/data/repository/order_impl";
import { UserRepositoryimpl } from "@/data/repository/user_impl";
import { ApiImpl } from "@/data/source";
import { OrderUseCase } from "@/domain/usecase/order";
import { UserUseCase } from "@/domain/usecase/userusecase";

const ordersusecase = new OrderUseCase(
  new OrderRepositoryimpl(new ApiImpl(axiosInstance))
);

export const loadOrders = createAsyncThunk(
  "orders/loadOrders",
  async (param: { id: number }, { dispatch }) => {
    console.log(param.id);
    try {
      const data = await ordersusecase.getOrders(param);
      return data;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
);
