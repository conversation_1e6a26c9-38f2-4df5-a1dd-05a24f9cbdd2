1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mohandhass2.myapp"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
11-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:2:3-78
11-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:2:20-76
12    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
12-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:3:3-76
12-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:3:20-74
13    <uses-permission android:name="android.permission.CAMERA" />
13-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:4:3-62
13-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:4:20-60
14    <uses-permission android:name="android.permission.INTERNET" />
14-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:5:3-64
14-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:5:20-62
15    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
15-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:6:3-77
15-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:6:20-75
16    <uses-permission android:name="android.permission.READ_CALENDAR" />
16-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:7:3-69
16-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:7:20-67
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:8:3-77
17-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:8:20-75
18    <uses-permission android:name="android.permission.RECORD_AUDIO" />
18-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:9:3-68
18-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:9:20-66
19    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
19-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:10:3-75
19-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:10:20-73
20    <uses-permission android:name="android.permission.VIBRATE" />
20-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:11:3-63
20-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:11:20-61
21    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
21-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:12:3-70
21-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:12:20-68
22    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
22-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:13:3-78
22-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:13:20-76
23
24    <queries>
24-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:14:3-20:13
25        <intent>
25-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:15:5-19:14
26            <action android:name="android.intent.action.VIEW" />
26-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:16:7-58
26-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:16:15-56
27
28            <category android:name="android.intent.category.BROWSABLE" />
28-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:17:7-67
28-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:17:17-65
29
30            <data android:scheme="https" />
30-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:18:7-37
30-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:18:13-35
31        </intent>
32        <intent>
32-->[:expo-calendar] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-calendar\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-14:18
33
34            <!-- Required for opening event in calendar if targeting API 30 -->
35            <action android:name="android.intent.action.VIEW" />
35-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:16:7-58
35-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:16:15-56
36
37            <data android:scheme="content" />
37-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:18:7-37
37-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:18:13-35
38        </intent> <!-- Query open documents -->
39        <intent>
39-->[:expo-document-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:9-17:18
40            <action android:name="android.intent.action.OPEN_DOCUMENT" />
40-->[:expo-document-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
40-->[:expo-document-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-71
41
42            <category android:name="android.intent.category.DEFAULT" />
42-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:32:9-67
42-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:32:19-65
43            <category android:name="android.intent.category.OPENABLE" />
43-->[:expo-document-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-73
43-->[:expo-document-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:23-70
44
45            <data android:mimeType="*/*" />
45-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:18:7-37
46        </intent> <!-- Query open documents -->
47        <intent>
47-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-17:18
48            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
48-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-79
48-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:21-76
49        </intent>
50        <intent>
50-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-19:18
51
52            <!-- Required for picking images from the camera roll if targeting API 30 -->
53            <action android:name="android.media.action.IMAGE_CAPTURE" />
53-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-73
53-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:21-70
54        </intent>
55        <intent>
55-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:9-24:18
56
57            <!-- Required for picking images from the camera if targeting API 30 -->
58            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
58-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-80
58-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:21-77
59        </intent>
60        <intent>
60-->[:expo-web-browser] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
61
62            <!-- Required for opening tabs if targeting API 30 -->
63            <action android:name="android.support.customtabs.action.CustomTabsService" />
63-->[:expo-web-browser] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-90
63-->[:expo-web-browser] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:21-87
64        </intent>
65        <intent>
65-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:11:9-17:18
66            <action android:name="android.intent.action.VIEW" />
66-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:16:7-58
66-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:16:15-56
67
68            <data
68-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:18:7-37
69                android:mimeType="*/*"
70                android:scheme="*" />
70-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:18:13-35
71        </intent>
72        <intent>
72-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:18:9-27:18
73            <action android:name="android.intent.action.VIEW" />
73-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:16:7-58
73-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:16:15-56
74
75            <category android:name="android.intent.category.BROWSABLE" />
75-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:17:7-67
75-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:17:17-65
76
77            <data
77-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:18:7-37
78                android:host="pay"
79                android:mimeType="*/*"
80                android:scheme="upi" />
80-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:18:13-35
81        </intent>
82        <intent>
82-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:28:9-30:18
83            <action android:name="android.intent.action.MAIN" />
83-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:27:9-60
83-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:27:17-58
84        </intent>
85        <intent>
85-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:31:9-35:18
86            <action android:name="android.intent.action.SEND" />
86-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:32:13-65
86-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:32:21-62
87
88            <data android:mimeType="*/*" />
88-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:18:7-37
89        </intent>
90        <intent>
90-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:36:9-38:18
91            <action android:name="rzp.device_token.share" />
91-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:37:13-61
91-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:37:21-58
92        </intent> <!-- Needs to be explicitly declared on Android R+ -->
93        <package android:name="com.google.android.apps.maps" />
93-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa08fd0869163705d58d4f365af34e4b\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
93-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa08fd0869163705d58d4f365af34e4b\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
94
95        <intent>
95-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2182c1e5a52c38fa13c74f3b2841e77\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
96            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
96-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2182c1e5a52c38fa13c74f3b2841e77\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
96-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2182c1e5a52c38fa13c74f3b2841e77\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
97        </intent>
98        <intent>
98-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c569f151f8eecc9b25adff1be1284e4\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
99            <action android:name="android.intent.action.GET_CONTENT" />
99-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c569f151f8eecc9b25adff1be1284e4\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
99-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c569f151f8eecc9b25adff1be1284e4\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
100
101            <category android:name="android.intent.category.OPENABLE" />
101-->[:expo-document-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-73
101-->[:expo-document-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:23-70
102
103            <data android:mimeType="*/*" />
103-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:18:7-37
104        </intent>
105    </queries>
106
107    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
107-->[:expo-media-library] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-media-library\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-76
107-->[:expo-media-library] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-media-library\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-73
108    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
108-->[:expo-media-library] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-media-library\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-75
108-->[:expo-media-library] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-media-library\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-72
109    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
109-->[:expo-media-library] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-media-library\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-75
109-->[:expo-media-library] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-media-library\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:22-72
110    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
110-->[:expo-media-library] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-media-library\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:5-90
110-->[:expo-media-library] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-media-library\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:22-87
111    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
111-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-81
111-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-78
112    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Include required permissions for Google Maps API to run. -->
112-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-77
112-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-74
113    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
113-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa08fd0869163705d58d4f365af34e4b\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
113-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa08fd0869163705d58d4f365af34e4b\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:22-76
114
115    <uses-feature
115-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa08fd0869163705d58d4f365af34e4b\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
116        android:glEsVersion="0x00020000"
116-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa08fd0869163705d58d4f365af34e4b\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
117        android:required="true" />
117-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa08fd0869163705d58d4f365af34e4b\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
118
119    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
119-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
119-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:22-65
120    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
120-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
120-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
121    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
121-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
121-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
122
123    <permission
123-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6591935515ee6cd5ec7881ab1d3c64e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
124        android:name="com.mohandhass2.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
124-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6591935515ee6cd5ec7881ab1d3c64e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
125        android:protectionLevel="signature" />
125-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6591935515ee6cd5ec7881ab1d3c64e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
126
127    <uses-permission android:name="com.mohandhass2.myapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
127-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6591935515ee6cd5ec7881ab1d3c64e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
127-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6591935515ee6cd5ec7881ab1d3c64e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
128    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
128-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05c6ca550590f77b772df379d6f4ca68\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
128-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05c6ca550590f77b772df379d6f4ca68\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
129    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
129-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af507bc22ed745895a82d64c46cec506\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:15:5-98
129-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af507bc22ed745895a82d64c46cec506\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:15:22-95
130    <uses-permission android:name="com.google.android.gms.permission.ACTIVITY_RECOGNITION" /> <!-- for android -->
130-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af507bc22ed745895a82d64c46cec506\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:16:5-94
130-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af507bc22ed745895a82d64c46cec506\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:16:22-91
131    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
132    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
133    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
134    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
135    <!-- for Samsung -->
136    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
136-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
136-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
137    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
137-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
137-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
138    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
138-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
138-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
139    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
139-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
139-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
140    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
140-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
140-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
141    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
141-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
141-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
142    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
142-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
142-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
143    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
143-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
143-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
144    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
144-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
144-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
145    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
145-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
145-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
146    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
146-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
146-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
147    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
147-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
147-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
148    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
148-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
148-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
149    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
149-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
149-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
150    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
150-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
150-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
151    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
151-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
151-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32853726267affe06dfc880f5875a26c\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
152
153    <application
153-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:21:3-38:17
154        android:name="com.mohandhass2.myapp.MainApplication"
154-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:21:16-47
155        android:allowBackup="true"
155-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:21:48-74
156        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
156-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6591935515ee6cd5ec7881ab1d3c64e\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
157        android:extractNativeLibs="false"
158        android:fullBackupContent="false"
158-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:21:75-108
159        android:icon="@mipmap/ic_launcher"
159-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:21:142-176
160        android:label="@string/app_name"
160-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:21:109-141
161        android:requestLegacyExternalStorage="true"
161-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:21:282-325
162        android:roundIcon="@mipmap/ic_launcher_round"
162-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:21:177-222
163        android:supportsRtl="true"
163-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:21:255-281
164        android:theme="@style/AppTheme"
164-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:21:223-254
165        android:usesCleartextTraffic="true" >
165-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:21:326-361
166        <meta-data
166-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:22:5-83
167            android:name="expo.modules.updates.ENABLED"
167-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:22:16-59
168            android:value="false" />
168-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:22:60-81
169        <meta-data
169-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:23:5-105
170            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
170-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:23:16-80
171            android:value="ALWAYS" />
171-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:23:81-103
172        <meta-data
172-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:24:5-99
173            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
173-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:24:16-79
174            android:value="0" />
174-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:24:80-97
175
176        <activity
176-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:25:5-37:16
177            android:name="com.mohandhass2.myapp.MainActivity"
177-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:25:15-43
178            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
178-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:25:44-134
179            android:exported="true"
179-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:25:256-279
180            android:launchMode="singleTask"
180-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:25:135-166
181            android:screenOrientation="portrait"
181-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:25:280-316
182            android:theme="@style/Theme.App.SplashScreen"
182-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:25:210-255
183            android:windowSoftInputMode="adjustResize" >
183-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:25:167-209
184            <intent-filter>
184-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:26:7-29:23
185                <action android:name="android.intent.action.MAIN" />
185-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:27:9-60
185-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:27:17-58
186
187                <category android:name="android.intent.category.LAUNCHER" />
187-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:28:9-68
187-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:28:19-66
188            </intent-filter>
189            <intent-filter>
189-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:30:7-36:23
190                <action android:name="android.intent.action.VIEW" />
190-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:16:7-58
190-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:16:15-56
191
192                <category android:name="android.intent.category.DEFAULT" />
192-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:32:9-67
192-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:32:19-65
193                <category android:name="android.intent.category.BROWSABLE" />
193-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:17:7-67
193-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:17:17-65
194
195                <data android:scheme="myapp" />
195-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:18:7-37
195-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:18:13-35
196                <data android:scheme="com.mohandhass2.myapp" />
196-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:18:7-37
196-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:18:13-35
197            </intent-filter>
198        </activity>
199        <activity
199-->[:react-native-razorpay] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-10:86
200            android:name="com.razorpay.CheckoutActivity"
200-->[:react-native-razorpay] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-57
201            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
201-->[:react-native-razorpay] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-razorpay\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-83
202            android:exported="false"
202-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:54:13-37
203            android:theme="@style/CheckoutTheme" >
203-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:55:13-49
204            <intent-filter>
204-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:56:13-58:29
205                <action android:name="android.intent.action.MAIN" />
205-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:27:9-60
205-->D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\AndroidManifest.xml:27:17-58
206            </intent-filter>
207        </activity>
208
209        <provider
209-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:9-30:20
210            android:name="expo.modules.filesystem.FileSystemFileProvider"
210-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-74
211            android:authorities="com.mohandhass2.myapp.FileSystemFileProvider"
211-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-74
212            android:exported="false"
212-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-37
213            android:grantUriPermissions="true" >
213-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-47
214            <meta-data
214-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-29:70
215                android:name="android.support.FILE_PROVIDER_PATHS"
215-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:17-67
216                android:resource="@xml/file_system_provider_paths" />
216-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-67
217        </provider>
218
219        <service
219-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:9-40:19
220            android:name="com.google.android.gms.metadata.ModuleDependencies"
220-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-78
221            android:enabled="false"
221-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-36
222            android:exported="false" >
222-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:13-37
223            <intent-filter>
223-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:13-35:29
224                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
224-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:17-94
224-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:25-91
225            </intent-filter>
226
227            <meta-data
227-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-39:36
228                android:name="photopicker_activity:0:required"
228-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:17-63
229                android:value="" />
229-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:17-33
230        </service>
231
232        <activity
232-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:9-44:59
233            android:name="com.canhub.cropper.CropImageActivity"
233-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-64
234            android:exported="true"
234-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c569f151f8eecc9b25adff1be1284e4\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
235            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
235-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-56
236        <provider
236-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:46:9-54:20
237            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
237-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:47:13-89
238            android:authorities="com.mohandhass2.myapp.ImagePickerFileProvider"
238-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:48:13-75
239            android:exported="false"
239-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:49:13-37
240            android:grantUriPermissions="true" >
240-->[:expo-image-picker] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:50:13-47
241            <meta-data
241-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-29:70
242                android:name="android.support.FILE_PROVIDER_PATHS"
242-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:17-67
243                android:resource="@xml/image_picker_provider_paths" />
243-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-67
244        </provider>
245
246        <service
246-->[:expo-location] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-location\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:9-14:56
247            android:name="expo.modules.location.services.LocationTaskService"
247-->[:expo-location] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-location\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-78
248            android:exported="false"
248-->[:expo-location] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-location\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-37
249            android:foregroundServiceType="location" />
249-->[:expo-location] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-location\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-53
250        <service
250-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:9-17:19
251            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
251-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-91
252            android:exported="false" >
252-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-37
253            <intent-filter android:priority="-1" >
253-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:29
253-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:28-49
254                <action android:name="com.google.firebase.MESSAGING_EVENT" />
254-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-78
254-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:25-75
255            </intent-filter>
256        </service>
257
258        <receiver
258-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:20
259            android:name="expo.modules.notifications.service.NotificationsService"
259-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-83
260            android:enabled="true"
260-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-35
261            android:exported="false" >
261-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
262            <intent-filter android:priority="-1" >
262-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-30:29
262-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:28-49
263                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
263-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:17-88
263-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:25-85
264                <action android:name="android.intent.action.BOOT_COMPLETED" />
264-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-79
264-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-76
265                <action android:name="android.intent.action.REBOOT" />
265-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:17-71
265-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:25-68
266                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
266-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:17-82
266-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:25-79
267                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
267-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:17-82
267-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:25-79
268                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
268-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-84
268-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:25-81
269            </intent-filter>
270        </receiver>
271
272        <activity
272-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:9-40:75
273            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
273-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:13-92
274            android:excludeFromRecents="true"
274-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:13-46
275            android:exported="false"
275-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-37
276            android:launchMode="standard"
276-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:13-42
277            android:noHistory="true"
277-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:13-37
278            android:taskAffinity=""
278-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:13-36
279            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
279-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:40:13-72
280
281        <meta-data
281-->[:expo-modules-core] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
282            android:name="org.unimodules.core.AppLoader#react-native-headless"
282-->[:expo-modules-core] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
283            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
283-->[:expo-modules-core] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
284        <meta-data
284-->[:expo-modules-core] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
285            android:name="com.facebook.soloader.enabled"
285-->[:expo-modules-core] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
286            android:value="true" />
286-->[:expo-modules-core] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
287        <meta-data
287-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e9bc6c234e70326f0babefb2238fc4f\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
288            android:name="com.google.android.gms.version"
288-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e9bc6c234e70326f0babefb2238fc4f\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
289            android:value="@integer/google_play_services_version" />
289-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e9bc6c234e70326f0babefb2238fc4f\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
290
291        <receiver
291-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:42:9-49:20
292            android:name="com.razorpay.RzpTokenReceiver"
292-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:43:13-57
293            android:exported="true" >
293-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:44:13-36
294            <intent-filter>
294-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:46:13-48:29
295                <action android:name="rzp.device_token.share" />
295-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:37:13-61
295-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:37:21-58
296            </intent-filter>
297        </receiver>
298
299        <provider
299-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:61:9-69:20
300            android:name="androidx.startup.InitializationProvider"
300-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:62:13-67
301            android:authorities="com.mohandhass2.myapp.androidx-startup"
301-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:63:13-68
302            android:exported="false" >
302-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:64:13-37
303            <meta-data
303-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:66:13-68:52
304                android:name="com.razorpay.RazorpayInitializer"
304-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:67:17-64
305                android:value="androidx.startup" />
305-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:68:17-49
306            <meta-data
306-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
307                android:name="androidx.work.WorkManagerInitializer"
307-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
308                android:value="androidx.startup" />
308-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
309            <meta-data
309-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cff5a9579380eb2099b3f5ffada093c5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
310                android:name="androidx.emoji2.text.EmojiCompatInitializer"
310-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cff5a9579380eb2099b3f5ffada093c5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
311                android:value="androidx.startup" />
311-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cff5a9579380eb2099b3f5ffada093c5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
312            <meta-data
312-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83f5c4cd7c3a21131883912131f63dc\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
313                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
313-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83f5c4cd7c3a21131883912131f63dc\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
314                android:value="androidx.startup" />
314-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83f5c4cd7c3a21131883912131f63dc\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
315            <meta-data
315-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
316                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
316-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
317                android:value="androidx.startup" />
317-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
318        </provider>
319
320        <activity
320-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:71:9-74:75
321            android:name="com.razorpay.MagicXActivity"
321-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:72:13-55
322            android:exported="false"
322-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:73:13-37
323            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
323-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:74:13-72
324
325        <meta-data
325-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:76:9-78:58
326            android:name="com.razorpay.plugin.googlepay_all"
326-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:77:13-61
327            android:value="com.razorpay.RzpGpayMerged" /> <!-- Needs to be explicitly declared on P+ -->
327-->[com.razorpay:standard-core:1.6.49] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\AndroidManifest.xml:78:13-55
328        <uses-library
328-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa08fd0869163705d58d4f365af34e4b\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
329            android:name="org.apache.http.legacy"
329-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa08fd0869163705d58d4f365af34e4b\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
330            android:required="false" />
330-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa08fd0869163705d58d4f365af34e4b\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
331        <uses-library
331-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2182c1e5a52c38fa13c74f3b2841e77\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
332            android:name="androidx.camera.extensions.impl"
332-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2182c1e5a52c38fa13c74f3b2841e77\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
333            android:required="false" />
333-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2182c1e5a52c38fa13c74f3b2841e77\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
334
335        <service
335-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae4669ff24f5b3ce62872727aeef56ca\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
336            android:name="androidx.camera.core.impl.MetadataHolderService"
336-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae4669ff24f5b3ce62872727aeef56ca\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
337            android:enabled="false"
337-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae4669ff24f5b3ce62872727aeef56ca\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
338            android:exported="false" >
338-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae4669ff24f5b3ce62872727aeef56ca\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
339            <meta-data
339-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae4669ff24f5b3ce62872727aeef56ca\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
340                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
340-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae4669ff24f5b3ce62872727aeef56ca\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
341                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
341-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae4669ff24f5b3ce62872727aeef56ca\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
342        </service>
343
344        <provider
344-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c569f151f8eecc9b25adff1be1284e4\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
345            android:name="com.canhub.cropper.CropFileProvider"
345-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c569f151f8eecc9b25adff1be1284e4\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
346            android:authorities="com.mohandhass2.myapp.cropper.fileprovider"
346-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c569f151f8eecc9b25adff1be1284e4\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
347            android:exported="false"
347-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c569f151f8eecc9b25adff1be1284e4\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
348            android:grantUriPermissions="true" >
348-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c569f151f8eecc9b25adff1be1284e4\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
349            <meta-data
349-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-29:70
350                android:name="android.support.FILE_PROVIDER_PATHS"
350-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:17-67
351                android:resource="@xml/library_file_paths" />
351-->[:expo-file-system] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-67
352        </provider>
353
354        <activity
354-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36c2911903c2f91ce96f9859f62e94a1\transformed\play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
355            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
355-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36c2911903c2f91ce96f9859f62e94a1\transformed\play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
356            android:excludeFromRecents="true"
356-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36c2911903c2f91ce96f9859f62e94a1\transformed\play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
357            android:exported="false"
357-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36c2911903c2f91ce96f9859f62e94a1\transformed\play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
358            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
358-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36c2911903c2f91ce96f9859f62e94a1\transformed\play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
359        <!--
360            Service handling Google Sign-In user revocation. For apps that do not integrate with
361            Google Sign-In, this service will never be started.
362        -->
363        <service
363-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36c2911903c2f91ce96f9859f62e94a1\transformed\play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
364            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
364-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36c2911903c2f91ce96f9859f62e94a1\transformed\play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
365            android:exported="true"
365-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36c2911903c2f91ce96f9859f62e94a1\transformed\play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
366            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
366-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36c2911903c2f91ce96f9859f62e94a1\transformed\play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
367            android:visibleToInstantApps="true" />
367-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36c2911903c2f91ce96f9859f62e94a1\transformed\play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
368
369        <receiver
369-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
370            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
370-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
371            android:exported="true"
371-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
372            android:permission="com.google.android.c2dm.permission.SEND" >
372-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
373            <intent-filter>
373-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
374                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
374-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
374-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
375            </intent-filter>
376
377            <meta-data
377-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
378                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
378-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
379                android:value="true" />
379-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
380        </receiver>
381        <!--
382             FirebaseMessagingService performs security checks at runtime,
383             but set to not exported to explicitly avoid allowing another app to call it.
384        -->
385        <service
385-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
386            android:name="com.google.firebase.messaging.FirebaseMessagingService"
386-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
387            android:directBootAware="true"
387-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
388            android:exported="false" >
388-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
389            <intent-filter android:priority="-500" >
389-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:29
389-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:28-49
390                <action android:name="com.google.firebase.MESSAGING_EVENT" />
390-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-78
390-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:25-75
391            </intent-filter>
392        </service>
393        <service
393-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
394            android:name="com.google.firebase.components.ComponentDiscoveryService"
394-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
395            android:directBootAware="true"
395-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00718f8cd295d237ea8fb67f7cbcc86a\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
396            android:exported="false" >
396-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
397            <meta-data
397-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
398                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
398-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
399                android:value="com.google.firebase.components.ComponentRegistrar" />
399-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
400            <meta-data
400-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
401                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
401-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
402                android:value="com.google.firebase.components.ComponentRegistrar" />
402-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
403            <meta-data
403-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba7214cefc26274a641f5f9ebd8f91b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
404                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
404-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba7214cefc26274a641f5f9ebd8f91b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
405                android:value="com.google.firebase.components.ComponentRegistrar" />
405-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba7214cefc26274a641f5f9ebd8f91b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
406            <meta-data
406-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba7214cefc26274a641f5f9ebd8f91b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
407                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
407-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba7214cefc26274a641f5f9ebd8f91b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
408                android:value="com.google.firebase.components.ComponentRegistrar" />
408-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba7214cefc26274a641f5f9ebd8f91b9\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
409            <meta-data
409-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62a7405242461443e9d1a439c7c29254\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
410                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
410-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62a7405242461443e9d1a439c7c29254\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
411                android:value="com.google.firebase.components.ComponentRegistrar" />
411-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62a7405242461443e9d1a439c7c29254\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
412            <meta-data
412-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00718f8cd295d237ea8fb67f7cbcc86a\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
413                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
413-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00718f8cd295d237ea8fb67f7cbcc86a\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
414                android:value="com.google.firebase.components.ComponentRegistrar" />
414-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00718f8cd295d237ea8fb67f7cbcc86a\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
415            <meta-data
415-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af903d3cabb379c20a8388ee7e71c8a6\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
416                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
416-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af903d3cabb379c20a8388ee7e71c8a6\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
417                android:value="com.google.firebase.components.ComponentRegistrar" />
417-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af903d3cabb379c20a8388ee7e71c8a6\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
418        </service>
419        <service
419-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bee234183f591e2702620b9626d8e9bb\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
420            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
420-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bee234183f591e2702620b9626d8e9bb\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
421            android:directBootAware="true"
421-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a76ddcc15e8544360e098934199ae152\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
422            android:exported="false" >
422-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bee234183f591e2702620b9626d8e9bb\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
423            <meta-data
423-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bee234183f591e2702620b9626d8e9bb\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
424                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
424-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bee234183f591e2702620b9626d8e9bb\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
425                android:value="com.google.firebase.components.ComponentRegistrar" />
425-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bee234183f591e2702620b9626d8e9bb\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
426            <meta-data
426-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e9e11ba8ec68b44bb95431d0155ba3f\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
427                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
427-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e9e11ba8ec68b44bb95431d0155ba3f\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
428                android:value="com.google.firebase.components.ComponentRegistrar" />
428-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e9e11ba8ec68b44bb95431d0155ba3f\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
429            <meta-data
429-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a76ddcc15e8544360e098934199ae152\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
430                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
430-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a76ddcc15e8544360e098934199ae152\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
431                android:value="com.google.firebase.components.ComponentRegistrar" />
431-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a76ddcc15e8544360e098934199ae152\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
432        </service>
433
434        <provider
434-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a76ddcc15e8544360e098934199ae152\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
435            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
435-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a76ddcc15e8544360e098934199ae152\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
436            android:authorities="com.mohandhass2.myapp.mlkitinitprovider"
436-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a76ddcc15e8544360e098934199ae152\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
437            android:exported="false"
437-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a76ddcc15e8544360e098934199ae152\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
438            android:initOrder="99" />
438-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a76ddcc15e8544360e098934199ae152\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
439
440        <activity
440-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b84b30b80c5949bd0f3610f30992b047\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
441            android:name="com.google.android.gms.common.api.GoogleApiActivity"
441-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b84b30b80c5949bd0f3610f30992b047\transformed\play-services-base-18.3.0\AndroidManifest.xml:20:19-85
442            android:exported="false"
442-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b84b30b80c5949bd0f3610f30992b047\transformed\play-services-base-18.3.0\AndroidManifest.xml:22:19-43
443            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
443-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b84b30b80c5949bd0f3610f30992b047\transformed\play-services-base-18.3.0\AndroidManifest.xml:21:19-78
444
445        <provider
445-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00718f8cd295d237ea8fb67f7cbcc86a\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
446            android:name="com.google.firebase.provider.FirebaseInitProvider"
446-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00718f8cd295d237ea8fb67f7cbcc86a\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
447            android:authorities="com.mohandhass2.myapp.firebaseinitprovider"
447-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00718f8cd295d237ea8fb67f7cbcc86a\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
448            android:directBootAware="true"
448-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00718f8cd295d237ea8fb67f7cbcc86a\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
449            android:exported="false"
449-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00718f8cd295d237ea8fb67f7cbcc86a\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
450            android:initOrder="100" />
450-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00718f8cd295d237ea8fb67f7cbcc86a\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
451
452        <service
452-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
453            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
453-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
454            android:directBootAware="false"
454-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
455            android:enabled="@bool/enable_system_alarm_service_default"
455-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
456            android:exported="false" />
456-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
457        <service
457-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
458            android:name="androidx.work.impl.background.systemjob.SystemJobService"
458-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
459            android:directBootAware="false"
459-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
460            android:enabled="@bool/enable_system_job_service_default"
460-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
461            android:exported="true"
461-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
462            android:permission="android.permission.BIND_JOB_SERVICE" />
462-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
463        <service
463-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
464            android:name="androidx.work.impl.foreground.SystemForegroundService"
464-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
465            android:directBootAware="false"
465-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
466            android:enabled="@bool/enable_system_foreground_service_default"
466-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
467            android:exported="false" />
467-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
468
469        <receiver
469-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
470            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
470-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
471            android:directBootAware="false"
471-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
472            android:enabled="true"
472-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
473            android:exported="false" />
473-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
474        <receiver
474-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
475            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
475-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
476            android:directBootAware="false"
476-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
477            android:enabled="false"
477-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
478            android:exported="false" >
478-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
479            <intent-filter>
479-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
480                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
480-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
480-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
481                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
481-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
481-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
482            </intent-filter>
483        </receiver>
484        <receiver
484-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
485            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
485-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
486            android:directBootAware="false"
486-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
487            android:enabled="false"
487-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
488            android:exported="false" >
488-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
489            <intent-filter>
489-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
490                <action android:name="android.intent.action.BATTERY_OKAY" />
490-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
490-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
491                <action android:name="android.intent.action.BATTERY_LOW" />
491-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
491-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
492            </intent-filter>
493        </receiver>
494        <receiver
494-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
495            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
495-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
496            android:directBootAware="false"
496-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
497            android:enabled="false"
497-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
498            android:exported="false" >
498-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
499            <intent-filter>
499-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
500                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
500-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
500-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
501                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
501-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
501-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
502            </intent-filter>
503        </receiver>
504        <receiver
504-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
505            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
505-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
506            android:directBootAware="false"
506-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
507            android:enabled="false"
507-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
508            android:exported="false" >
508-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
509            <intent-filter>
509-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
510                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
510-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
510-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
511            </intent-filter>
512        </receiver>
513        <receiver
513-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
514            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
514-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
515            android:directBootAware="false"
515-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
516            android:enabled="false"
516-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
517            android:exported="false" >
517-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
518            <intent-filter>
518-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
519                <action android:name="android.intent.action.BOOT_COMPLETED" />
519-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-79
519-->[:expo-notifications] D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-76
520                <action android:name="android.intent.action.TIME_SET" />
520-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
520-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
521                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
521-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
521-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
522            </intent-filter>
523        </receiver>
524        <receiver
524-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
525            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
525-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
526            android:directBootAware="false"
526-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
527            android:enabled="@bool/enable_system_alarm_service_default"
527-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
528            android:exported="false" >
528-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
529            <intent-filter>
529-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
530                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
530-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
530-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
531            </intent-filter>
532        </receiver>
533        <receiver
533-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
534            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
534-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
535            android:directBootAware="false"
535-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
536            android:enabled="true"
536-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
537            android:exported="true"
537-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
538            android:permission="android.permission.DUMP" >
538-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
539            <intent-filter>
539-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
540                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
540-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
540-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
541            </intent-filter>
542        </receiver>
543        <receiver
543-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
544            android:name="androidx.profileinstaller.ProfileInstallReceiver"
544-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
545            android:directBootAware="false"
545-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
546            android:enabled="true"
546-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
547            android:exported="true"
547-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
548            android:permission="android.permission.DUMP" >
548-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
549            <intent-filter>
549-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
550                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
550-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
550-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
551            </intent-filter>
552            <intent-filter>
552-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
553                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
553-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
553-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
554            </intent-filter>
555            <intent-filter>
555-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
556                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
556-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
556-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
557            </intent-filter>
558            <intent-filter>
558-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
559                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
559-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
559-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
560            </intent-filter>
561        </receiver>
562
563        <service
563-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8789a91f0e174789db96162f1e721fb6\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
564            android:name="androidx.room.MultiInstanceInvalidationService"
564-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8789a91f0e174789db96162f1e721fb6\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
565            android:directBootAware="true"
565-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8789a91f0e174789db96162f1e721fb6\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
566            android:exported="false" />
566-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8789a91f0e174789db96162f1e721fb6\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
567        <service
567-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\070cc64a529048f6406ece9d20906c35\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
568            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
568-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\070cc64a529048f6406ece9d20906c35\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
569            android:exported="false" >
569-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\070cc64a529048f6406ece9d20906c35\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
570            <meta-data
570-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\070cc64a529048f6406ece9d20906c35\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
571                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
571-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\070cc64a529048f6406ece9d20906c35\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
572                android:value="cct" />
572-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\070cc64a529048f6406ece9d20906c35\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
573        </service>
574        <service
574-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b910d123385b72a50df249d4995d68f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
575            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
575-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b910d123385b72a50df249d4995d68f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
576            android:exported="false"
576-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b910d123385b72a50df249d4995d68f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
577            android:permission="android.permission.BIND_JOB_SERVICE" >
577-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b910d123385b72a50df249d4995d68f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
578        </service>
579
580        <receiver
580-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b910d123385b72a50df249d4995d68f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
581            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
581-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b910d123385b72a50df249d4995d68f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
582            android:exported="false" />
582-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b910d123385b72a50df249d4995d68f\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
583
584        <service
584-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af507bc22ed745895a82d64c46cec506\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:19:9-21:40
585            android:name="io.nlopez.smartlocation.activity.providers.ActivityGooglePlayServicesProvider$ActivityRecognitionService"
585-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af507bc22ed745895a82d64c46cec506\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:20:13-132
586            android:exported="false" />
586-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af507bc22ed745895a82d64c46cec506\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:21:13-37
587        <service
587-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af507bc22ed745895a82d64c46cec506\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:22:9-24:40
588            android:name="io.nlopez.smartlocation.geofencing.providers.GeofencingGooglePlayServicesProvider$GeofencingService"
588-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af507bc22ed745895a82d64c46cec506\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:23:13-127
589            android:exported="false" />
589-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af507bc22ed745895a82d64c46cec506\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:24:13-37
590        <service
590-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af507bc22ed745895a82d64c46cec506\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:25:9-27:40
591            android:name="io.nlopez.smartlocation.geocoding.providers.AndroidGeocodingProvider$AndroidGeocodingService"
591-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af507bc22ed745895a82d64c46cec506\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:26:13-120
592            android:exported="false" />
592-->[:expo-location$io.nlopez.smartlocation-jetified-aar] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af507bc22ed745895a82d64c46cec506\transformed\io.nlopez.smartlocation-3.3.3-jetified\AndroidManifest.xml:27:13-37
593    </application>
594
595</manifest>
