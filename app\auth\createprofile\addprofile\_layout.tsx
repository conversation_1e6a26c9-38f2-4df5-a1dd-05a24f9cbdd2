import BackArrow from "../../../../assets/icons/BackArrow.svg";
import React, { createContext, useRef, useState } from "react";
import { HelpPopUp } from "..";
import { router, useRouter } from "expo-router";
import { Slot } from "expo-router";
import { usePathname } from "expo-router";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const _layout = () => {
  const HelpBottomSheetRef = useRef(null);

  const pathname = usePathname();

  return (
    <SafeAreaView
      style={{
        flex: 1,
      }}
    >
      <ScrollView className="mb-4 flex-1">
        {/* Title Area */}
        <View className="px-5 flex-1">
          <View className="flex-row relative items-center justify-start mt-4">
            <TouchableOpacity
              onPress={() => {
                router.back();
              }}
              className="absolute"
            >
              <View>
                <BackArrow />
              </View>
            </TouchableOpacity>
            <View className="flex-1 items-center justify-center">
              <Text className="font-[400] text-[26px] leading-[39px]">
                Create your profile
              </Text>
            </View>
            {/* <TouchableOpacity
              onPress={() => {
                HelpBottomSheetRef.current.open();
              }}
              className="right-5 top-2 absolute w-[50px] rounded-[30px] h-[25px] bg-[#A4F4AC] items-center justify-center"
            >
              <Text className="font-[600] text-[14px] leading-[21px] text-[#00660A]">
                Help
              </Text>
            </TouchableOpacity> */}
          </View>
          <View className="flex-row space-x-2 mt-7">
            <View
              className="flex-1 h-[4px] rounded-[13px]"
              style={{
                backgroundColor:
                  pathname.includes("page1") ||
                  pathname.includes("page2") ||
                  pathname.includes("page3")
                    ? "#00660A"
                    : "#E6E6E6",
              }}
            />
            <View
              className="flex-1 h-[4px] rounded-[13px]"
              style={{
                backgroundColor:
                  pathname.includes("page2") || pathname.includes("page3")
                    ? "#00660A"
                    : "#E6E6E6",
              }}
            />
            <View
              className="flex-1 h-[4px] rounded-[13px]"
              style={{
                backgroundColor: pathname.includes("page3")
                  ? "#00660A"
                  : "#E6E6E6",
              }}
            />
          </View>
          <Slot />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default _layout;
