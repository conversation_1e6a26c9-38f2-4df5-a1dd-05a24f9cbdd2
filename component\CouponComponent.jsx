import { View, Text, Image, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import im from "../assets/CardImage/cardImage.png";
import SideArrow from "../assets/CardImage/sideArrow.svg";
import Uparrow from "../assets/CardImage/Vector.svg";

const CouponComponent = ({ cardname, title, details, img }) => {
  const [showDetails, setDetails] = useState(false);
  return (
    <>
      <View className="border-[1px] border-[#E9EAE9] rounded-tr-[4px] rounded-tl-[4px]">
        <View className="flex-row p-3 space-x-4">
          <View>
            <Image source={im} />
          </View>
          <View className="">
            <View className="">
              <Text className="font-[500] text-[14px] leading-[21px]">
                FLAT ₹ 400 OFF using Visa Bank
              </Text>
              <Text className="font-[500] text-[14px] leading-[21px]">
                Credit Cards
              </Text>
            </View>
            <View className="my-2">
              <Text className="font-[500] text-[12px] leading-[18px] text-[#2157E2]">
                Save ₹ 400 with this code
              </Text>
            </View>
            <View className="flex-row justify-between w-[90%] mt-3">
              <TouchableOpacity className="border-[#E9EAE9] border-[1px] px-2 py-1">
                <Text>VISANEW</Text>
              </TouchableOpacity>
              {showDetails ? (
                <TouchableOpacity
                  onPress={() => {
                    setDetails(!showDetails);
                  }}
                  className="border-[#E9EAE9] border-[1px] flex-row items-center space-x-2 px-2 py-1"
                >
                  <Text>Hide Details</Text>
                  <Uparrow />
                </TouchableOpacity>
              ) : (
                <>
                  <TouchableOpacity
                    onPress={() => {
                      setDetails(!showDetails);
                    }}
                    className="border-[#E9EAE9] border-[1px] flex-row items-center space-x-2 px-2 py-1"
                  >
                    <Text>View Details</Text>
                    <SideArrow />
                  </TouchableOpacity>
                </>
              )}
            </View>
          </View>
        </View>
        {showDetails && (
          <View className="px-4">
            <View className="flex-row items-center space-x-2 my-2">
              <View className="bg-[#D9D9D9] h-[9px] w-[9px] rounded-full" />
              <Text>Offer applicable on Visa bank credit cards</Text>
            </View>
            <View className="flex-row items-center space-x-2 my-2">
              <View className="bg-[#D9D9D9] h-[9px] w-[9px] rounded-full" />
              <Text>
                Offer valid once per card per user during the offer period
              </Text>
            </View>
            <View className="flex-row items-center space-x-2 my-2">
              <View className="bg-[#D9D9D9] h-[9px] w-[9px] rounded-full" />
              <Text>Valid on a minimum transaction of ₹ 400 or above</Text>
            </View>
            <View className="flex-row items-center space-x-2 my-2">
              <View className="bg-[#D9D9D9] h-[9px] w-[9px] rounded-full" />
              <Text>Maximum Discount ₹ 50</Text>
            </View>
            <View className="flex-row items-center space-x-2 my-2">
              <View className="bg-[#D9D9D9] h-[9px] w-[9px] rounded-full" />
              <Text>Other T & C’s may apply</Text>
            </View>
          </View>
        )}

        <TouchableOpacity className="h-[37px] bg-[#D4FFD8] items-center justify-center rounded-br-[4px] rounded-bl-[4px]">
          <Text className="font-[500] text-[14px] leading-[21px] text-[#00660A]">Tap to Apply</Text>
        </TouchableOpacity>
      </View>
    </>
  );
};

export default CouponComponent;
