import { OrderListModel } from "@/data/model/order_model/order_list_model";
import { Api } from "@/data/source/network/api";
import { OrderRepository } from "@/domain/repository/order";

export class OrderRepositoryimpl implements OrderRepository {
  api: Api;
  constructor(api: Api) {
    this.api = api;
  }
  async getOrders(param: { id: number }): Promise<OrderListModel[]> {
    try {
      const data = await this.api.gatData<
        { data: OrderListModel[] },
        { userId: number }
      >("order/all_order_list", { userId: param.id });
      return data.data.data;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
