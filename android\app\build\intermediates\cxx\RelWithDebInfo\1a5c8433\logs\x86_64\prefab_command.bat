@echo off
"C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  x86_64 ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  26 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging9906135369825940992\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\prefab" ^
  "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\refs\\shopify_react-native-skia\\n36v5t16" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8a5944fdc62bae076801e020e1281841\\transformed\\hermes-android-0.76.9-release\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6dd910b94089a928dab663afb856f4b\\transformed\\fbjni-0.6.0\\prefab"
