# C/C++ build system timings
generate_cxx_metadata
  [gap of 101ms]
  create-invalidation-state 102ms
  generate-prefab-packages
    exec-prefab 978ms
    [gap of 86ms]
  generate-prefab-packages completed in 1068ms
  execute-generate-process
    exec-configure 1319ms
    [gap of 172ms]
  execute-generate-process completed in 1492ms
  [gap of 107ms]
  write-metadata-json-to-file 23ms
generate_cxx_metadata completed in 2896ms

