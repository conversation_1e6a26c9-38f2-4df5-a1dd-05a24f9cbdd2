import AsyncStorage from "@react-native-async-storage/async-storage";
import Constants from "expo-constants";
import axios from "axios";
import { useEffect, useState } from "react";

const useSetApiData = ({ endpoint }) => {
  const [data, setdata] = useState(null);
  const [isLoading, setLoading] = useState(false);
  const SetFunction = async (datas) => {
    let msg;
    setLoading(true);
    const token = await AsyncStorage.getItem("login");
    await axios
      .post(
        `${Constants.expoConfig?.extra?.BASE_URL}${endpoint}`,
        { ...datas },
        {
          headers: {
            "X-Powered-By": "Express",
            "Access-Control-Allow-Origin": "*",
            "Content-Type": "application/json",
            Authorization: token,
          },
        }
      )
      .then((val) => {
        return val.data;
      })
      .then((datas) => {
        msg = datas;
        setdata(datas);
        return datas;
      })
      .then((data) => {
        console.log(data);
        setLoading(false);
        return data;
      })
      .catch((error) => {
        console.error(error.response ? error.response.data : error.message);
      });
    return msg;
  };

  return { isLoading, data, SetFunction };
};

export default useSetApiData;
