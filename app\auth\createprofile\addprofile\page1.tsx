import * as ImagePicker from "expo-image-picker";
import AsyncStorage from "@react-native-async-storage/async-storage";
import CamaraIcon from "@/assets/icons/bottemSheetIcon/camara.svg";
import Checkbox from "expo-checkbox";
import Close from "@/assets/icons/bottemSheetIcon/Close.svg";
import DocIcon from "@/assets/icons/bottemSheetIcon/gallery.svg";
import RBSheet from "react-native-raw-bottom-sheet";
import React, { useContext, useEffect, useRef, useState } from "react";
import TextinputFile from "../../../../component/TextinputFile";
import UpArrow from "../../../../assets/profile/uparrow.svg";
import UploadIcon from "@/assets/icons/uploadIcon.svg";
import useTenStackMutate from "@/hooks/useTenStackMutate/useTenStackMutate";
import useUpdateWithFormData from "../../../../hooks/useUpdateWithFormData";
import { DropDownComponent2 } from ".";
import { router } from "expo-router";
import { Controller, useForm } from "react-hook-form";

import {
  Alert,
  Image,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

const page1 = () => {
  const {
    control,
    formState: { isValid, errors },
    setValue,
    handleSubmit,
  } = useForm();

  const { mutate: mutateupload_documents } = useTenStackMutate({
    invalidateQueriesKey: [""],
    endpoint: "auth/upload_documents",
  });
  const { mutate: mutateupload_front_part } = useTenStackMutate({
    invalidateQueriesKey: [""],
    endpoint: "auth/upload_adhaar_front_part",
  });
  const { mutate: mutateupload_back_part } = useTenStackMutate({
    invalidateQueriesKey: [""],
    endpoint: "auth/upload_adhaar_back_part",
  });

  const Submit = () => {
    handleSubmit(async (data) => {
      console.log(data.adhaarFront, "a tedst ierjhfiej");
      if (
        data?.adhaarFront === undefined ||
        data?.adhaarFront === null ||
        data?.adhaarFront === ""
      ) {
        Alert.alert("Error", "Please upload a adhaar photo picture");
        return;
      }
      if (
        data?.adhaarBack === undefined ||
        data?.adhaarBack === null ||
        data?.adhaarBack === ""
      ) {
        Alert.alert("Error", "Please upload a adhaar photo picture");
        return;
      }
      if (
        data?.panImg === undefined ||
        data?.panImg === null ||
        data?.panImg === ""
      ) {
        Alert.alert("Error", "Please upload a pan photo picture");
        return;
      }
      const formdata = new FormData();
      const front_partFormData = new FormData();
      const back_partFormData = new FormData();

      front_partFormData.append("adhaar_photo", {
        uri: data?.adhaarFront,
        name: data?.adhaarFront,
        filename: data?.adhaarFront,
        type: "image/png",
        size: 100000000,
      } as any);

      back_partFormData.append("adhaar_photo", {
        uri: data?.adhaarBack,
        name: data?.adhaarBack,
        filename: data?.adhaarBack,
        type: "image/png",
        size: 100000000,
      } as any);

      formdata.append("adhaar_no", data?.adhaar);
      formdata.append("pan_no", data?.pancard);
      formdata.append("pan_image", {
        uri: data?.panImg,
        name: data?.panImg,
        filename: data?.panImg,
        type: "image/png",
        size: 100000000,
      } as any);
      await mutateupload_documents(formdata, {
        onSuccess: () => {
          console.log("documents uploaded successfully");
        },
      });
      await mutateupload_front_part(front_partFormData, {
        onSuccess: () => {
          console.log("front part uploaded successfully");
        },
      });
      await mutateupload_back_part(back_partFormData, {
        onSuccess: () => {
          console.log("back part uploaded successfully");
          router.push(`/auth/createprofile/addprofile/page${2}`);
        },
      });
    })();
  };
  return (
    <View className="">
      <View className="mt-6">
        <Text className="font-Pop font-[400] text-[18px] leading-[27px]">
          Upload your documents
        </Text>
        <Text className="font-Pop font-[400] text-[14px] leading-[21px]">
          Upload all documents to start earning
        </Text>
      </View>
      <View className="">
        {/* Aadhar card TextField Area */}
        <View className="mt-4">
          <Text className="font-[400] text-[16px] text-[#4D4D4D]">
            Identity Proof
          </Text>

          <View className="mt-4 items-start">
            <Text className="font-[400] text-[16px] text-[#4D4D4D]">
              adhaar card Number
            </Text>
            <AdhaarTextinputFile
              placeholderVal={"Enter Adhaar number here"}
              control={control}
              name={"adhaar"}
              rules={{
                required: {
                  value: true,
                  message: "Enter Adhaar number",
                },
                minLength: { value: 12, message: "Enter 12 digit number" },
                maxLength: { value: 12, message: "Enter 12 digit number" },
                pattern: {
                  value: /^[0-9]{12}$/,
                  message: "Please enter a valid 12-digit",
                },
              }}
            />
            {errors.adhaar && (
              <Text className="text-red-500 text-center">
                {(errors as any).adhaar.message}
              </Text>
            )}
          </View>
        </View>
      </View>

      <View
        className="mt-4 justify-between"
        style={{
          flexDirection: "row",
        }}
      >
        <UploadField
          text={"Front side"}
          setValue={setValue}
          name={"adhaarFront"}
          key={1}
        />
        <UploadField
          text={"Back side"}
          setValue={setValue}
          name={"adhaarBack"}
          key={2}
        />
      </View>

      <View className="mt-6">
        <Text className="font-[400] text-[16px] leading-[20px] font-Pop">
          2. PAN card
        </Text>
        {/* Last Name TextField Area */}
        <View className="mt-4 items-start">
          <Text className="font-[400] text-[16px] text-[#4D4D4D]">
            Pan card Number
          </Text>
          <TextinputFile
            placeholderVal={"Enter Pan card Number"}
            control={control}
            name={"pancard"}
            rules={{
              required: {
                value: true,
                message: "PAN number required",
              },
              minLength: { value: 10, message: "Enter 10 digit number" },
              maxLength: { value: 10, message: "Enter 10 digit number" },
              pattern: {
                value: /^[0-9]{10}$/,
                message: "Please enter a valid 10-digit",
              },
            }}
          />
          {errors.pancard && (
            <Text className="text-red-500 text-center">
              {(errors as any).pancard.message}
            </Text>
          )}
        </View>
      </View>

      <View className="mt-4 ">
        <UploadField
          text={"Front side"}
          setValue={setValue}
          name={"panImg"}
          key={3}
        />
      </View>
      <View className="m-2 px-4 mt-24">
        <TouchableOpacity
          style={{
            opacity: isValid ? 1 : 0.7,
          }}
          onPress={() => {
            Submit();
          }}
          className="h-[44px] items-center justify-center bg-[#00660A]"
        >
          <Text className="font-[400] text-[#fff] leading-[24px] font-Pop">
            Continue
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export const UploadField = ({ title, text, setValue, images, name }) => {
  const [image, setImage] = useState(images ? images : "");

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      quality: 1,
    });

    if (!result.canceled) {
      setValue(name, result.assets[0].uri);
      setImage(() => {
        return result.assets[0].uri;
      });
    }
  };

  const openCamera = async () => {
    let result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      quality: 1,
    });

    if (!result.canceled) {
      setValue(name, result.assets[0].uri);
      setImage(() => {
        return result.assets[0].uri;
      });
    }
  };
  const refRBSheet = useRef();
  return (
    <>
      <TouchableOpacity
        onPress={() => {
          refRBSheet.current.open();
        }}
        className="relative border-[1px] space-y-5 border-[#E9EAE9] h-[120px] w-[184px] items-center justify-center rounded-[8px]"
      >
        <UpArrow />
        <Text className="font-[500] font-Pop text-[12px] leading-[18px] text-[#00660A]">
          {text}
        </Text>
        <Image
          className="absolute"
          source={{ uri: image }}
          style={{ width: 100, height: 100 }}
        />

        <View className="mt-2">
          <RBSheet
            ref={refRBSheet}
            customStyles={{
              container: {
                borderRadius: 20,
              },
              draggableIcon: {
                backgroundColor: "#000",
              },
            }}
            customModalProps={{
              statusBarTranslucent: true,
            }}
            customAvoidingViewProps={{
              enabled: false,
            }}
          >
            <View className="px-6 py-2 justify-center">
              <View className="mt-2">
                <View className="flex-row justify-between">
                  <View />
                  <TouchableOpacity
                    onPress={() => {
                      refRBSheet.current.close();
                    }}
                  >
                    <Close />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            <View className="flex-1 flex-row items-center justify-evenly">
              <TouchableOpacity
                onPress={() => {
                  openCamera();
                }}
              >
                <View className="items-center justify-center space-y-2">
                  <CamaraIcon width={40} height={40} />
                  <Text>Camara</Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  pickImage();
                }}
              >
                <View className="items-center justify-center space-y-2">
                  <DocIcon width={40} height={40} />
                  <Text>Document</Text>
                </View>
              </TouchableOpacity>
            </View>
          </RBSheet>
        </View>
      </TouchableOpacity>
    </>
  );
};

const AdhaarTextinputFile = (props) => {
  return (
    <>
      <View
        className={
          "px-2 border-[1px] border-[#ACB9D5] h-[40px] items-center flex-row mt-2 rounded-[4px] justify-between " +
          props.containerstyle
        }
      >
        <Controller
          control={props?.control}
          name={props?.name}
          rules={{
            required: {
              value: true,
            },
            ...props?.rules,
          }}
          render={({ field: { onChange, onBlur, value } }) => (
            <TextInput
              value={value}
              onChangeText={(value) => {
                onChange(value);
              }}
              style={{
                color: "#000",
                flex: 1,
              }}
              maxLength={16}
              placeholder={props?.placeholderVal}
              className={props.inputstyle}
              defaultValue={props?.placeholder}
              editable={props?.editable}
            />
          )}
        />
        <TouchableOpacity>{props.component}</TouchableOpacity>
      </View>
    </>
  );
};
export default page1;
