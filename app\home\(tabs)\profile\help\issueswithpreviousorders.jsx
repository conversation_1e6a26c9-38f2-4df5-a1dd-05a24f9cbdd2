import { View, Text, TouchableOpacity } from "react-native";
import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import BackIcon from "@/assets/icons/BackArrow.svg";
import { SlideComponent } from "./";

const issueswithpreviousorders = () => {
  return (
    <SafeAreaView className="flex-1">
      <View className="items-center justify-center relative mt-6">
        <TouchableOpacity
          className="absolute left-[3%]"
          onPress={() => {
            router.back();
          }}
        >
          <BackIcon />
        </TouchableOpacity>
        <View>
          <Text className="font-[600] text-[20px] leading-[30px]">
            Issues with previous orders
          </Text>
        </View>
      </View>
      <View className="mt-8">
        <SlideComponent text={"I did not receive this order"} />
      </View>
      <View className="">
        <SlideComponent text={"Item(s) portion size is not adequate"} />
      </View>
      <View className="">
        <SlideComponent text={"Report a Safety Incident"} />
      </View>
      <View className="">
        <SlideComponent text={"Few item(s) are missing in my order"} />
      </View>
      <View className="">
        <SlideComponent text={"Item(s) delivered are incorrect or wrong"} />
      </View>
      <View className="">
        <SlideComponent text={"Item(s) quality is poor"} />
      </View>
      <View className="">
        <SlideComponent text={"Item(s) has spillage issue"} />
      </View>
      <View className="">
        <SlideComponent text={"Report a delivery Partner fraud incident"} />
      </View>
      <View className="">
        <SlideComponent text={"Payment and Billing related query"} />
      </View>
    </SafeAreaView>
  );
};

export default issueswithpreviousorders;
