import { Text, TouchableOpacity, View } from "react-native";
import React, { useEffect } from "react";
import { Image } from "react-native";

const NotificationComponent = ({ items }, isLoading) => {
  return (
    <>
      <TouchableOpacity
        className="flex-row items-center space-x-[30px] px-9 py-5"
        style={{
          backgroundColor: items?.is_read ? "#FFFFFF" : "#F5FFF6",
        }}
      >
        <Image
          source={{ uri: items?.item?.image }}
          className="h-[30px] w-[30px]"
        />
        <View className="space-y-[5px]">
          <Text className="font-[400] text-[16px] leading-[24px]">
            {items?.item?.title}
          </Text>
          <Text className="font-[400] text-[12px] leading-[12px] underline">
            {items?.item?.description}
          </Text>
          <Text className="font-[400] text-[12px] leading-[18pc] text-[#627164]">
            1 mins ago
          </Text>
        </View>
      </TouchableOpacity>
    </>
  );
};

export default NotificationComponent;
