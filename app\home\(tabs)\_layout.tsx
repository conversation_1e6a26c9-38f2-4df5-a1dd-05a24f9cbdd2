import * as Location from "expo-location";
import ClipboardIcon from "../../../assets/tabsicon/clipboard.svg";
import HomeIcon from "../../../assets/tabsicon/home.svg";
import ProfileIcon from "../../../assets/tabsicon/Profile.svg";
import React, { useEffect, useState } from "react";
import TrophyIcon from "../../../assets/tabsicon/trophy.svg";
import useLoadData from "@/hooks/useLoadDasAndPro/useLoadData";
import useLoadOrders from "@/hooks/useLoadOrders/useLoadOrders";
import useSetApiData from "../../../hooks/useSetApiData";
import { Tabs } from "expo-router";
import { ActivityIndicator, Text, View } from "react-native";
import { Alert } from "react-native";

const _layout = () => {
  const { SetFunction, data } = useSetApiData({
    endpoint: "auth/set_current_location",
  });
  const { isLoading } = useLoadData();

  const DeliveryPerson = async () => {
    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Permission denied",
        "Allow location access to use this feature."
      );
      return;
    }

    let location = await Location.getCurrentPositionAsync({});
    const { latitude, longitude } = location.coords;
    SetFunction({
      latitude: latitude,
      longitude: longitude,
    });
  };
  useEffect(() => {
    DeliveryPerson();
  }, []);
  return (
    <>
      {isLoading ? (
        <View className="flex-1 items-center justify-center">
          <ActivityIndicator size={"large"} />
        </View>
      ) : (
        <Tabs
          screenOptions={{
            tabBarStyle: {
              backgroundColor: "#fff",
              height: 76,
              paddingTop: 16,
            },
            tabBarLabelStyle: {
              fontSize: 12,
              fontWeight: "500",
              fontFamily: "Poppins",
            },
            tabBarActiveTintColor: "#00660A",
            tabBarInactiveTintColor: "#939D94",
          }}
        >
          <Tabs.Screen
            name="home"
            options={{
              headerShown: false,
              title: "Home",
              tabBarIcon: ({ focused }) => {
                return (
                  <View className="items-center justify-center">
                    <HomeIcon stroke={focused ? "#00660A" : "#939D94"} />
                  </View>
                );
              },
            }}
          />

          <Tabs.Screen
            name="profile"
            options={{
              headerShown: false,
              title: "Profile",
              tabBarIcon: ({ focused }) => {
                return (
                  <View className="items-center justify-center">
                    <ProfileIcon fill={focused ? "#00660A" : "#939D94"} />
                  </View>
                );
              },
            }}
          />
        </Tabs>
      )}
    </>
  );
};

export default _layout;
