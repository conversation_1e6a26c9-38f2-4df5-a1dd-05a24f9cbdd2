<svg width="45" height="45" viewBox="0 0 45 45" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_251_9390)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M35.0124 9.60984C33.7468 9.25828 32.432 9.98952 32.0677 11.2622C32.0236 11.416 31.9496 11.5596 31.85 11.6849C31.7503 11.8101 31.627 11.9145 31.487 11.9921C31.347 12.0696 31.1931 12.1189 31.0341 12.1369C30.8751 12.155 30.7141 12.1416 30.5602 12.0975C30.4064 12.0534 30.2628 11.9794 30.1375 11.8797C30.0123 11.7801 29.9079 11.6567 29.8304 11.5167C29.7528 11.3768 29.7036 11.2229 29.6855 11.0639C29.6674 10.9048 29.6808 10.7438 29.7249 10.59C30.0744 9.3662 30.8928 8.32978 32.0021 7.70598C33.1115 7.08218 34.4222 6.9214 35.6495 7.25859C35.6973 7.26984 35.7929 7.29374 35.8801 7.32328C39.3859 8.49187 39.6643 13.2492 36.5199 14.9873L29.5309 18.8545C29.3909 18.932 29.237 18.9811 29.078 18.9991C28.9191 19.0171 28.7581 19.0037 28.6044 18.9595C28.4506 18.9153 28.307 18.8412 28.1819 18.7415C28.0568 18.6418 27.9525 18.5185 27.875 18.3785C27.7975 18.2385 27.7484 18.0847 27.7304 17.9257C27.7124 17.7667 27.7259 17.6058 27.7701 17.452C27.8143 17.2982 27.8883 17.1547 27.988 17.0295C28.0877 16.9044 28.211 16.8001 28.351 16.7226L35.3401 12.8555C36.721 12.0919 36.4974 10.1048 35.1165 9.63796L35.1081 9.63515C35.0763 9.62635 35.0444 9.61791 35.0124 9.60984Z" fill="#FFD340"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M35.0124 9.60984C33.7468 9.25828 32.432 9.98952 32.0677 11.2622C32.0236 11.416 31.9496 11.5596 31.85 11.6849C31.7503 11.8101 31.627 11.9145 31.487 11.9921C31.347 12.0696 31.1931 12.1189 31.0341 12.1369C30.8751 12.155 30.7141 12.1416 30.5602 12.0975C30.4064 12.0534 30.2628 11.9794 30.1375 11.8797C30.0123 11.7801 29.9079 11.6567 29.8304 11.5167C29.7528 11.3768 29.7036 11.2229 29.6855 11.0639C29.6674 10.9048 29.6808 10.7438 29.7249 10.59C30.0744 9.3662 30.8928 8.32978 32.0021 7.70598C33.1115 7.08218 34.4222 6.9214 35.6495 7.25859C35.6973 7.26984 35.7929 7.29374 35.8801 7.32328C39.3859 8.49187 39.6643 13.2492 36.5199 14.9873L29.5309 18.8545C29.3909 18.932 29.237 18.9811 29.078 18.9991C28.9191 19.0171 28.7581 19.0037 28.6044 18.9595C28.4506 18.9153 28.307 18.8412 28.1819 18.7415C28.0568 18.6418 27.9525 18.5185 27.875 18.3785C27.7975 18.2385 27.7484 18.0847 27.7304 17.9257C27.7124 17.7667 27.7259 17.6058 27.7701 17.452C27.8143 17.2982 27.8883 17.1547 27.988 17.0295C28.0877 16.9044 28.211 16.8001 28.351 16.7226L35.3401 12.8555C36.721 12.0919 36.4974 10.1048 35.1165 9.63796L35.1081 9.63515C35.0763 9.62635 35.0444 9.61791 35.0124 9.60984Z" fill="url(#paint0_linear_251_9390)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M35.0124 9.60984C33.7468 9.25828 32.432 9.98952 32.0677 11.2622C32.0236 11.416 31.9496 11.5596 31.85 11.6849C31.7503 11.8101 31.627 11.9145 31.487 11.9921C31.347 12.0696 31.1931 12.1189 31.0341 12.1369C30.8751 12.155 30.7141 12.1416 30.5602 12.0975C30.4064 12.0534 30.2628 11.9794 30.1375 11.8797C30.0123 11.7801 29.9079 11.6567 29.8304 11.5167C29.7528 11.3768 29.7036 11.2229 29.6855 11.0639C29.6674 10.9048 29.6808 10.7438 29.7249 10.59C30.0744 9.3662 30.8928 8.32978 32.0021 7.70598C33.1115 7.08218 34.4222 6.9214 35.6495 7.25859C35.6973 7.26984 35.7929 7.29374 35.8801 7.32328C39.3859 8.49187 39.6643 13.2492 36.5199 14.9873L29.5309 18.8545C29.3909 18.932 29.237 18.9811 29.078 18.9991C28.9191 19.0171 28.7581 19.0037 28.6044 18.9595C28.4506 18.9153 28.307 18.8412 28.1819 18.7415C28.0568 18.6418 27.9525 18.5185 27.875 18.3785C27.7975 18.2385 27.7484 18.0847 27.7304 17.9257C27.7124 17.7667 27.7259 17.6058 27.7701 17.452C27.8143 17.2982 27.8883 17.1547 27.988 17.0295C28.0877 16.9044 28.211 16.8001 28.351 16.7226L35.3401 12.8555C36.721 12.0919 36.4974 10.1048 35.1165 9.63796L35.1081 9.63515C35.0763 9.62635 35.0444 9.61791 35.0124 9.60984Z" fill="url(#paint1_radial_251_9390)"/>
</g>
<g filter="url(#filter1_i_251_9390)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.35192 7.25887C10.5795 6.92132 11.8908 7.08208 13.0005 7.7062C14.1102 8.33032 14.9287 9.36732 15.2779 10.5917C15.3668 10.9024 15.3287 11.2356 15.1719 11.5182C15.0151 11.8008 14.7525 12.0095 14.4418 12.0985C14.1312 12.1874 13.7979 12.1493 13.5153 11.9925C13.2327 11.8358 13.024 11.5731 12.935 11.2625C12.8488 10.9582 12.7032 10.6741 12.5065 10.4265C12.3099 10.1789 12.0661 9.97269 11.7893 9.81983C11.5125 9.66698 11.2081 9.57049 10.8938 9.53595C10.5795 9.5014 10.2614 9.52948 9.95801 9.61856C9.93641 9.62356 9.91485 9.62872 9.89332 9.63403L9.88489 9.63684C8.50395 10.1037 8.28036 12.0907 9.66129 12.8543L16.6504 16.7215C16.7903 16.799 16.9137 16.9033 17.0134 17.0284C17.113 17.1536 17.1871 17.2971 17.2313 17.4509C17.2755 17.6046 17.289 17.7656 17.271 17.9246C17.253 18.0835 17.2038 18.2374 17.1264 18.3774C17.0489 18.5174 16.9446 18.6407 16.8195 18.7404C16.6943 18.8401 16.5508 18.9141 16.397 18.9583C16.2433 19.0025 16.0823 19.016 15.9233 18.998C15.7644 18.98 15.6105 18.9309 15.4705 18.8534L8.48145 14.9862C5.33848 13.2481 5.61692 8.49075 9.1227 7.32216C9.20848 7.29403 9.30551 7.26872 9.35192 7.25747V7.25887Z" fill="#EAA73A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.35192 7.25887C10.5795 6.92132 11.8908 7.08208 13.0005 7.7062C14.1102 8.33032 14.9287 9.36732 15.2779 10.5917C15.3668 10.9024 15.3287 11.2356 15.1719 11.5182C15.0151 11.8008 14.7525 12.0095 14.4418 12.0985C14.1312 12.1874 13.7979 12.1493 13.5153 11.9925C13.2327 11.8358 13.024 11.5731 12.935 11.2625C12.8488 10.9582 12.7032 10.6741 12.5065 10.4265C12.3099 10.1789 12.0661 9.97269 11.7893 9.81983C11.5125 9.66698 11.2081 9.57049 10.8938 9.53595C10.5795 9.5014 10.2614 9.52948 9.95801 9.61856C9.93641 9.62356 9.91485 9.62872 9.89332 9.63403L9.88489 9.63684C8.50395 10.1037 8.28036 12.0907 9.66129 12.8543L16.6504 16.7215C16.7903 16.799 16.9137 16.9033 17.0134 17.0284C17.113 17.1536 17.1871 17.2971 17.2313 17.4509C17.2755 17.6046 17.289 17.7656 17.271 17.9246C17.253 18.0835 17.2038 18.2374 17.1264 18.3774C17.0489 18.5174 16.9446 18.6407 16.8195 18.7404C16.6943 18.8401 16.5508 18.9141 16.397 18.9583C16.2433 19.0025 16.0823 19.016 15.9233 18.998C15.7644 18.98 15.6105 18.9309 15.4705 18.8534L8.48145 14.9862C5.33848 13.2481 5.61692 8.49075 9.1227 7.32216C9.20848 7.29403 9.30551 7.26872 9.35192 7.25747V7.25887Z" fill="url(#paint2_linear_251_9390)"/>
</g>
<g filter="url(#filter2_i_251_9390)">
<path d="M24.85 24.637V18.7026H20.1531V24.637C20.1531 25.6776 19.6891 26.676 18.8875 27.337L16.1875 29.587H28.8156L26.1156 27.337C25.7204 27.0066 25.4023 26.5936 25.1837 26.1272C24.965 25.6608 24.8512 25.1521 24.85 24.637Z" fill="url(#paint3_linear_251_9390)"/>
<path d="M24.85 24.637V18.7026H20.1531V24.637C20.1531 25.6776 19.6891 26.676 18.8875 27.337L16.1875 29.587H28.8156L26.1156 27.337C25.7204 27.0066 25.4023 26.5936 25.1837 26.1272C24.965 25.6608 24.8512 25.1521 24.85 24.637Z" fill="url(#paint4_radial_251_9390)"/>
</g>
<path d="M24.85 24.637V18.7026H20.1531V24.637C20.1531 25.6776 19.6891 26.676 18.8875 27.337L16.1875 29.587H28.8156L26.1156 27.337C25.7204 27.0066 25.4023 26.5936 25.1837 26.1272C24.965 25.6608 24.8512 25.1521 24.85 24.637Z" fill="url(#paint5_radial_251_9390)"/>
<path d="M22.5008 23.2733C19.9012 23.2733 17.4082 22.2406 15.57 20.4025C13.7319 18.5643 12.6992 16.0712 12.6992 13.4717V3.82482C12.6992 3.27639 13.1492 2.82639 13.6977 2.82639H31.318C31.8664 2.82639 32.3164 3.27639 32.3164 3.82482V13.4717C32.3164 18.8858 27.9289 23.2733 22.5008 23.2733Z" fill="#EA873A"/>
<path d="M22.5008 23.2733C19.9012 23.2733 17.4082 22.2406 15.57 20.4025C13.7319 18.5643 12.6992 16.0712 12.6992 13.4717V3.82482C12.6992 3.27639 13.1492 2.82639 13.6977 2.82639H31.318C31.8664 2.82639 32.3164 3.27639 32.3164 3.82482V13.4717C32.3164 18.8858 27.9289 23.2733 22.5008 23.2733Z" fill="url(#paint6_radial_251_9390)"/>
<path d="M22.5008 23.2733C19.9012 23.2733 17.4082 22.2406 15.57 20.4025C13.7319 18.5643 12.6992 16.0712 12.6992 13.4717V3.82482C12.6992 3.27639 13.1492 2.82639 13.6977 2.82639H31.318C31.8664 2.82639 32.3164 3.27639 32.3164 3.82482V13.4717C32.3164 18.8858 27.9289 23.2733 22.5008 23.2733Z" fill="url(#paint7_radial_251_9390)"/>
<path d="M22.5008 23.2733C19.9012 23.2733 17.4082 22.2406 15.57 20.4025C13.7319 18.5643 12.6992 16.0712 12.6992 13.4717V3.82482C12.6992 3.27639 13.1492 2.82639 13.6977 2.82639H31.318C31.8664 2.82639 32.3164 3.27639 32.3164 3.82482V13.4717C32.3164 18.8858 27.9289 23.2733 22.5008 23.2733Z" fill="url(#paint8_radial_251_9390)"/>
<g filter="url(#filter3_i_251_9390)">
<path d="M32.2338 29.5874H12.7713C11.7447 29.5874 10.8729 30.3046 10.676 31.303L8.52441 41.2733C8.42598 41.7374 8.77754 42.1733 9.25566 42.1733H35.7635C36.2416 42.1733 36.5932 41.7374 36.4947 41.2733L34.3432 31.303C34.1322 30.3046 33.2604 29.5874 32.2338 29.5874Z" fill="url(#paint9_linear_251_9390)"/>
<path d="M32.2338 29.5874H12.7713C11.7447 29.5874 10.8729 30.3046 10.676 31.303L8.52441 41.2733C8.42598 41.7374 8.77754 42.1733 9.25566 42.1733H35.7635C36.2416 42.1733 36.5932 41.7374 36.4947 41.2733L34.3432 31.303C34.1322 30.3046 33.2604 29.5874 32.2338 29.5874Z" fill="url(#paint10_linear_251_9390)"/>
<path d="M32.2338 29.5874H12.7713C11.7447 29.5874 10.8729 30.3046 10.676 31.303L8.52441 41.2733C8.42598 41.7374 8.77754 42.1733 9.25566 42.1733H35.7635C36.2416 42.1733 36.5932 41.7374 36.4947 41.2733L34.3432 31.303C34.1322 30.3046 33.2604 29.5874 32.2338 29.5874Z" fill="url(#paint11_linear_251_9390)"/>
</g>
<g filter="url(#filter4_f_251_9390)">
<path d="M26.3617 33.5864H17.8961C17.4039 33.5864 16.9961 33.9801 16.9961 34.4864V37.0317C16.9961 37.5239 17.3898 37.9317 17.8961 37.9317H26.3617C26.8539 37.9317 27.2617 37.5379 27.2617 37.0317V34.4864C27.2617 33.9942 26.868 33.5864 26.3617 33.5864Z" fill="#914556"/>
</g>
<g filter="url(#filter5_i_251_9390)">
<path d="M26.7328 33.3279H18.2672C17.775 33.3279 17.3672 33.7217 17.3672 34.2279V36.7733C17.3672 37.2654 17.7609 37.6733 18.2672 37.6733H26.7328C27.225 37.6733 27.6328 37.2795 27.6328 36.7733V34.2279C27.6328 33.7358 27.2391 33.3279 26.7328 33.3279Z" fill="url(#paint12_linear_251_9390)"/>
</g>
<g filter="url(#filter6_i_251_9390)">
<path d="M21.5498 8.05405H23.5931V18.2705H21.2579V10.5206L19.4481 11.0168L18.8789 9.01731L21.5498 8.05405Z" fill="#EE9C45"/>
</g>
<defs>
<filter id="filter0_i_251_9390" x="27.7227" y="6.94534" width="10.9844" height="12.0615" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.140625"/>
<feGaussianBlur stdDeviation="0.140625"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.937255 0 0 0 0 0.576471 0 0 0 0 0.247059 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_251_9390"/>
</filter>
<filter id="filter1_i_251_9390" x="6.29688" y="6.94537" width="10.9805" height="12.0604" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.140625"/>
<feGaussianBlur stdDeviation="0.140625"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.854902 0 0 0 0 0.458824 0 0 0 0 0.286275 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_251_9390"/>
</filter>
<filter id="filter2_i_251_9390" x="16.1875" y="18.7026" width="12.9102" height="11.025" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.28125" dy="0.140625"/>
<feGaussianBlur stdDeviation="0.140625"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.843137 0 0 0 0 0.462745 0 0 0 0 0.215686 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_251_9390"/>
</filter>
<filter id="filter3_i_251_9390" x="8.50781" y="29.5874" width="28.3695" height="12.9516" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.365625" dy="0.365625"/>
<feGaussianBlur stdDeviation="0.210938"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.509804 0 0 0 0 0.270588 0 0 0 0 0.270588 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_251_9390"/>
</filter>
<filter id="filter4_f_251_9390" x="16.7711" y="33.3614" width="10.7156" height="4.79531" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.1125" result="effect1_foregroundBlur_251_9390"/>
</filter>
<filter id="filter5_i_251_9390" x="17.3672" y="33.1873" width="10.4062" height="4.48593" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.140625" dy="-0.140625"/>
<feGaussianBlur stdDeviation="0.1125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.941176 0 0 0 0 0.533333 0 0 0 0 0.333333 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_251_9390"/>
</filter>
<filter id="filter6_i_251_9390" x="18.8789" y="8.05405" width="4.71484" height="10.2165" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.295833 0 0 0 0 0.124384 0 0 0 0 0.110937 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_251_9390"/>
</filter>
<linearGradient id="paint0_linear_251_9390" x1="31.7513" y1="16.7958" x2="36.8588" y2="12.1889" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEC551"/>
<stop offset="1" stop-color="#FEC551" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint1_radial_251_9390" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(31.8076 12.5222) rotate(-21.5519) scale(7.10223 6.54224)">
<stop offset="0.727" stop-color="#FFEC6A" stop-opacity="0"/>
<stop offset="1" stop-color="#FFEC6A"/>
</radialGradient>
<linearGradient id="paint2_linear_251_9390" x1="13.9335" y1="11.1893" x2="10.4361" y2="10.1909" gradientUnits="userSpaceOnUse">
<stop stop-color="#D27840"/>
<stop offset="1" stop-color="#D27840" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_251_9390" x1="24.3227" y1="27.787" x2="17.2042" y2="27.787" gradientUnits="userSpaceOnUse">
<stop stop-color="#FBC33A"/>
<stop offset="1" stop-color="#DB7B3C"/>
</linearGradient>
<radialGradient id="paint4_radial_251_9390" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(24.7192 24.1448) rotate(90) scale(2.93906 2.68066)">
<stop offset="0.193" stop-color="#FFE469"/>
<stop offset="1" stop-color="#FFE469" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint5_radial_251_9390" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(27.7539 28.6186) rotate(149.162) scale(3.19912 4.85726)">
<stop stop-color="#FFD34D"/>
<stop offset="1" stop-color="#FFD34D" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint6_radial_251_9390" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(26.0797 4.40842) rotate(83.702) scale(17.4708 11.2185)">
<stop offset="0.121" stop-color="#FFFA73"/>
<stop offset="1" stop-color="#FFFA73" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint7_radial_251_9390" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(26.1936 2.82639) rotate(90) scale(0.990478 11.0693)">
<stop stop-color="#FFF45B"/>
<stop offset="1" stop-color="#FFF45B" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint8_radial_251_9390" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(27.223 6.69357) rotate(121.572) scale(18.4656 17.7164)">
<stop offset="0.787" stop-color="#C55D73" stop-opacity="0"/>
<stop offset="1" stop-color="#C55D73"/>
</radialGradient>
<linearGradient id="paint9_linear_251_9390" x1="8.50754" y1="35.8761" x2="36.506" y2="35.8761" gradientUnits="userSpaceOnUse">
<stop stop-color="#9D5B6C"/>
<stop offset="1" stop-color="#955569"/>
</linearGradient>
<linearGradient id="paint10_linear_251_9390" x1="22.5096" y1="42.7204" x2="22.5096" y2="40.4338" gradientUnits="userSpaceOnUse">
<stop stop-color="#8C3A79"/>
<stop offset="1" stop-color="#8C3A79" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint11_linear_251_9390" x1="35.4569" y1="37.3105" x2="34.1379" y2="37.6185" gradientUnits="userSpaceOnUse">
<stop stop-color="#B0817C"/>
<stop offset="1" stop-color="#B0817C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint12_linear_251_9390" x1="28.1588" y1="35.5006" x2="17.3672" y2="35.5006" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE767"/>
<stop offset="1" stop-color="#FEBB5B"/>
</linearGradient>
</defs>
</svg>
