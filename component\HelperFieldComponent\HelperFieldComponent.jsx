import { View, Text, TouchableOpacity } from "react-native";
import React from "react";
import RightArrow from "../../assets/RightArrow.svg";

const HelperFieldComponent = ({ text, pressfun }) => {
  return (
    <>
      {/* <View className="h-[53px] justify-center border-b-[1px] border-[#E9EAE9]"> */}
      <View className="h-[53px] justify-center">
        <TouchableOpacity
          className="flex-row items-center px-4 justify-between"
          onPress={pressfun}
        >
          <Text className="max-w-[300px] font-[500] text-[14px] text-[#001A03] leading-[21px]">
            {text}
          </Text>
          <RightArrow />
        </TouchableOpacity>
      </View>
    </>
  );
};

export default HelperFieldComponent;
