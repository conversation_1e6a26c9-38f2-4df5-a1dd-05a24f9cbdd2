import AsyncStorage from "@react-native-async-storage/async-storage";

export abstract class LocalStorage {
  abstract saveLoginUserData(
    email: string,
    password: string,
    type: string
  ): Promise<void>;

  static async getToken(): Promise<string | null> {
    const token = await AsyncStorage.getItem("login");
    return token;
  }
  static async saveToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem("login", token);
    } catch (error) {
      console.log(error);
    }
  }
  static async removeToken(name: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(name);
    } catch (error) {
      console.log(error);
    }
  }
  static async logout(): Promise<void> {
    try {
      await AsyncStorage.removeItem("login");
    } catch (error) {
      console.log(error);
    }
  }
}

export class LocalStorageImpl extends LocalStorage {
  localStorage: typeof AsyncStorage;
  constructor() {
    super();
    this.localStorage = AsyncStorage;
  }

  async saveLoginUserData(
    email: string,
    password: string,
    type: string
  ): Promise<void> {
    try {
      await AsyncStorage.setItem("email", email);
      await AsyncStorage.setItem("password", password);
      await AsyncStorage.setItem("type", type);
    } catch (error) {
      console.log(error);
    }
  }
}
