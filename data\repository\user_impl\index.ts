import { Api } from "@/data/source/network/api";
import { UserRepository } from "@/domain/repository/user";

export class UserRepositoryimpl implements UserRepository {
  api: Api;
  constructor(api: Api) {
    this.api = api;
  }
  async getUserData(): Promise<UserEntity> {
    try {
      const data = await this.api.gatData<{ data: UserEntity }>("getProfile");
      if (data.status) {
        return data.data.data;
      }
      throw new Error("Dashboard data status is false");
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
