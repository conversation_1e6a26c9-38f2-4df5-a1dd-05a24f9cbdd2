import { View, Text, TouchableOpacity, Image } from "react-native";
import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { router, useLocalSearchParams } from "expo-router";
import BackArrow from "@/assets/icons/BackArrow.svg";
import BoxIcon from "@/assets/Hometab/box.svg";
import QrImage from "@/assets/Hometab/qrImage.png";
import CreatePaymentLink from "../../../../component/CreatePaymentLinkQrCode/CreatePaymentLinkQrCode";

const index = () => {
  const { amount } = useLocalSearchParams();
  return (
    <SafeAreaView>
      <View className="px-4 mt-3">
        <View className="flex-row relative items-center justify-start mt-4">
          <TouchableOpacity
            onPress={() => {
              router.back();
            }}
            className="absolute"
          >
            <View>
              <BackArrow />
            </View>
          </TouchableOpacity>
          <View className="flex-1 items-center justify-center">
            <Text className="font-[400] text-[19px] leading-[39px]">
              Confirm Payment
            </Text>
          </View>
        </View>
      </View>
      <View className="px-4 mt-4 ">
        <View className="flex-row p-4 items-center justify-between border-[1px] border-[#F5FFF6] rounded-[4px]">
          <View className="space-y-2">
            <Text className="">Amount to be paid</Text>
            <Text className="">*including taxes</Text>
          </View>
          <View className="">
            <Text className="">₹ {amount}</Text>
          </View>
        </View>
      </View>

      {/* <View className="px-4 mt-4">
        <TouchableOpacity
          onPress={() => {
            router.push("home/trackorder/payscreen/otp");
          }}
          className="flex-row items-center justify-center space-x-4 h-[65px] bg-[#00660A] rounded-[20px]"
        >
          <BoxIcon />
          <Text className="font-[600] font-Pop text-[16px] text-[#fff] leading-[24px]">
            Scan QR Code to accept payment
          </Text>
        </TouchableOpacity>
      </View> */}
      <CreatePaymentLink amount={amount} />
    </SafeAreaView>
  ); 
};

export default index;
