import React, { useCallback, useMemo, useRef, useState } from "react";
import {
  Button,
  LayoutChangeEvent,
  StyleSheet,
  Text,
  View,
} from "react-native";
import {
  Canvas,
  Group,
  Skia,
  Path,
  Mask,
  Rect,
  Image,
  useImage,
  SkPath,
} from "@shopify/react-native-skia";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import { svgPathProperties } from "svg-path-properties";
import OfferSvg from "../../assets/images/slidImage/Frame 1321317892.svg";

const Offer = ({ width, height }) => {
  const offerImage = useImage(require("../../assets/offer.png"));
  return (
    offerImage && (
      <Image image={offerImage} fit="contain" width={width} height={height} />
    )
  );
};

// Scratch pattern on which the user will perform the gesture
const ScratchPattern = ({ width, height }) => {
  const scratchPatternImage = useImage(
    require("../../assets/scratch-pattern.jpg")
  );

  return (
    scratchPatternImage && (
      <Image
        image={scratchPatternImage}
        fit="cover"
        width={width}
        height={height}
      />
    )
  );
};

export const ScratchCardComponent = () => {
  // Canvas dimensions
  const [canvasLayoutMeta, setCanvasLayoutMeta] = useState({
    width: 0,
    height: 0,
  });

  const STROKE_WIDTH = useRef(40);
  const totalAreaScratched = useRef(0);
  const [isScratched, setIsScratched] = useState(false);
  const [paths, setPaths] = useState([]);
  const pan = Gesture.Pan();
  try {
    pan
      .onStart((g) => {
        const newPaths = [...paths];
        const path = Skia.Path.Make();
        path.moveTo(g.x, g.y);
        newPaths.push(path);
        setPaths(newPaths);
      })
      .onUpdate((g) => {
        const newPaths = [...paths];
        const path = newPaths[newPaths.length - 1];
        try {
          path.lineTo(g.x, g.y);
        } catch (error) {
          console.log(error);
        }
        setPaths(newPaths);
      })
      .onEnd(() => {
        try {
          const pathProperties = new svgPathProperties(
            paths[paths.length - 1].toSVGString()
          );
          const pathArea =
            pathProperties.getTotalLength() * STROKE_WIDTH.current;
          totalAreaScratched.current += pathArea;
        } catch (error) {
          console.log(error);
        }

        const { width, height } = canvasLayoutMeta;
        const areaScratched =
          (totalAreaScratched.current / (width * height)) * 100;

        if (areaScratched > 70) {
          setIsScratched(true);
        }
      })
      .minDistance(1)
      .enabled(!isScratched);
  } catch (error) {
    console.log(error);
  }
  const handleCanvasLayout = useCallback((e) => {
    const { width, height } = e.nativeEvent.layout;
    setCanvasLayoutMeta({ width, height });
  }, []);

  const handleReset = () => {
    setIsScratched(false);
    setPaths([]);
    totalAreaScratched.current = 0;
  };

  const { width , height } = useMemo(() => canvasLayoutMeta, [canvasLayoutMeta]);

  return (
    <GestureDetector gesture={pan}>
      <View style={styles.container}>
        <Canvas onLayout={handleCanvasLayout} style={styles.canvas}>
          <Offer width={width} height={height} />
          {!isScratched ? (
            <Mask
              clip
              mode="luminance"
              mask={
                <Group>
                  <Rect
                    x={0}
                    y={0}
                    width={width}
                    height={height}
                    color="white"
                  />
                  {paths.map((p) => (
                    <Path
                      key={p.toSVGString()}
                      path={p}
                      strokeWidth={STROKE_WIDTH.current}
                      style="stroke"
                      strokeJoin={"round"}
                      strokeCap={"round"}
                      antiAlias
                      color={"black"}
                    />
                  ))}
                </Group>
              }
            >
              <ScratchPattern width={width} height={height} />
            </Mask>
          ) : (
            <Offer width={width} height={height} />
          )}
        </Canvas>
      </View>
    </GestureDetector>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "50%",
    height: "25%",
    backgroundColor: "#06D6A0",
  },
  canvas: {
    width: "100%",
    height: "100%",
  },
  buttonContainer: {
    marginTop: 50,
  },
});
export default ScratchCardComponent;
