import AsyncStorage from "@react-native-async-storage/async-storage";
import Constants from "expo-constants";
import axios from "axios";
import { useEffect, useState } from "react";

const useGetApiDatawithParam2 = ({ endpoint, param }) => {
  const [data, setdata] = useState(null);
  const [isLoading, setLoading] = useState(false);
  const [reFresh, setreFresh] = useState(false);
  const LoginFunction = async () => {
    await setLoading(true);
    const token = await AsyncStorage.getItem("login");
    await axios
      .post(`${Constants.expoConfig?.extra?.BASE_URL}${endpoint}`, param, {
        headers: {
          "X-Powered-By": "Express",
          "Access-Control-Allow-Origin": "*",
          "Content-Type": "application/json",
          Authorization: token,
        },
      })
      .then((val) => {
        return val;
      })
      .then((datas) => {
        setdata(datas);
      })
      .catch((error) => {
        console.error(error.response ? error.response.data : error.message);
      });
    await setLoading(false);
  };

  const triggerreFresh = () => {
    setreFresh(!reFresh);
  };
  useEffect(() => {
    LoginFunction();
  }, [reFresh]);

  return { isLoading, data, triggerreFresh };
};

export default useGetApiDatawithParam2;
