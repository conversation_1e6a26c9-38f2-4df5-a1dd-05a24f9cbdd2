import { View, Text, Modal, Image, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";

const PopUpComponent = ({ modle, setmodle, onpressfun, text }) => {
  return (
    <View className="relative flex-1">
      <Modal transparent={true} visible={modle}>
        <View className="flex-1 items-center justify-center">
          <LinearGradient
            colors={["#000", "#000"]}
            start={[0, 0]}
            style={{
              position: "absolute",
              left: 0,
              right: 0,
              top: 0,
              bottom: 0,
              zIndex: 1,
              flex: 1,
              opacity: 0.8,
            }}
          />
          <View className="w-[310px] z-50 h-[294px] bg-[#fff] rounded-[20px] justify-between">
            <View className="items-center mt-8">
              <View className="mt-2">
                <Text className="font-[600] text-[24px] leading-[24px] text-[#0E0E0E]">
                  Confirmation
                </Text>
              </View>
              <View className="mt-2 px-5">
                <Text className="font-[400] text-center text-[16px] leading-[20px] text-[#616161]">
                  Are you sure you arrived at the {text} location ?
                </Text>
              </View>
            </View>
            <View className="space-y-3">
              <View className="items-center px-5">
                <TouchableOpacity
                  onPress={() => {
                    setmodle(false);
                    onpressfun();
                  }}
                  className="h-[48px] bg-[#00660A] w-full rounded-[8px] items-center justify-center"
                >
                  <Text className="font-[500] text-[16px] text-[#FFFFFF] leading-[24px]">
                    Confirm
                  </Text>
                </TouchableOpacity>
              </View>
              <View className="items-center mb-4 px-5">
                <TouchableOpacity
                  onPress={() => {
                    setmodle(false);
                  }}
                  className="h-[48px] border-[1px] border-[#00660A] w-full rounded-[8px] items-center justify-center"
                >
                  <Text className="font-[500] text-[16px] text-[#00660A] leading-[24px]">
                    Cancel
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default PopUpComponent;
