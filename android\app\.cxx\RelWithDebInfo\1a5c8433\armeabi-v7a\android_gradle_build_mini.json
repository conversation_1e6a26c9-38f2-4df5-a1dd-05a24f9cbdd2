{"buildFiles": ["D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\slider\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\.cxx\\RelWithDebInfo\\1a5c8433\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\.cxx\\RelWithDebInfo\\1a5c8433\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_RNCSlider::@4898bc4726ecf1751b6a": {"artifactName": "react_codegen_RNCSlider", "abi": "armeabi-v7a", "output": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433\\obj\\armeabi-v7a\\libreact_codegen_RNCSlider.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6dd910b94089a928dab663afb856f4b\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "armeabi-v7a", "output": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6dd910b94089a928dab663afb856f4b\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"artifactName": "react_codegen_rngesturehandler_codegen", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "armeabi-v7a", "output": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6dd910b94089a928dab663afb856f4b\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so"]}, "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2": {"artifactName": "react_codegen_RNDateTimePickerCGen", "abi": "armeabi-v7a", "runtimeFiles": []}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "armeabi-v7a", "output": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433\\obj\\armeabi-v7a\\libappmodules.so", "runtimeFiles": ["D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433\\obj\\armeabi-v7a\\libreact_codegen_RNCSlider.so", "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433\\obj\\armeabi-v7a\\libreact_codegen_rnsvg.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6dd910b94089a928dab663afb856f4b\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "react_codegen_rnskia::@376d8504f62839611b97": {"artifactName": "react_codegen_rnskia", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"artifactName": "react_codegen_rnsvg", "abi": "armeabi-v7a", "output": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\1a5c8433\\obj\\armeabi-v7a\\libreact_codegen_rnsvg.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6dd910b94089a928dab663afb856f4b\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so"]}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "armeabi-v7a", "runtimeFiles": []}}}