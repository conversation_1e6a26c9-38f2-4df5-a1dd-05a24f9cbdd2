import { View, Text, TouchableOpacity, TextInput } from "react-native";
import React, { useEffect, useState } from "react";
import { Controller } from "react-hook-form";
import ProfileOtpFiled from "./ProfileOtpFiled";
import { useConformNumberEmail } from "../store";
import useSetApiData from "../hooks/useSetApiData";
import useGetApiData from "../hooks/useGetApiData";
import useGetApiDataFun from "../hooks/useGetApiDataFun";

const TextinputWithEdit = (props) => {
  const [editable, seteditable] = useState(false);
  const [sendOtp, setsendOtp] = useState(false);
  const [otp, setotp] = useState("");
  const [resendOtp, setresendOtp] = useState(false);
  const [count, setcount] = useState(0);
  const inerval = () => {
    setInterval(() => {
      setcount((count) => {
        if (count > 0) {
          return count - 1;
        } else {
          clearInterval();
          setresendOtp(false);
        }
      });
    }, 1000);
  };

  const { DataFetcher: SendOtp } = useGetApiDataFun("send_otp");

  useEffect(() => {
    if (!props?.placeholder) {
      seteditable(true);
    }
  }, []);
  const { phone, email, setPhone, setEmail } = useConformNumberEmail(
    (state) => state
  );

  useEffect(() => {
    if (props?.name == "email") {
      setEmail(props?.placeholder);
    } else {
      setPhone(props?.placeholder);
    }
  }, []);

  return (
    <>
      <View className="mt-2">
        <Text className="font-[400] text-[16px] text-[#4D4D4D]">
          {props?.text}
        </Text>
        <View className="pl-2 border-[1px] border-[#ACB9D5] h-[40px] items-center flex-row mt-2 rounded-[4px]">
          <Controller
            control={props?.control}
            name={props?.name}
            rules={{
              ...props?.rules,
            }}
            defaultValue={props?.placeholder}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                value={value}
                onChangeText={(value) => {
                  onChange(value);
                }}
                className="flex-1"
                style={{
                  color: editable ? "#000" : "#B3B3B3",
                  backgroundColor: "#fff",
                }}
                autoCapitalize="none"
                keyboardType={
                  props?.name === "number" ? "numeric" : "email-address"
                }
                editable={editable}
                placeholder={props?.place}
              />
            )}
          />
          <>
            {editable &&
            (props?.name === "email" || props?.name === "number") &&
            props?.changes ? (
              <>
                <TouchableOpacity
                  onPress={() => {
                    setsendOtp(true);
                    if (!sendOtp) {
                      SendOtp({
                        contact_type:
                          props?.name === "email" ? props?.name : "phone",
                        contact_value: props?.watch(props?.name),
                      }).then((val) => {
                        setotp(val?.otp);
                      });
                    }
                  }}
                  className="w-[124px] h-full bg-[#00660A] items-center justify-center rounded-[4px]"
                >
                  <Text className="font-[400] text-[16px] text-[#FFFFFF]">
                    Send OTP
                  </Text>
                </TouchableOpacity>
              </>
            ) : (
              <>
                {!props?.removeBtn && (
                  <>
                    {editable ? (
                      <>
                        <TouchableOpacity
                          onPress={() => {
                            seteditable(!editable);
                          }}
                          className="pr-2"
                        >
                          <Text className="font-[500] text-[14px] leading-[21px] text-[#00660A]">
                            Save
                          </Text>
                        </TouchableOpacity>
                      </>
                    ) : (
                      <>
                        <TouchableOpacity
                          onPress={() => {
                            seteditable(!editable);
                          }}
                          className="pr-2"
                        >
                          <Text className="font-[500] text-[14px] leading-[21px] text-[#00660A]">
                            Edit
                          </Text>
                        </TouchableOpacity>
                      </>
                    )}
                  </>
                )}
              </>
            )}
          </>
        </View>

        {sendOtp && (props?.name === "email" || props?.name === "number") ? (
          <>
            <View className="mt-3">
              <View className="flex-row items-center">
                <Text className="font-[400] text-[14px] leading-[24px] text-[#627164]">
                  Didn’t get it?
                </Text>
                <TouchableOpacity
                  disabled={resendOtp}
                  onPress={() => {
                    inerval();
                    setcount(60);
                    setresendOtp(true);
                    SendOtp({
                      contact_type:
                        props?.name === "email" ? props?.name : "phone",
                      contact_value: props?.watch(props?.name),
                    }).then((val) => {
                      setotp(val?.otp);
                    });
                  }}
                >
                  <Text className="font-[500] text-[#D1AE00]">
                    {resendOtp ? <>{count}</> : <>Resend</>}
                  </Text>
                </TouchableOpacity>
              </View>
              <ProfileOtpFiled
                setsendOtp={setsendOtp}
                seteditable={seteditable}
                otp={otp}
                filedName={props?.name}
                watch={props?.watch}
                control={props?.control}
                name={`otp_${props?.name}`}
              />
            </View>
          </>
        ) : (
          <></>
        )}
      </View>
    </>
  );
};

export default TextinputWithEdit;
