import { View, Text } from "react-native";
import React, { memo } from "react";
import Logoimg from "../../assets/loading/OrdaLane Transparent Logo-03.png";
import { Image } from "react-native";
import DriverIcon from "../../assets/loading/DriverIcon.svg";

const LoadingComponent = () => {
  return (
    <>
      <View className="flex-1 bg-[#006634] items-center justify-center">
        <DriverIcon />
        <View className="relative items-center justify-center mt-4">
          <Image
            source={Logoimg}
            style={{
              objectFit: "contain",
              width: 300,
              height: 70,
            }}
          />
          <Text className="font-Pop font-[500] text-[40px] leading-[60px] text-[#fff]">
            Delivery Partner
          </Text>
        </View>
      </View>
    </>
  );
};

export default LoadingComponent;
