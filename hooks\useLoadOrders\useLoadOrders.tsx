import React, { useEffect, useState } from "react";
import { Text, View } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { loadOrders } from "@/redux/orders/asyncReducers";
import { loadProfile } from "@/redux/profile/asyncReducers";
import { selectProfile } from "@/redux/profile/meno";
import { AppDispacher } from "@/redux/store";

const useLoadOrders = () => {
  const [isLoading, setLoading] = useState(false);
  const dispacher = useDispatch<AppDispacher>();
  const profile = useSelector(selectProfile);
  const LoadData = async () => {
    try {
      setLoading(true);
      await dispacher(loadProfile());
      console.log(profile);
      await dispacher(loadOrders({ id: profile.id }));
      setLoading(false);
    } catch (error) {
      console.log(error);
    }
  };
  useEffect(() => {
    LoadData();
  }, []);
  return { isLoading, LoadData };
};

export default useLoadOrders;
