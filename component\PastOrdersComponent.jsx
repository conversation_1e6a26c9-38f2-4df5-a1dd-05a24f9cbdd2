import { View, Text, Image, TouchableOpacity } from "react-native";
import React from "react";
import resImage from "../assets/sidemenuassert/Rectangle 592762.png";
import RightArrow from "../assets/sidemenuassert/rightArrow.svg";
import VegIcon from "../assets/sidemenuassert/VegIcon.svg";
import Cycleicon from "../assets/sidemenuassert/cycleicon.svg";
import { router } from "expo-router";

const PastOrdersComponent = () => {
  return (
    <View className="px-4">
      <View
        className=""
        style={{
          backgroundColor: "#fff",
          shadowColor: "#E9EAE9",
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 1,
          shadowRadius: 12,
          elevation: 12,
        }}
      >
        <View className="flex-row items-start p-3">
          <View className="flex-grow">
            <Image source={resImage} />
          </View>
          <View
            className="flex-grow"
            style={{
              alignSelf: "center",
            }}
          >
            <Text className="font-[500] text-[16px] leading-[24px] text-[#001A03]">
              Cold Stone Creamery
            </Text>
            <Text>Miraya Rose, Bangalore</Text>
            <Text>24 mins</Text>
          </View>
          <TouchableOpacity
            onPress={() => {
              router.push({
                pathname: `home/cart/${1}`,
                params: {
                  name: "Cold Stone Creamery",
                  reating: "4.2",
                  // ...item,
                },
              });
            }}
            className="flex-grow flex-row items-center space-x-3"
            style={{
              alignSelf: "flex-end",
            }}
          >
            <Text className="font-[500] text-[12px] leading-[18px] text-[#00660A]">
              View Menu
            </Text>
            <RightArrow />
          </TouchableOpacity>
        </View>
        <View className="bg-[#E9EAE9] h-[1px] mt-2" />
        <View className="flex-row items-start space-x-2 p-3">
          <View>
            <VegIcon />
          </View>
          <View className="space-y-1">
            <Text>1 X French Vanilla</Text>
            <Text>90 Grams</Text>
          </View>
        </View>
        <View className="bg-[#E9EAE9] h-[1px] mt-1" />
        <TouchableOpacity
          onPress={() => {
            router.push("home/sidemenu/pastorder/orderdetails");
          }}
          className="flex-row p-3 justify-between"
        >
          <View>
            <Text>25th Apr 2024 at 12:40AM</Text>
          </View>
          <View className="flex-row items-center space-x-2">
            <Text>₹452.5</Text>
            <RightArrow />
          </View>
        </TouchableOpacity>
        <View className="bg-[#E9EAE9] h-[1px] mt-1" />
        <View className="p-3 items-end">
          <TouchableOpacity className="flex-row items-center justify-center h-[29px] w-[104px] bg-[#00660A] rounded-[4px]">
            <Cycleicon />
            <Text className="font-[500] leading-[21px] text-[14px] text-[#fff]">
              Reorder
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default PastOrdersComponent;
