import { View, Text, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import RsIcon from "@/assets/images/CardImages/RsIcon.svg";
import RadioBtn from "../assets/OrderDetails/redioBtn.svg";

const VariantComponent = (props) => {
  const [IsActive, setIsActive] = useState(props?.isActive);
  return (
    <View className="flex-row justify-between mb-5">
      <View>
        <Text>{props?.gm} Grams</Text>
      </View>
      <View className="flex-row space-x-4">
        <View className="flex-row items-center space-x-1">
          <RsIcon />
          <Text>{props?.amount}</Text>
        </View>
        <TouchableOpacity>
          <RadioBtn fill={IsActive ? "#00660A" : "#B3B3B3"} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default VariantComponent;
