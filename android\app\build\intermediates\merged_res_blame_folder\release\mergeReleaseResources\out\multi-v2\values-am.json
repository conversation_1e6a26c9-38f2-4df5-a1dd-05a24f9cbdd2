{"logs": [{"outputFile": "com.mohandhass2.myapp-mergeReleaseResources-83:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a39c8a492fe916611935c0d3835604e6\\transformed\\material-1.6.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,216,291,380,482,559,623,708,770,828,913,975,1033,1099,1161,1216,1312,1369,1428,1484,1551,1656,1736,1817,1916,1989,2060,2142,2209,2275,2341,2414,2495,2563,2636,2707,2774,2859,2926,3013,3101,3175,3243,3328,3379,3443,3523,3605,3667,3731,3794,3889,3978,4063,4154", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,74,88,101,76,63,84,61,57,84,61,57,65,61,54,95,56,58,55,66,104,79,80,98,72,70,81,66,65,65,72,80,67,72,70,66,84,66,86,87,73,67,84,50,63,79,81,61,63,62,94,88,84,90,75", "endOffsets": "211,286,375,477,554,618,703,765,823,908,970,1028,1094,1156,1211,1307,1364,1423,1479,1546,1651,1731,1812,1911,1984,2055,2137,2204,2270,2336,2409,2490,2558,2631,2702,2769,2854,2921,3008,3096,3170,3238,3323,3374,3438,3518,3600,3662,3726,3789,3884,3973,4058,4149,4225"}, "to": {"startLines": "19,50,58,59,60,85,137,141,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "714,3493,4255,4344,4446,7055,10631,11014,11237,11295,11380,11442,11500,11566,11628,11683,11779,11836,11895,11951,12018,12123,12203,12284,12383,12456,12527,12609,12676,12742,12808,12881,12962,13030,13103,13174,13241,13326,13393,13480,13568,13642,13710,13795,13846,13910,13990,14072,14134,14198,14261,14356,14445,14530,14781", "endLines": "22,50,58,59,60,85,137,141,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,192", "endColumns": "12,74,88,101,76,63,84,61,57,84,61,57,65,61,54,95,56,58,55,66,104,79,80,98,72,70,81,66,65,65,72,80,67,72,70,66,84,66,86,87,73,67,84,50,63,79,81,61,63,62,94,88,84,90,75", "endOffsets": "870,3563,4339,4441,4518,7114,10711,11071,11290,11375,11437,11495,11561,11623,11678,11774,11831,11890,11946,12013,12118,12198,12279,12378,12451,12522,12604,12671,12737,12803,12876,12957,13025,13098,13169,13236,13321,13388,13475,13563,13637,13705,13790,13841,13905,13985,14067,14129,14193,14256,14351,14440,14525,14616,14852"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\00636ad238812e00d92433007e026a91\\transformed\\exoplayer-core-2.18.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,175,237,297,369,432,521,602", "endColumns": "61,57,61,59,71,62,88,80,65", "endOffsets": "112,170,232,292,364,427,516,597,663"}, "to": {"startLines": "110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8942,9004,9062,9124,9184,9256,9319,9408,9489", "endColumns": "61,57,61,59,71,62,88,80,65", "endOffsets": "8999,9057,9119,9179,9251,9314,9403,9484,9550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6591935515ee6cd5ec7881ab1d3c64e\\transformed\\core-1.13.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "51,52,53,54,55,56,57,200", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3568,3661,3761,3858,3957,4053,4155,15384", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "3656,3756,3853,3952,4048,4150,4250,15480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eeb7a35c1d2dda21edaace2253c1f099\\transformed\\exoplayer-ui-2.18.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,482,664,744,825,901,986,1067,1133,1195,1279,1362,1429,1492,1553,1619,1719,1821,1922,1991,2067,2135,2201,2280,2360,2422,2487,2540,2597,2643,2704,2762,2837,2896,2958,3017,3074,3138,3201,3265,3315,3371,3441,3511", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,79,80,75,84,80,65,61,83,82,66,62,60,65,99,101,100,68,75,67,65,78,79,61,64,52,56,45,60,57,74,58,61,58,56,63,62,63,49,55,69,69,51", "endOffsets": "278,477,659,739,820,896,981,1062,1128,1190,1274,1357,1424,1487,1548,1614,1714,1816,1917,1986,2062,2130,2196,2275,2355,2417,2482,2535,2592,2638,2699,2757,2832,2891,2953,3012,3069,3133,3196,3260,3310,3366,3436,3506,3558"}, "to": {"startLines": "2,11,15,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,333,532,7119,7199,7280,7356,7441,7522,7588,7650,7734,7817,7884,7947,8008,8074,8174,8276,8377,8446,8522,8590,8656,8735,8815,8877,9555,9608,9665,9711,9772,9830,9905,9964,10026,10085,10142,10206,10269,10333,10383,10439,10509,10579", "endLines": "10,14,18,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "endColumns": "17,12,12,79,80,75,84,80,65,61,83,82,66,62,60,65,99,101,100,68,75,67,65,78,79,61,64,52,56,45,60,57,74,58,61,58,56,63,62,63,49,55,69,69,51", "endOffsets": "328,527,709,7194,7275,7351,7436,7517,7583,7645,7729,7812,7879,7942,8003,8069,8169,8271,8372,8441,8517,8585,8651,8730,8810,8872,8937,9603,9660,9706,9767,9825,9900,9959,10021,10080,10137,10201,10264,10328,10378,10434,10504,10574,10626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7460a359c4c281b49833222722dfee74\\transformed\\play-services-wallet-18.1.3\\res\\values-am\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "66", "endOffsets": "268"}, "to": {"startLines": "206", "startColumns": "4", "startOffsets": "15919", "endColumns": "70", "endOffsets": "15985"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b84b30b80c5949bd0f3610f30992b047\\transformed\\play-services-base-18.3.0\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,426,544,642,765,884,988,1086,1210,1309,1450,1569,1700,1823,1879,1932", "endColumns": "97,134,117,97,122,118,103,97,123,98,140,118,130,122,55,52,66", "endOffsets": "290,425,543,641,764,883,987,1085,1209,1308,1449,1568,1699,1822,1878,1931,1998"}, "to": {"startLines": "63,64,65,66,67,68,69,70,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4683,4785,4924,5046,5148,5275,5398,5506,5740,5868,5971,6116,6239,6374,6501,6561,6618", "endColumns": "101,138,121,101,126,122,107,101,127,102,144,122,134,126,59,56,70", "endOffsets": "4780,4919,5041,5143,5270,5393,5501,5603,5863,5966,6111,6234,6369,6496,6556,6613,6684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b14b43fc1759186e6ca5ed92b9460e08\\transformed\\foundation-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,85", "endOffsets": "137,223"}, "to": {"startLines": "204,205", "startColumns": "4,4", "startOffsets": "15746,15833", "endColumns": "86,85", "endOffsets": "15828,15914"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4d414adee14b24510c264c21c70961f0\\transformed\\appcompat-1.7.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "875,973,1071,1177,1263,1366,1483,1561,1637,1728,1821,1913,2007,2107,2200,2295,2388,2479,2570,2650,2750,2850,2946,3048,3148,3247,3397,15016", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "968,1066,1172,1258,1361,1478,1556,1632,1723,1816,1908,2002,2102,2195,2290,2383,2474,2565,2645,2745,2845,2941,3043,3143,3242,3392,3488,15091"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f0ad3059e848ec2624bad4e9e6fc7d3\\transformed\\browser-1.6.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,96", "endOffsets": "146,241,347,444"}, "to": {"startLines": "81,138,139,140", "startColumns": "4,4,4,4", "startOffsets": "6689,10716,10811,10917", "endColumns": "95,94,105,96", "endOffsets": "6780,10806,10912,11009"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e9aa3803274a6cf3eaa3a68ef58f1ff5\\transformed\\ui-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,188,265,357,453,535,613,696,778,856,934,1015,1085,1158,1231,1303,1383,1448", "endColumns": "82,76,91,95,81,77,82,81,77,77,80,69,72,72,71,79,64,115", "endOffsets": "183,260,352,448,530,608,691,773,851,929,1010,1080,1153,1226,1298,1378,1443,1559"}, "to": {"startLines": "61,62,82,83,84,142,143,190,191,193,194,196,197,198,199,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4523,4606,6785,6877,6973,11076,11154,14621,14703,14857,14935,15096,15166,15239,15312,15485,15565,15630", "endColumns": "82,76,91,95,81,77,82,81,77,77,80,69,72,72,71,79,64,115", "endOffsets": "4601,4678,6872,6968,7050,11149,11232,14698,14776,14930,15011,15161,15234,15307,15379,15560,15625,15741"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2d12819c5c8831c86b34929884541b49\\transformed\\play-services-basement-18.3.0\\res\\values-am\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "71", "startColumns": "4", "startOffsets": "5608", "endColumns": "131", "endOffsets": "5735"}}]}]}