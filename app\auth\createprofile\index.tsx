import ArrowIcon from "../../../assets/profile/arrow.svg";
import CloseIcon from "../../../assets/icons/CloseICon.svg";
import DriverIcon from "../../../assets/profile/driver.svg";
import LanguageChangeIcon from "../../../assets/icons/uil_language.svg";
import LockIcon from "../../../assets/profile/lockIcon.svg";
import QuestionIcon from "../../../assets/icons/Qusetion.svg";
import RBSheet from "react-native-raw-bottom-sheet";
import React, { forwardRef, useEffect, useRef } from "react";
import SettingIcon from "../../../assets/icons/Vector.svg";
import useCheckStatus from "@/hooks/checkStatusHook/useCheckStatus";
import { router } from "expo-router";
import { ActivityIndicator, Image, ScrollView, Text, View } from "react-native";
import { TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const index = () => {
  const HelpBottomSheetRef = useRef(null);

  const { data: statusData, isFetched, isLoading } = useCheckStatus();
  useEffect(() => {
    if (
      statusData?.workStatus.data.status === 1 &&
      statusData?.profileStatus.data.status === 1
    ) {
      router.dismissAll();
      router.replace("/home/<USER>/home");
    }
  }, [isLoading]);

  return (
    <SafeAreaView>
      <ScrollView>
        <View className="items-center mt-12 flex-row justify-between">
          <View
            className="items-center ml-4"
            style={{
              flex: 1,
            }}
          >
            <DriverIcon />
          </View>
        </View>
        {isLoading ? (
          <>
            <ActivityIndicator className="mt-10" size={"large"} />
          </>
        ) : (
          <>
            <View className="items-center justify-center mt-4">
              <Text className="font-[500] text-[12px] font-Pop leading-[18px]">
                Become a delivery partner in 3 easy steps
              </Text>
            </View>
            <View className="px-4 my-8 ">
              <View className="flex-row justify-between border-[1px] border-[#E9EAE9] rounded-[8px]">
                <View className="flex-row space-x-2">
                  <View className="">
                    <LockIcon />
                  </View>
                  <View className="space-y-2 max-w-[200px]">
                    <Text className="font-[400] text-[16px] font-Pop leading-[24px] text-[#627164]">
                      Step 1
                    </Text>
                    <Text className="font-[800] leading-[27px] text-[18px] font-Pop">
                      Profile
                    </Text>
                    <Text className="font-[400] text-[13px] leading-[19px] font-Pop">
                      Upload Aadhar, PAN & Bank Details
                    </Text>
                    {statusData?.profileStatus.data.status ? (
                      <>
                        <TouchableOpacity
                          disabled={true}
                          onPress={() => {
                            router.push("/auth/createprofile/addprofile");
                          }}
                          className="h-[40px] w-[137px] flex-row space-x-2 items-center justify-center bg-[#A4F4AC] rounded-[5px] "
                        >
                          <Text className="font-[400] text-[16px] leading-[20px] font-Pop">
                            Completed
                          </Text>
                          <ArrowIcon />
                        </TouchableOpacity>
                      </>
                    ) : (
                      <>
                        <TouchableOpacity
                          onPress={() => {
                            router.push("/auth/createprofile/addprofile");
                          }}
                          className="h-[40px] w-[137px] flex-row space-x-2 items-center justify-center bg-[#A4F4AC] rounded-[5px] "
                        >
                          <Text className="font-[400] text-[16px] leading-[20px] font-Pop">
                            Start Now
                          </Text>
                          <ArrowIcon />
                        </TouchableOpacity>
                      </>
                    )}
                  </View>
                </View>
                <View className="">
                  <Image
                    source={require("../../../assets/profile/contactLogo.png")}
                    style={{ width: 50, height: 50 }}
                    className="w-[50px] h-[50px]"
                    resizeMode="contain"
                    resizeMethod="scale"
                  />
                </View>
              </View>

              <View className="mt-6 flex-row justify-between border-[1px] border-[#E9EAE9] rounded-[8px]">
                <View className="flex-row space-x-2">
                  <View className="">
                    <LockIcon />
                  </View>
                  <View className="space-y-2 max-w-[200px]">
                    <Text className="font-[400] text-[16px] font-Pop leading-[24px] text-[#627164]">
                      Step 2
                    </Text>
                    <Text className="font-[800] leading-[27px] text-[18px] font-Pop">
                      Work Settings
                    </Text>
                    <Text className="font-[400] text-[13px] leading-[19px] font-Pop">
                      Select vehicle, Upload DL and RC
                    </Text>
                    {statusData?.profileStatus.data.status ? (
                      <>
                        {statusData?.workStatus.data.status === 1 ? (
                          <>
                            <TouchableOpacity
                              disabled={true}
                              onPress={() => {
                                router.push("/auth/createprofile/worksetting");
                              }}
                              className="h-[40px] w-[137px] flex-row space-x-2 items-center justify-center bg-[#A4F4AC] rounded-[5px] "
                            >
                              <Text className="font-[400] text-[16px] leading-[20px] font-Pop">
                                Completed
                              </Text>
                              <ArrowIcon />
                            </TouchableOpacity>
                          </>
                        ) : (
                          <>
                            <TouchableOpacity
                              onPress={() => {
                                router.push("/auth/createprofile/worksetting");
                              }}
                              className="h-[40px] w-[137px] flex-row space-x-2 items-center justify-center bg-[#A4F4AC] rounded-[5px] "
                            >
                              <Text className="font-[400] text-[16px] leading-[20px] font-Pop">
                                Start Now
                              </Text>
                              <ArrowIcon />
                            </TouchableOpacity>
                          </>
                        )}
                      </>
                    ) : (
                      <>
                        <TouchableOpacity
                          disabled={true}
                          onPress={() => {
                            router.push("/auth/createprofile/worksetting");
                          }}
                          className="h-[40px] opacity-50 w-[137px] flex-row space-x-2 items-center justify-center bg-[#A4F4AC] rounded-[5px] "
                        >
                          <Text className="font-[400] text-[16px] leading-[20px] font-Pop">
                            Start Now
                          </Text>
                          <ArrowIcon />
                        </TouchableOpacity>
                      </>
                    )}
                  </View>
                </View>
                <View className="">
                  <Image
                    source={require("../../../assets/profile/calender-iso-color.png")}
                    style={{ width: 100, height: 100 }}
                    resizeMode="contain"
                    resizeMethod="scale"
                  />
                </View>
              </View>
            </View>
          </>
        )}

        <HelpPopUp ref={HelpBottomSheetRef} />
      </ScrollView>
    </SafeAreaView>
  );
};

export const HelpPopUp = forwardRef((props, ref) => {
  return (
    <>
      <RBSheet
        ref={ref as any}
        customStyles={{
          container: {
            borderRadius: 20,
            height: 300,
          },
          draggableIcon: {
            backgroundColor: "#000",
          },
        }}
        customModalProps={{
          statusBarTranslucent: true,
        }}
        customAvoidingViewProps={{
          enabled: false,
        }}
      >
        <View className="px-5 mt-10 space-y-6">
          <View className="flex-row justify-between">
            <Text className="">Select an option</Text>
            <TouchableOpacity
              onPress={() => {
                (ref as any).current.close();
              }}
            >
              <CloseIcon />
            </TouchableOpacity>
          </View>
          <View className="space-y-6">
            <TouchableOpacity className="flex-row space-x-4 py-1">
              <QuestionIcon />
              <Text className="">Frequently Asked Questions</Text>
            </TouchableOpacity>
            <TouchableOpacity className="flex-row space-x-4 py-1">
              <LanguageChangeIcon />
              <Text className="">Change Language</Text>
            </TouchableOpacity>
            <TouchableOpacity className="flex-row space-x-4 py-1">
              <SettingIcon />
              <Text className="">Settings</Text>
            </TouchableOpacity>
          </View>
        </View>
      </RBSheet>
    </>
  );
});

export default index;
