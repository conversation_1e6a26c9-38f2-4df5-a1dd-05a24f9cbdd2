com.mohandhass2.myapp-exoplayer-core-2.18.1-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00636ad238812e00d92433007e026a91\transformed\exoplayer-core-2.18.1\res
com.mohandhass2.myapp-firebase-common-21.0.0-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00718f8cd295d237ea8fb67f7cbcc86a\transformed\firebase-common-21.0.0\res
com.mohandhass2.myapp-ui-unit-release-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\010919da35f16f21a3cdff49c62ea7fe\transformed\ui-unit-release\res
com.mohandhass2.myapp-savedstate-1.2.1-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01f5868e1a3bbc746a0df16a210183c2\transformed\savedstate-1.2.1\res
com.mohandhass2.myapp-startup-runtime-1.1.1-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\030dd61fccce7abad1860c46c9bc2833\transformed\startup-runtime-1.1.1\res
com.mohandhass2.myapp-annotation-experimental-1.4.1-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0526bec2661570e47335e8b7673651c8\transformed\annotation-experimental-1.4.1\res
com.mohandhass2.myapp-firebase-messaging-24.0.1-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05887ea86031039b7ecbf23129389746\transformed\firebase-messaging-24.0.1\res
com.mohandhass2.myapp-lifecycle-livedata-core-2.8.3-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05dc0a5189e38a49515800420503f4ec\transformed\lifecycle-livedata-core-2.8.3\res
com.mohandhass2.myapp-ui-util-release-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\067bf269eb6447a77b3f507899f3a04d\transformed\ui-util-release\res
com.mohandhass2.myapp-drawee-3.2.0-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\145a58c47b72462367ffa7d2f3c84c3b\transformed\drawee-3.2.0\res
com.mohandhass2.myapp-constraintlayout-2.0.1-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1619f5c30df2cf616e20d2bf30ab0293\transformed\constraintlayout-2.0.1\res
com.mohandhass2.myapp-animation-core-release-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\169d07584a7d8da08ddca9bda578ccc6\transformed\animation-core-release\res
com.mohandhass2.myapp-lifecycle-livedata-core-ktx-2.8.3-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1764594bc6f5ec7fd6e5d2160556e05d\transformed\lifecycle-livedata-core-ktx-2.8.3\res
com.mohandhass2.myapp-tracing-ktx-1.2.0-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e70593a0893cff27c8130045c5a5c4d\transformed\tracing-ktx-1.2.0\res
com.mohandhass2.myapp-browser-1.6.0-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f0ad3059e848ec2624bad4e9e6fc7d3\transformed\browser-1.6.0\res
com.mohandhass2.myapp-core-ktx-1.13.1-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\221f7c180d1c301358f6357420c82a90\transformed\core-ktx-1.13.1\res
com.mohandhass2.myapp-standard-core-1.6.49-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\222c8bd50fbb99823117f37113651644\transformed\standard-core-1.6.49\res
com.mohandhass2.myapp-camera-lifecycle-1.4.1-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\252c730e0b6bf494a3c5d63841ce9a6b\transformed\camera-lifecycle-1.4.1\res
com.mohandhass2.myapp-tracing-1.2.0-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\279f54180a3004b464686cca012f271e\transformed\tracing-1.2.0\res
com.mohandhass2.myapp-animation-release-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27f54ac0f057eed65ce9b2bbafc3b1a6\transformed\animation-release\res
com.mohandhass2.myapp-runtime-release-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2827c297f1a88ba114a41b748c37e060\transformed\runtime-release\res
com.mohandhass2.myapp-lifecycle-livedata-2.8.3-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a7d51e9cff25a2a05dab7969422bcd\transformed\lifecycle-livedata-2.8.3\res
com.mohandhass2.myapp-glide-4.16.0-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d10be2ca6f02e3c7fd72b76301c06b9\transformed\glide-4.16.0\res
com.mohandhass2.myapp-play-services-basement-18.3.0-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d12819c5c8831c86b34929884541b49\transformed\play-services-basement-18.3.0\res
com.mohandhass2.myapp-coordinatorlayout-1.2.0-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b9987bc127d003de604ba580089c06\transformed\coordinatorlayout-1.2.0\res
com.mohandhass2.myapp-play-services-auth-21.1.0-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36c2911903c2f91ce96f9859f62e94a1\transformed\play-services-auth-21.1.0\res
com.mohandhass2.myapp-appcompat-resources-1.7.0-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d816cf54fe94cb3ebe3970169e0d2ed\transformed\appcompat-resources-1.7.0\res
com.mohandhass2.myapp-lifecycle-viewmodel-savedstate-2.8.3-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f35a2568b33cdf8a7708826aa85168c\transformed\lifecycle-viewmodel-savedstate-2.8.3\res
com.mohandhass2.myapp-profileinstaller-1.3.1-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41136865cc12ba8416250ab4fcd14dc3\transformed\profileinstaller-1.3.1\res
com.mohandhass2.myapp-media-1.4.3-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\448e2a47c2b2ada0bf8cd80bd6ec7e39\transformed\media-1.4.3\res
com.mohandhass2.myapp-lifecycle-service-2.8.3-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46c8c78d188f6b3d348830d1ed0ead43\transformed\lifecycle-service-2.8.3\res
com.mohandhass2.myapp-appcompat-1.7.0-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d414adee14b24510c264c21c70961f0\transformed\appcompat-1.7.0\res
com.mohandhass2.myapp-lifecycle-runtime-compose-release-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\515ee7d0555dc42a384af55a3cffa1d5\transformed\lifecycle-runtime-compose-release\res
com.mohandhass2.myapp-savedstate-ktx-1.2.1-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ac842835d82618b00972ca2e63d52fd\transformed\savedstate-ktx-1.2.1\res
com.mohandhass2.myapp-recyclerview-1.2.1-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b3921f3c0ec9fec3b31ebed685fc140\transformed\recyclerview-1.2.1\res
com.mohandhass2.myapp-ui-graphics-release-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6063bb0af1d30b1b1d98ea038715db0d\transformed\ui-graphics-release\res
com.mohandhass2.myapp-swiperefreshlayout-1.1.0-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6770489b2150f304c763c4521df37a82\transformed\swiperefreshlayout-1.1.0\res
com.mohandhass2.myapp-play-services-wallet-18.1.3-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7460a359c4c281b49833222722dfee74\transformed\play-services-wallet-18.1.3\res
com.mohandhass2.myapp-customview-poolingcontainer-1.0.0-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7684a13bf28674155e26ec50b0113afc\transformed\customview-poolingcontainer-1.0.0\res
com.mohandhass2.myapp-drawerlayout-1.1.1-39 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d870e92d279060797355c3afa632fe0\transformed\drawerlayout-1.1.1\res
com.mohandhass2.myapp-camera-video-1.4.1-40 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f0d1fb312ac592d79624503f22091b2\transformed\camera-video-1.4.1\res
com.mohandhass2.myapp-foundation-layout-release-41 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f2129670b9baaeadad5956e4a14d103\transformed\foundation-layout-release\res
com.mohandhass2.myapp-android-maps-utils-3.8.2-42 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e9bc6c234e70326f0babefb2238fc4f\transformed\android-maps-utils-3.8.2\res
com.mohandhass2.myapp-autofill-1.1.0-43 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ee05b4940e071ad5d1d7c7e68ff5e05\transformed\autofill-1.1.0\res
com.mohandhass2.myapp-lifecycle-viewmodel-release-44 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9208c5cce24d430e4aba003c6876b859\transformed\lifecycle-viewmodel-release\res
com.mohandhass2.myapp-Android-Image-Cropper-4.3.1-45 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c569f151f8eecc9b25adff1be1284e4\transformed\Android-Image-Cropper-4.3.1\res
com.mohandhass2.myapp-core-splashscreen-1.2.0-alpha02-46 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fc222b694c9ca04a566560b361d90b0\transformed\core-splashscreen-1.2.0-alpha02\res
com.mohandhass2.myapp-material-1.6.1-47 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a39c8a492fe916611935c0d3835604e6\transformed\material-1.6.1\res
com.mohandhass2.myapp-core-1.13.1-48 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6591935515ee6cd5ec7881ab1d3c64e\transformed\core-1.13.1\res
com.mohandhass2.myapp-ui-text-release-49 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7de52dba0a6c59ac13dcc0a9bc126fe\transformed\ui-text-release\res
com.mohandhass2.myapp-transition-1.2.0-50 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac31c4583d570f588270ebf42a86bc82\transformed\transition-1.2.0\res
com.mohandhass2.myapp-camera-camera2-1.4.1-51 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae4669ff24f5b3ce62872727aeef56ca\transformed\camera-camera2-1.4.1\res
com.mohandhass2.myapp-cardview-1.0.0-52 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae58c5a64fce4f723365c9edcca52b81\transformed\cardview-1.0.0\res
com.mohandhass2.myapp-emoji2-views-helper-1.3.0-53 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b00c31ecb50cb3fc2567ded277a17d86\transformed\emoji2-views-helper-1.3.0\res
com.mohandhass2.myapp-lifecycle-viewmodel-ktx-2.8.3-54 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0caf49a87eaf61534dda6b7b151c0b4\transformed\lifecycle-viewmodel-ktx-2.8.3\res
com.mohandhass2.myapp-react-android-0.76.9-release-55 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0cc9e4ae2bfcac04a12a08baf379016\transformed\react-android-0.76.9-release\res
com.mohandhass2.myapp-foundation-release-56 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b14b43fc1759186e6ca5ed92b9460e08\transformed\foundation-release\res
com.mohandhass2.myapp-fragment-1.6.1-57 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b825b073e97da6f5b520ec2775c042b7\transformed\fragment-1.6.1\res
com.mohandhass2.myapp-graphics-path-1.0.1-58 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8455887186881c9b8485743c77022f8\transformed\graphics-path-1.0.1\res
com.mohandhass2.myapp-play-services-base-18.3.0-59 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b84b30b80c5949bd0f3610f30992b047\transformed\play-services-base-18.3.0\res
com.mohandhass2.myapp-viewpager2-1.0.0-60 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc7e9b6dca1567bc491d9562fda28106\transformed\viewpager2-1.0.0\res
com.mohandhass2.myapp-lifecycle-runtime-release-61 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c057469839ed01cf70753c0e493d65de\transformed\lifecycle-runtime-release\res
com.mohandhass2.myapp-camera-view-1.4.1-62 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c70b754ff3b176c20bb896d71baebb6b\transformed\camera-view-1.4.1\res
com.mohandhass2.myapp-camera-core-1.4.1-63 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd0c7a87842c17bbcbd53561366b7d99\transformed\camera-core-1.4.1\res
com.mohandhass2.myapp-emoji2-1.3.0-64 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cff5a9579380eb2099b3f5ffada093c5\transformed\emoji2-1.3.0\res
com.mohandhass2.myapp-runtime-saveable-release-65 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d1dc6071eda49a080bf4422b12311f7f\transformed\runtime-saveable-release\res
com.mohandhass2.myapp-lifecycle-runtime-ktx-release-66 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfb4e9514ab2344f5ebdea6099d9e43a\transformed\lifecycle-runtime-ktx-release\res
com.mohandhass2.myapp-core-runtime-2.2.0-67 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e489da49fc17afc8c1db9f440c532b97\transformed\core-runtime-2.2.0\res
com.mohandhass2.myapp-activity-1.7.2-68 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e50a7041aff24bf45b5302e9c4f92086\transformed\activity-1.7.2\res
com.mohandhass2.myapp-ui-release-69 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9aa3803274a6cf3eaa3a68ef58f1ff5\transformed\ui-release\res
com.mohandhass2.myapp-work-runtime-2.7.1-70 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea79fc2a98096845c57bc3721521abee\transformed\work-runtime-2.7.1\res
com.mohandhass2.myapp-camera-mlkit-vision-1.4.1-71 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebd6ae7208f5f47c20a80ddaf756eeb9\transformed\camera-mlkit-vision-1.4.1\res
com.mohandhass2.myapp-fragment-ktx-1.6.1-72 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed2489108ffffc121e0f0f135fd22cc1\transformed\fragment-ktx-1.6.1\res
com.mohandhass2.myapp-exoplayer-ui-2.18.1-73 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eeb7a35c1d2dda21edaace2253c1f099\transformed\exoplayer-ui-2.18.1\res
com.mohandhass2.myapp-ui-geometry-release-74 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0cfe080caec0f58ab5da943448e1219\transformed\ui-geometry-release\res
com.mohandhass2.myapp-camera-extensions-1.4.1-75 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2182c1e5a52c38fa13c74f3b2841e77\transformed\camera-extensions-1.4.1\res
com.mohandhass2.myapp-activity-ktx-1.7.2-76 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5a446f5a198e3f5ba28288dc9be9bfa\transformed\activity-ktx-1.7.2\res
com.mohandhass2.myapp-lifecycle-process-2.8.3-77 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83f5c4cd7c3a21131883912131f63dc\transformed\lifecycle-process-2.8.3\res
com.mohandhass2.myapp-play-services-maps-18.2.0-78 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa08fd0869163705d58d4f365af34e4b\transformed\play-services-maps-18.2.0\res
com.mohandhass2.myapp-res-79 D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\generated\res\createBundleReleaseJsAndAssets
com.mohandhass2.myapp-pngs-80 D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\generated\res\pngs\release
com.mohandhass2.myapp-res-81 D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\generated\res\processReleaseGoogleServices
com.mohandhass2.myapp-resValues-82 D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\generated\res\resValues\release
com.mohandhass2.myapp-packageReleaseResources-83 D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\intermediates\incremental\release\packageReleaseResources\merged.dir
com.mohandhass2.myapp-packageReleaseResources-84 D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\intermediates\incremental\release\packageReleaseResources\stripped.dir
com.mohandhass2.myapp-release-85 D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\intermediates\merged_res\release\mergeReleaseResources
com.mohandhass2.myapp-main-86 D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\res
com.mohandhass2.myapp-release-87 D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\release\res
com.mohandhass2.myapp-release-88 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-89 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\@react-native-community\datetimepicker\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-90 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\@react-native-community\slider\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-91 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\@shopify\react-native-skia\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-92 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-application\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-93 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-asset\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-94 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-av\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-95 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-calendar\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-96 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-camera\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-97 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-constants\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-98 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-device\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-99 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-document-picker\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-100 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-101 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-font\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-102 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-loader\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-103 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-104 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-keep-awake\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-105 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-linear-gradient\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-106 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-linking\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-107 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-location\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-108 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-media-library\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-109 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-modules-core\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-110 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-111 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-splash-screen\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-112 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-system-ui\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-113 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-web-browser\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-114 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-115 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-116 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-maps\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-117 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-quick-base64\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-118 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-razorpay\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-119 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-120 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-screens\android\build\intermediates\packaged_res\release\packageReleaseResources
com.mohandhass2.myapp-release-121 D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-svg\android\build\intermediates\packaged_res\release\packageReleaseResources
