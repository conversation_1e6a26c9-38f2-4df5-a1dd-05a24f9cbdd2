import { View, Text, TouchableOpacity, ScrollView } from "react-native";
import React, { useState } from "react";
import { Slot, router } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import StarIcon from "../../../../../assets/Hometab/GreenStar.svg";
import useGetApiData from "../../../../../hooks/useGetApiData";
import BackArrow from "@/assets/icons/BackArrow.svg";

const _layout = () => {
  const [active, setactive] = useState("Weekly Ratings");
  const { data, isLoading, triggerreFresh } = useGetApiData({
    endpoint: "auth/rating",
  });
  return (
    <SafeAreaView className="flex-1">
      <Slot />
    </SafeAreaView>
  );
};

export const WeeklyRatingComponent = (data) => {
  return (
    <View className="m-2 p-4 border-[1px] rounded-[4px] border-[#E9EAE9] flex-row items-center justify-between">
      <View className="">
        <View className="mb-4">
          <Text className="">({data?.data?.weekly})</Text>
        </View>
        <View className="">
          <View className="flex-row space-x-2">
            <StarIcon
              fill={
                Math.floor(Number(data?.data?.avg_rating)) > 0
                  ? "#00660A"
                  : "#fff"
              }
            />
            <StarIcon
              fill={
                Math.floor(Number(data?.data?.avg_rating)) > 1
                  ? "#00660A"
                  : "#fff"
              }
            />
            <StarIcon
              fill={
                Math.floor(Number(data?.data?.avg_rating)) > 2
                  ? "#00660A"
                  : "#fff"
              }
            />
            <StarIcon
              fill={
                Math.floor(Number(data?.data?.avg_rating)) > 3
                  ? "#00660A"
                  : "#fff"
              }
            />
            <StarIcon
              fill={
                Math.floor(Number(data?.data?.avg_rating)) > 4
                  ? "#00660A"
                  : "#fff"
              }
            />
          </View>
        </View>
      </View>
      <View
        className=""
        style={{
          alignSelf: "flex-end",
        }}
      >
        <Text className="font-[600] font-Pop text-[24px] leading-[36px]">
          {data?.data?.avg_rating}
        </Text>
      </View>
    </View>
  );
};
export const MonthlyRatingComponent = (data) => {
  return (
    <View className="m-2 p-4 border-[1px] rounded-[4px] border-[#E9EAE9] flex-row items-center justify-between">
      <View className="">
        <View className="mb-4">
          <Text className="">({data?.data?.monthly})</Text>
        </View>
        <View className="">
          <View className="flex-row space-x-2">
            <StarIcon
              fill={
                Math.floor(Number(data?.data?.avg_rating)) > 0
                  ? "#00660A"
                  : "#fff"
              }
            />
            <StarIcon
              fill={
                Math.floor(Number(data?.data?.avg_rating)) > 1
                  ? "#00660A"
                  : "#fff"
              }
            />
            <StarIcon
              fill={
                Math.floor(Number(data?.data?.avg_rating)) > 2
                  ? "#00660A"
                  : "#fff"
              }
            />
            <StarIcon
              fill={
                Math.floor(Number(data?.data?.avg_rating)) > 3
                  ? "#00660A"
                  : "#fff"
              }
            />
            <StarIcon
              fill={
                Math.floor(Number(data?.data?.avg_rating)) > 4
                  ? "#00660A"
                  : "#fff"
              }
            />
          </View>
        </View>
      </View>
      <View
        className=""
        style={{
          alignSelf: "flex-end",
        }}
      >
        <Text className="font-[600] font-Pop text-[24px] leading-[36px]">
          {data?.data?.avg_rating}
        </Text>
      </View>
    </View>
  );
};

export default _layout;
