# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# input_SRC at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/RNDateTimePickerCGen-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/ComponentDescriptors.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/EventEmitters.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/Props.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/ShadowNodes.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt:21 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt:21 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp/react/renderer/components/RNCSlider/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp/react/renderer/components/RNCSlider/RNCSliderMeasurementsManager.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp/react/renderer/components/RNCSlider/RNCSliderShadowNode.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/RNCSlider-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider/ComponentDescriptors.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider/EventEmitters.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider/Props.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider/RNCSliderJSI-generated.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider/ShadowNodes.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/rnskia-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia/ComponentDescriptors.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia/EventEmitters.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia/Props.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia/ShadowNodes.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia/States.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia/rnskiaJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:21 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:21 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/safeareacontext-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/utils/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# rnsvg_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt:19 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/rnsvg.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# rnsvg_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt:19 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnsvg/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# rnsvg_codegen_SRCS at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt:20 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg/*cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp"
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()

# override_cpp_SRC at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:42 (file)
# input_SRC at D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/*.cpp")
set(OLD_GLOB
  "D:/App/Delivery-Partner/Delivery-Partner-App/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/App/Delivery-Partner/Delivery-Partner-App/android/app/.cxx/RelWithDebInfo/1a5c8433/x86/CMakeFiles/cmake.verify_globs")
endif()
