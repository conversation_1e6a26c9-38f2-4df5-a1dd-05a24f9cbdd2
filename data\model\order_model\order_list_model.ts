import { DeliveryAddressEntity } from "@/domain/entity/order_entity/delivery_address_entity";
import { OrderListEntity } from "@/domain/entity/order_entity/order_list_entity";
import { PickupAddressEntity } from "@/domain/entity/order_entity/pickup_address_entity";

export class OrderListModel implements OrderListEntity {
  id: number;
  order_number: string;
  order_status: string;
  invoice_no: string;
  customer_id: number;
  product_mode: number;
  delivery_address_id: number;
  pickup_address: PickupAddressEntity[];
  delivery_address: DeliveryAddressEntity;
  edt: number;
  deliver_partner_id: string;
  delivery_fees: string;
  order_otp: number;
  platform_fees: string;
  cash_handling_charges: string;
  order_amnt: string;
  coupon_code: string;
  coupon_discounted_amnt: string;
  gst_total: string;
  status_id: number;
  order_status_id: number;
  created_at: string;
  constructor({
    id,
    order_number,
    order_status,
    invoice_no,
    customer_id,
    product_mode,
    delivery_address_id,
    pickup_address,
    delivery_address,
    edt,
    deliver_partner_id,
    delivery_fees,
    order_otp,
    platform_fees,
    cash_handling_charges,
    order_amnt,
    coupon_code,
    coupon_discounted_amnt,
    gst_total,
    status_id,
    order_status_id,
    created_at,
  }: {
    id: number;
    order_number: string;
    order_status: string;
    invoice_no: string;
    customer_id: number;
    product_mode: number;
    delivery_address_id: number;
    pickup_address: PickupAddressEntity[];
    delivery_address: DeliveryAddressEntity;
    edt: number;
    deliver_partner_id: string;
    delivery_fees: string;
    order_otp: number;
    platform_fees: string;
    cash_handling_charges: string;
    order_amnt: string;
    coupon_code: string;
    coupon_discounted_amnt: string;
    gst_total: string;
    status_id: number;
    order_status_id: number;
    created_at: string;
  }) {
    this.id = id;
    this.order_number = order_number;
    this.order_status = order_status;
    this.invoice_no = invoice_no;
    this.customer_id = customer_id;
    this.product_mode = product_mode;
    this.delivery_address_id = delivery_address_id;
    this.pickup_address = pickup_address;
    this.delivery_address = delivery_address;
    this.edt = edt;
    this.deliver_partner_id = deliver_partner_id;
    this.delivery_fees = delivery_fees;
    this.order_otp = order_otp;
    this.platform_fees = platform_fees;
    this.cash_handling_charges = cash_handling_charges;
    this.order_amnt = order_amnt;
    this.coupon_code = coupon_code;
    this.coupon_discounted_amnt = coupon_discounted_amnt;
    this.gst_total = gst_total;
    this.status_id = status_id;
    this.order_status_id = order_status_id;
    this.created_at = created_at;
  }
}
