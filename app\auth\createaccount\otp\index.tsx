import AsyncStorage from "@react-native-async-storage/async-storage";
import ButtonComponent from "../../../../component/ButtonComponent";
import LinearGradientComponent from "../../../../component/LinearGradientComponent";
import OtpTextInput from "react-native-text-input-otp";
import PrivacyPolicyComponent from "../../../../component/PrivacyPolicyComponent";
import React, { useEffect, useState } from "react";
import useLoginApi from "../../../../hooks/useLoginApi";
import { router, useLocalSearchParams } from "expo-router";
import { Alert } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useLogin } from "../../../../store";

import {
  View,
  Text,
  TouchableOpacity,
  Keyboard,
  TouchableWithoutFeedback,
} from "react-native";

const otp = () => {
  const data = useLocalSearchParams();
  const [otp, setOtp] = useState("");
  const [Isotpfilled, setotpfilled] = useState(false);

  const [resendOtp, setresendOtp] = useState(false);
  const [count, setcount] = useState(0);

  const { userToken, isLogin, setuserToken, removeuserToken } = useLogin(
    (state) => state
  );

  const check = () => {
    if (otp.length === 4) {
      setotpfilled(true);
    } else {
      setotpfilled(false);
    }
  };

  const { SetFunction: SendOtp } = useLoginApi({
    endpoint: "sendOtp",
  });

  const { SetFunction: verifyOtp } = useLoginApi({
    endpoint: "verifyOtp",
  });

  const handleSubmit = () => {
    verifyOtp({
      phone: Number(data?.number),
      otp: Number(otp),
    }).then(async (response) => {
      await AsyncStorage.setItem("Number", data?.number as string);
      console.log(await AsyncStorage.getItem("Number"));
      setuserToken(response.data.payload.token);
      router.push("/auth/createprofile/");
    });
    // router.push("home");
  };

  const inerval = () => {
    SendOtp({
      phone: Number(data.phone),
    });
    setInterval(() => {
      setcount((count) => {
        if (count > 0) {
          return count - 1;
        } else {
          clearInterval();
          setresendOtp(false);
        }
      });
    }, 1000);
  };

  useEffect(() => {
    check();
  }, [otp]);

  useEffect(() => {
    Alert.alert("Testing OTP", data?.otp);
  }, []);

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <SafeAreaView
        style={{
          flex: 1,
          position: "relative",
        }}
      >
        {/* LinearGradient Component Area*/}
        <LinearGradientComponent />
        <View className="z-10 px-6 justify-center">
          {/* Login Title Area */}
          <View className="items-center justify-end h-[24%] mb-8 gap-3">
            <View>
              <Text className="font-[400] text-[26px] text-white">
                Enter OTP
              </Text>
            </View>
            <View className="flex-row items-center justify-center">
              <Text className="font-[400] text-[18px] text-white">
                We’ve sent a text on {data?.number}
              </Text>
              <TouchableOpacity
                onPress={() => {
                  router.back();
                }}
              >
                <Text className="font-[400] text-[18px] text-[#FFD401]">
                  edit
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          {/* Otp Field Area */}
          <View className="items-center justify-center">
            <View
              style={{
                maxWidth: "90%",
              }}
            >
              <OtpTextInput
                otp={otp}
                setOtp={setOtp}
                digits={4}
                style={{
                  borderRadius: 0,
                  borderTopWidth: 0,
                  borderRightWidth: 0,
                  borderLeftWidth: 0,
                  height: 45,
                }}
                fontStyle={{ fontSize: 26, fontWeight: "bold", color: "#fff" }}
                focusedStyle={{ borderColor: "#5cb85c", borderBottomWidth: 2 }}
              />
            </View>
          </View>
          {/* Otp resend text Area */}
          <View className="flex-row items-center justify-center mt-14 space-x-1">
            <Text className="font-[400] text-[18px] text-[#fff]">
              {resendOtp ? <>Resend the OTP in </> : <>Didn’t get it? </>}
            </Text>
            {resendOtp ? (
              <>
                <Text className="font-[400] text-[18px] text-[#FFD401]">
                  {count}
                </Text>
              </>
            ) : (
              <TouchableOpacity
                onPress={() => {
                  inerval();
                  setcount(60);
                  setresendOtp(true);
                }}
              >
                <Text className="font-[400] text-[18px] text-[#FFD401]">
                  Resend
                </Text>
              </TouchableOpacity>
            )}
          </View>
          {/* Terms of services and Privacy Policy Area */}
          <View className="mt-36">
            <View clsassName="items-center justify-center">
              <PrivacyPolicyComponent />
            </View>
          </View>
          {/* Button Area */}
          <View className="mt-4">
            <ButtonComponent
              text="Verify OTP"
              value={Isotpfilled}
              pressfun={() => {
                if (Isotpfilled) {
                  handleSubmit();
                }
              }}
            />
          </View>
        </View>
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
};

export default otp;
