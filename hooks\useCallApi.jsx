import AsyncStorage from "@react-native-async-storage/async-storage";
import Constants from "expo-constants";
import axios from "axios";
import { useState } from "react";

const useCallApi = () => {
  const [isLoading, setLoading] = useState(false);
  const CallFunction = async (endpoint) => {
    let msg;
    setLoading(true);
    const token = await AsyncStorage.getItem("login");
    await axios
      .post(
        `${Constants.expoConfig?.extra?.BASE_URL}${endpoint}`,
        {},
        {
          headers: {
            "X-Powered-By": "Express",
            "Access-Control-Allow-Origin": "*",
            "Content-Length": 0,
            Authorization: token,
          },
        }
      )
      .then((val) => {
        return val.data;
      })
      .then((datas) => {
        msg = datas;
        return datas;
      })
      .then((data) => {
        setLoading(false);
        return data;
      })
      .catch((error) => {
        console.error(error.response ? error.response.data : error.message);
      });
    return msg;
  };

  return { isLoading, CallFunction };
};

export default useCallApi;
