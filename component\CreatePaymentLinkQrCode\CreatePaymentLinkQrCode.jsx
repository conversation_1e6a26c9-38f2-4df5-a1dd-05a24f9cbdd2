import axios from "axios";
import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  BackHandler,
  ActivityIndicator,
} from "react-native";
import QrImage from "@/assets/Hometab/qrImage.png";
import { encode } from "base-64";

const CreatePaymentLink = ({ amount }) => {
  const [QrImage, setQrImage] = useState("");
  const getFutureUnixTimestamp = (hoursFromNow) => {
    const currentUnixTime = Math.floor(Date.now() / 1000); // Current Unix timestamp in seconds
    const futureUnixTime = currentUnixTime + hoursFromNow * 60 * 60; // Add hours (converted to seconds)
    return futureUnixTime;
  };

  useEffect(() => {
    const futureTimestamp = getFutureUnixTimestamp(1);
    const apiKey = "rzp_test_yaXUQ9vZbfNIxc";
    const apiSecret = "ICSydCOrijxm3re9CwJEB86c";
    const authHeader = encode(`${apiKey}:${apiSecret}`);

    const createPaymentLink = async () => {
      await axios
        .post(
          "https://api.razorpay.com/v1/payments/qr_codes",
          {
            type: "upi_qr",
            name: "Store_1",
            usage: "single_use",
            fixed_amount: true,
            payment_amount: amount * 100,
            description: "For Store 1",
            // customer_id: "cust_PSJXj5ebfuBPCC",
            close_by: futureTimestamp,
            notes: {
              purpose: "Test UPI QR code notes",
            },
          },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Basic ${authHeader}`,
            },
          }
        )
        .then((val) => {
          setQrImage(val?.data?.image_url);
        });
    };

    createPaymentLink();
  }, []);

  return (
    <View className="items-center justify-center mt-10">
      {QrImage != "" ? (
        <>
          <Image
            onLayout={() => {
              return <ActivityIndicator size={"large"} color={"blue"} />;
            }}
            source={{ uri: QrImage }}
            width={400}
            height={400}
          />
        </>
      ) : (
        <>
          <ActivityIndicator size={"large"} color={"blue"} />
        </>
      )}
    </View>
  );
};
export default CreatePaymentLink;
