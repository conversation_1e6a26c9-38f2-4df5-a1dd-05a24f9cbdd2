import AsyncStorage from "@react-native-async-storage/async-storage";
import Constants from "expo-constants";
import axios from "axios";
import { useEffect, useState } from "react";

const useGetApiDataFun = (endpoint) => {
  const [data, setdata] = useState(null);
  const [isLoading, setLoading] = useState(false);
  const DataFetcher = async (param) => {
    setLoading(true);
    const token = await AsyncStorage.getItem("login");
    let Testdata;
    await axios
      .post(`${Constants.expoConfig?.extra?.BASE_URL}${endpoint}`, param, {
        headers: {
          "X-Powered-By": "Express",
          "Access-Control-Allow-Origin": "*",
          "Content-Type": "application/json",
          Authorization: token,
        },
      })
      .then((val) => {
        return val.data;
      })
      .then((datas) => {
        Testdata = datas;
        setdata(datas);
        return datas;
      })
      .catch((error) => {
        console.error(error.response ? error.response.data : error.message);
      });
    setLoading(false);
    return Testdata;
  };

  return { isLoading, DataFetcher };
};

export default useGetApiDataFun;
