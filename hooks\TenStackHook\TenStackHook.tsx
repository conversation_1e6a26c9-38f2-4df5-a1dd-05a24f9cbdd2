import { useQuery } from "@tanstack/react-query";
import { axiosInstance } from "@/api/axios";

const useTenStackHook = <T extends object, r extends object>(props: {
  key: string;
  canSave: boolean;
  id?: string;
  endpoint: string;
  params?: T;
}) => {
  const { key, canSave, id, endpoint, params } = props;
  const APIFunction = async (): Promise<r> => {
    const header = {
      "X-Powered-By": "Express",
      "Access-Control-Allow-Origin": "*",
      "Content-Type": "application/json",
    };
    console.log(params);
    console.log(endpoint);
    console.log(header);

    return await axiosInstance
      .post(`${endpoint}`, params, {
        headers: header,
      })
      .then((val) => {
        return val.data;
      });
  };
  const Values = useQuery<r>({
    queryKey: [key, id],
    queryFn: () => {
      return APIFunction();
    },
    enabled: canSave,
  });
  return Values;
};

export default useTenStackHook;
