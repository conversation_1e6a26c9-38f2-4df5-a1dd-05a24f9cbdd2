# C/C++ build system timings
generate_cxx_metadata
  [gap of 368ms]
  create-invalidation-state 66ms
  generate-prefab-packages
    exec-prefab 1017ms
    [gap of 89ms]
  generate-prefab-packages completed in 1109ms
  execute-generate-process
    exec-configure 1502ms
    [gap of 218ms]
  execute-generate-process completed in 1720ms
  [gap of 118ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 3400ms

