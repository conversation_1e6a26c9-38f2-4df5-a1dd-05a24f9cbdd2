import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  FlatList,
} from "react-native";
import React, { useEffect, useState } from "react";
import { router } from "expo-router";
import BackIcon from "../../../../../assets/OrderDetails/backIcon.svg";
import BellIcon from "../../../../../assets/ProfileAsser/SectionIcon/UserprofileAssert/bell-dynamic-gradient.svg";
import { SafeAreaView } from "react-native-safe-area-context";
import useGetApiData from "../../../../../hooks/useGetApiData";
import NotificationComponent from "../../../../../component/NotificationComponent";
import useSetApiData from "../../../../../hooks/useSetApiData";
import { useNotificationList } from "../../../../../store/Order";

const index = () => {
  const { data, isLoading } = useGetApiData({
    endpoint: "auth/notificationList",
  });
  const { notificationList, SetNotificationList } = useNotificationList(
    (state) => state
  );
  const { SetFunction } = useSetApiData({ endpoint: "auth/notificationRead" });

  useEffect(() => {
    SetNotificationList(data?.data);
  }, [isLoading]);

  useEffect(() => {
    return () => {
      if (Array.isArray(notificationList)) {
        notificationList?.map((not) => {
          SetFunction({
            notificationId: not?.id,
          });
        });
      }
    };
  }, []);

  return (
    <SafeAreaView className="flex-1">
      <View className="items-center justify-center relative mt-6">
        <TouchableOpacity
          className="absolute left-[3%]"
          onPress={() => {
            router.back();
          }}
        >
          <BackIcon />
        </TouchableOpacity>
        <View>
          <Text className="font-[600] text-[20px] leading-[30px]">
            Notifications
          </Text>
        </View>
      </View>
      {isLoading ? (
        <></>
      ) : (
        <>
          <FlatList
            data={data?.data}
            contentContainerStyle={{
              flex: 1,
            }}
            ListEmptyComponent={() => <EmptyComponent />}
            renderItem={(item) => {
              return (
                <>
                  <NotificationComponent items={item} />
                </>
              );
            }}
          />
        </>
      )}
    </SafeAreaView>
  );
};

const EmptyComponent = () => {
  return (
    <View className="flex-1 justify-center items-center">
      <View>
        <BellIcon />
      </View>
      <View className="mt-2">
        <Text className="font-[500] text-[16px] leading-[24px] text-[#627164]">
          You have not received any notifications yet
        </Text>
      </View>
    </View>
  );
};

export default index;
