{"logs": [{"outputFile": "com.mohandhass2.myapp-mergeReleaseResources-83:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,208,280,350,432,500,567,640,719,803,889,958,1035,1116,1192,1273,1354,1430,1505,1580,1666,1736,1812,1886", "endColumns": "75,76,71,69,81,67,66,72,78,83,85,68,76,80,75,80,80,75,74,74,85,69,75,73,78", "endOffsets": "126,203,275,345,427,495,562,635,714,798,884,953,1030,1111,1187,1268,1349,1425,1500,1575,1661,1731,1807,1881,1960"}, "to": {"startLines": "50,64,143,145,146,150,163,164,165,202,203,206,207,210,211,212,214,215,217,219,220,222,225,227,228", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3520,4849,11633,11775,11845,12157,13074,13141,13214,16188,16272,16533,16602,16834,16915,16991,17147,17228,17380,17531,17606,17793,18008,18207,18281", "endColumns": "75,76,71,69,81,67,66,72,78,83,85,68,76,80,75,80,80,75,74,74,85,69,75,73,78", "endOffsets": "3591,4921,11700,11840,11922,12220,13136,13209,13288,16267,16353,16597,16674,16910,16986,17067,17223,17299,17450,17601,17687,17858,18079,18276,18355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a39c8a492fe916611935c0d3835604e6\\transformed\\material-1.6.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,240,323,422,535,615,685,775,845,905,992,1057,1118,1182,1243,1297,1398,1459,1519,1573,1643,1754,1841,1922,2035,2114,2196,2288,2355,2421,2491,2569,2655,2727,2805,2874,2943,3025,3113,3206,3300,3374,3443,3538,3590,3658,3743,3831,3893,3957,4020,4120,4213,4310,4403", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,98,112,79,69,89,69,59,86,64,60,63,60,53,100,60,59,53,69,110,86,80,112,78,81,91,66,65,69,77,85,71,77,68,68,81,87,92,93,73,68,94,51,67,84,87,61,63,62,99,92,96,92,76", "endOffsets": "235,318,417,530,610,680,770,840,900,987,1052,1113,1177,1238,1292,1393,1454,1514,1568,1638,1749,1836,1917,2030,2109,2191,2283,2350,2416,2486,2564,2650,2722,2800,2869,2938,3020,3108,3201,3295,3369,3438,3533,3585,3653,3738,3826,3888,3952,4015,4115,4208,4305,4398,4475"}, "to": {"startLines": "19,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "706,3596,4394,4493,4606,7439,11235,11705,12097,12225,12312,12377,12438,12502,12563,12617,12718,12779,12839,12893,12963,13293,13380,13461,13574,13653,13735,13827,13894,13960,14030,14108,14194,14266,14344,14413,14482,14564,14652,14745,14839,14913,14982,15077,15129,15197,15282,15370,15432,15496,15559,15659,15752,15849,16111", "endLines": "22,51,59,60,61,87,139,144,149,151,152,153,154,155,156,157,158,159,160,161,162,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,201", "endColumns": "12,82,98,112,79,69,89,69,59,86,64,60,63,60,53,100,60,59,53,69,110,86,80,112,78,81,91,66,65,69,77,85,71,77,68,68,81,87,92,93,73,68,94,51,67,84,87,61,63,62,99,92,96,92,76", "endOffsets": "886,3674,4488,4601,4681,7504,11320,11770,12152,12307,12372,12433,12497,12558,12612,12713,12774,12834,12888,12958,13069,13375,13456,13569,13648,13730,13822,13889,13955,14025,14103,14189,14261,14339,14408,14477,14559,14647,14740,14834,14908,14977,15072,15124,15192,15277,15365,15427,15491,15554,15654,15747,15844,15937,16183"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6591935515ee6cd5ec7881ab1d3c64e\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "52,53,54,55,56,57,58,221", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3679,3775,3878,3976,4074,4177,4282,17692", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3770,3873,3971,4069,4172,4277,4389,17788"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\00636ad238812e00d92433007e026a91\\transformed\\exoplayer-core-2.18.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,194,262,330,407,480,571,657", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "126,189,257,325,402,475,566,652,731"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9423,9499,9562,9630,9698,9775,9848,9939,10025", "endColumns": "75,62,67,67,76,72,90,85,78", "endOffsets": "9494,9557,9625,9693,9770,9843,9934,10020,10099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eeb7a35c1d2dda21edaace2253c1f099\\transformed\\exoplayer-ui-2.18.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,478,656,737,816,895,983,1073,1144,1208,1299,1390,1454,1517,1582,1653,1762,1867,1980,2048,2131,2204,2275,2360,2443,2506,2570,2623,2681,2729,2790,2849,2917,2983,3051,3112,3171,3237,3304,3371,3425,3488,3570,3647", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,78,78,87,89,70,63,90,90,63,62,64,70,108,104,112,67,82,72,70,84,82,62,63,52,57,47,60,58,67,65,67,60,58,65,66,66,53,62,81,76,53", "endOffsets": "278,473,651,732,811,890,978,1068,1139,1203,1294,1385,1449,1512,1577,1648,1757,1862,1975,2043,2126,2199,2270,2355,2438,2501,2565,2618,2676,2724,2785,2844,2912,2978,3046,3107,3166,3232,3299,3366,3420,3483,3565,3642,3696"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,333,528,7509,7590,7669,7748,7836,7926,7997,8061,8152,8243,8307,8370,8435,8506,8615,8720,8833,8901,8984,9057,9128,9213,9296,9359,10104,10157,10215,10263,10324,10383,10451,10517,10585,10646,10705,10771,10838,10905,10959,11022,11104,11181", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,80,78,78,87,89,70,63,90,90,63,62,64,70,108,104,112,67,82,72,70,84,82,62,63,52,57,47,60,58,67,65,67,60,58,65,66,66,53,62,81,76,53", "endOffsets": "328,523,701,7585,7664,7743,7831,7921,7992,8056,8147,8238,8302,8365,8430,8501,8610,8715,8828,8896,8979,9052,9123,9208,9291,9354,9418,10152,10210,10258,10319,10378,10446,10512,10580,10641,10700,10766,10833,10900,10954,11017,11099,11176,11230"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7460a359c4c281b49833222722dfee74\\transformed\\play-services-wallet-18.1.3\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "70", "endOffsets": "272"}, "to": {"startLines": "231", "startColumns": "4", "startOffsets": "18552", "endColumns": "74", "endOffsets": "18622"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b14b43fc1759186e6ca5ed92b9460e08\\transformed\\foundation-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,152", "endColumns": "96,94", "endOffsets": "147,242"}, "to": {"startLines": "229,230", "startColumns": "4,4", "startOffsets": "18360,18457", "endColumns": "96,94", "endOffsets": "18452,18547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b84b30b80c5949bd0f3610f30992b047\\transformed\\play-services-base-18.3.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4926,5032,5179,5302,5409,5545,5669,5788,6025,6169,6274,6421,6543,6683,6834,6898,6966", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "5027,5174,5297,5404,5540,5664,5783,5891,6164,6269,6416,6538,6678,6829,6893,6961,7045"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4d414adee14b24510c264c21c70961f0\\transformed\\appcompat-1.7.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "891,996,1089,1197,1282,1384,1494,1572,1649,1740,1833,1924,2018,2118,2211,2306,2400,2491,2582,2663,2766,2864,2962,3065,3171,3272,3425,16679", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "991,1084,1192,1277,1379,1489,1567,1644,1735,1828,1919,2013,2113,2206,2301,2395,2486,2577,2658,2761,2859,2957,3060,3166,3267,3420,3515,16756"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f0ad3059e848ec2624bad4e9e6fc7d3\\transformed\\browser-1.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "83,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7050,11325,11424,11535", "endColumns": "102,98,110,97", "endOffsets": "7148,11419,11530,11628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2d12819c5c8831c86b34929884541b49\\transformed\\play-services-basement-18.3.0\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "5896", "endColumns": "128", "endOffsets": "6020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e9aa3803274a6cf3eaa3a68ef58f1ff5\\transformed\\ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,979,1068,1141,1216,1292,1368,1446,1513", "endColumns": "85,76,96,100,87,84,84,85,82,85,88,72,74,75,75,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,974,1063,1136,1211,1287,1363,1441,1508,1631"}, "to": {"startLines": "62,63,84,85,86,147,148,199,200,204,205,209,213,216,218,223,224,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4686,4772,7153,7250,7351,11927,12012,15942,16028,16358,16444,16761,17072,17304,17455,17863,17941,18084", "endColumns": "85,76,96,100,87,84,84,85,82,85,88,72,74,75,75,77,66,122", "endOffsets": "4767,4844,7245,7346,7434,12007,12092,16023,16106,16439,16528,16829,17142,17375,17526,17936,18003,18202"}}]}]}