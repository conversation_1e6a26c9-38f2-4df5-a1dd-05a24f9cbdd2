import { View, Text, TouchableOpacity, Image } from "react-native";
import React, { forwardRef, useRef } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import CoinIcon from "../../../../assets/Rewards/grommet-icons_money.svg";
import RsIcon from "../../../../assets/Rewards/Rs.svg";
import LockIcon from "../../../../assets/Rewards/mdi_lock-outline.svg";
import TrophyIcon from "../../../../assets/Rewards/fluent-emoji_trophy.svg";
import TrophyIconSmall from "../../../../assets/Rewards/smalltrophy.svg";
import TshirtImg from "../../../../assets/Rewards/T-shirtImg.png";
import Card from "../../../../assets/Rewards/card.png";
import RBSheet from "react-native-raw-bottom-sheet";
import { router } from "expo-router";

const index = () => {
  return (
    <SafeAreaView>
      <View className="px-4 mt-8">
        <View className="">
          <TouchableOpacity className="flex-row items-center justify-center h-[49px] bg-[#D4FFD8]">
            <CoinIcon />
            <View className="">
              <Text className="">All Time Earnings</Text>
            </View>
          </TouchableOpacity>
        </View>
        <View className="mt-9 flex-row items-center">
          <View className="flex-1 items-center justify-center">
            <View className="flex-row items-center">
              <RsIcon />
              <Text className="font-[600] font-Pop text-[24px] leading-[36px] ">
                3500
              </Text>
            </View>
            <View className="">
              <Text className="">All Time</Text>
            </View>
          </View>
          <View className="w-[1px] h-full bg-[#939D94]" />
          <View className="flex-1 items-center justify-center">
            <View className="flex-row items-center">
              <Text className="font-[600] font-Pop text-[24px] leading-[36px] ">
                6
              </Text>
            </View>
            <View className="">
              <Text className="">Deliveries</Text>
            </View>
          </View>
          <View className="w-[1px] h-full bg-[#939D94]" />
          <View className="flex-1 items-center justify-center">
            <View className="flex-row items-center">
              <Text className="font-[600] font-Pop text-[24px] leading-[36px] ">
                19 kms
              </Text>
            </View>
            <View className="">
              <Text className="">Distance travelled</Text>
            </View>
          </View>
        </View>

        <View className="mt-6 flex-row items-center space-x-4">
          <TrophyIcon />
          <Text className="font-[500] font-Pop text-[16px] leading-[24px]">
            Explore Rewards
          </Text>
        </View>

        <View className="mt-4">
          <Text className="my-3">New Partner Rewards</Text>
          <View className="mt-4 flex-row flex-wrap justify-between">
            <RewardCardComponent
              ImageComp={() => {
                return (
                  <>
                    <View className="bg-[#00660A]">
                      <Image source={Card} />
                    </View>
                  </>
                );
              }}
              name={"Scratch Card upto ₹500"}
              remaining={"3 deliveries left"}
              percent={"70%"}
              type={"ScratchCard"}
            />
            <RewardCardComponent
              ImageComp={() => {
                return (
                  <>
                    <Image source={TshirtImg} />
                  </>
                );
              }}
              name={"Ordalane T-Shirt"}
              remaining={"3 deliveries left"}
              percent={"100%"}
              type={"TShirt"}
            />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export const RewardCardComponent = ({
  ImageComp,
  name,
  remaining,
  percent,
  type,
}) => {
  const RewardBottomSheetRef = useRef();

  return (
    <>
      <View className="flex-1 border-[1px] border-[#E9EAE9] rounded-[8px] overflow-hidden">
        <View className="">{ImageComp()}</View>
        <View className="p-2">
          <View className="my-2 flex-row space-x-2 h-[26px] w-[95px] items-center justify-center border-[1px] border-[#001A03] rounded-[20px]">
            <LockIcon />
            <View className="">
              <Text className="">Locked</Text>
            </View>
          </View>
          <View className="">
            <Text className="my-2">{name}</Text>
          </View>
          <View className="">
            <View className="relative my-2 h-[6px] rounded-[8px] bg-[#D9D9D9] w-full">
              <View
                className=" bg-[#00660A] h-full rounded-[8px]"
                style={{ width: percent }}
              />
            </View>
            <Text className="font-[500] font-Pop leading-[15px] text-[#00660A]">
              {remaining}
            </Text>
          </View>
          <View className="mt-4 mb-2">
            <TouchableOpacity
              disabled={percent === "100%" ? false : true}
              style={{
                opacity: percent === "100%" ? 1 : 0.6,
              }}
              className="h-[30px] items-center justify-center rounded-[4px] bg-[#00660A]"
              onPress={() => {
                RewardBottomSheetRef.current.open();
              }}
            >
              <Text className="font-[400] font-Pop text-[12px] text-[#fff] leading-[18px]">
                Claim Reward
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
      <RewardBottomSheet
        ref={RewardBottomSheetRef}
        imageComp={ImageComp}
        type={type}
        name={name}
      />
    </>
  );
};

export const RewardBottomSheet = forwardRef((props, ref) => {
  return (
    <>
      <RBSheet
        ref={ref}
        customStyles={{
          container: {
            borderRadius: 20,
            height: 400,
            position: "relative",
          },
          draggableIcon: {
            backgroundColor: "#000",
          },
        }}
        useNativeDriver={true}
        customModalProps={{
          statusBarTranslucent: true,
        }}
        customAvoidingViewProps={{
          enabled: false,
        }}
      >
        <View className="px-8 items-center justify-center mt-4">
          <View className="">{props?.imageComp()}</View>
          <View className="">
            <Text className="font-[500] font-Pop leading-[36px] text-[24px] my-2">
              {props?.name}
            </Text>
          </View>
          <View className="">
            <Text className="text-center">
              Complete 6 deliveries and score an exclusive T-shirt, plus many
              more goodies along the way. Start earning today!
            </Text>
          </View>
          <View className="mt-4 flex-row items-center space-x-2">
            <TrophyIconSmall />
            <Text className="">6 Deliveries</Text>
          </View>
          <View className="mt-4">
            <Text className="text-[#00660A] font-[500] text-[14px] leading-[21px]">
              Claim your reward today !!
            </Text>
          </View>
        </View>
        <View className="mt-4 px-4 ">
          <TouchableOpacity
            onPress={() => {
              router.push(props?.type === "TShirt" ? "home/rewards/preview" : "home/rewards/scratchcard");
            }}
            className="h-[44px] w-full items-center justify-center bg-[#00660A] rounded-[4px]"
          >
            <Text className="font-[400] font-Pop text-[16px] text-[#fff] leading-[24px]">
              Claim Reward
            </Text>
          </TouchableOpacity>
        </View>
      </RBSheet>
    </>
  );
});

export default index;
