import RazorpayCheckout from "react-native-razorpay";

export const Payment = async (amount) => {
  const options = {
    description: "Credits towards consultation",
    image: "https://i.imgur.com/3g7nmJC.png",
    currency: "INR",
    key: "rzp_test_yaXUQ9vZbfNIxc",
    amount: String(amount * 100),
    name: "Test Merchant",
    prefill: {
      email: "<EMAIL>",
      contact: "9999999999",
      name: "Test User",
    },
    theme: { color: "#F37254" },
  };
  try {
    console.log("Initiating Razorpay with options:", options);
    const data = await RazorpayCheckout.open(options);
    console.log("Razorpay Success:", data);
    alert(`Payment Success: ${data.razorpay_payment_id}`);
    return true;
  } catch (error) {
    console.error("Razorpay Error Details:", error);
    alert(`Payment Failed: ${error?.description || "Something went wrong"}`);
    return false;
  }
};
