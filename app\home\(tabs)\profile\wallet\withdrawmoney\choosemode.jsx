import { View, Text, Dimensions, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { router, useLocalSearchParams } from "expo-router";
import Rsicon from "@/assets/Hometab/rs.svg";
import Gpayimg from "@/assets/PaymentImg/Gpay.svg";
import PhonePay from "@/assets/PaymentImg/Phone.svg";
import Paytmimg from "@/assets/PaymentImg/paytm.svg";
import IcicIcon from "@/assets/PaymentImg/icicIcon.svg";
import BackArrow from "@/assets/icons/BackArrow.svg";
import useSetApiData from "../../../../../../hooks/useSetApiData";

const paymentmode = () => {
  const { amount } = useLocalSearchParams();
  const { SetFunction } = useSetApiData({
    endpoint: "withdrawl_money_from_wallet",
  });
  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 justify-between px-4 bg-[#FFFFFF]">
        <View>
          {/* Title Area  */}
          <View className="justify-between mt-3">
            <View className="flex-row relative items-center justify-start mt-4">
              <TouchableOpacity
                onPress={() => {
                  router.back();
                }}
                className="absolute"
              >
                <View>
                  <BackArrow />
                </View>
              </TouchableOpacity>
              <View className="flex-1 items-center justify-center">
                <Text className="font-[400] text-[26px] leading-[39px]">
                  Choose Account
                </Text>
              </View>
            </View>
          </View>
          {/* Amount Area */}
          <View className="flex-row items-center justify-between p-6 mt-8 border-[1px] border-[#E7EFFB] rounded-[4px]">
            <View className="">
              <Text className="font-[500] text-[#000] leading-[27px] text-[18px]">
                Amount Withdrawable
              </Text>
              <Text className="font-[400] text-[#81878C] text-[14px] leading-[21px]">
                *including taxes
              </Text>
            </View>
            <View className="flex-row items-center">
              <Rsicon />
              <View>
                <Text className="font-[600] text-[20px] leading-[30px] text-[#627164]">
                  {amount}
                </Text>
              </View>
            </View>
          </View>

          {/* Payment Area */}
          {/* <View className="mt-4 border-[1px] border-[#E7EFFB] rounded-[4px]">
              <View className="">
                <Text className="font-[400] text-[16px] leading-[20px] text-[#4D4D4D]">
                  Select Refund Account
                </Text>
              </View>
            </View> */}

          {/* Banks Area */}
          {/* <View className="border-[1px] border-[#E7EFFB] rounded-[4px] mt-6">
            <View className="flex-row items-center justify-between p-5">
              <View className="flex-row items-center space-x-6">
                <IcicIcon />
                <View>
                  <Text className="font-[500] text-[14px] text-[#151E28]">
                    ICICI Bank
                  </Text>
                  <Text className="font-[500] text-[14px] text-[#627164]">
                    ******4567
                  </Text>
                </View>
              </View>
              <TouchableOpacity onPress={() => {}}>
                <Text className="font-[400] text-[16px] leading-[20px]">
                  Edit
                </Text>
              </TouchableOpacity>
            </View>
          </View> */}
        </View>
        <View className="mb-10 items-center justify-center">
          <TouchableOpacity
            onPress={() => {
              SetFunction({
                amount: amount,
              })
              router.push("home/profile/wallet/withdrawmoney/paysuccess");
            }}
            className="h-[44px] w-[224px] items-center justify-center bg-[#00660A]"
          >
            <Text className="font-[500] text-[16px] leading-[24px] text-[#FFFFFF]">
              Confirm
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default paymentmode;
