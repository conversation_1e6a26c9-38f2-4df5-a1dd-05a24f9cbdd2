<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.google.mlkit:barcode-scanning:17.2.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f3ffc0304c3c0f21b526f53f0d7750c\transformed\barcode-scanning-17.2.0\assets"><file name="mlkit_barcode_models/barcode_ssd_mobilenet_v1_dmp25_quant.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f3ffc0304c3c0f21b526f53f0d7750c\transformed\barcode-scanning-17.2.0\assets\mlkit_barcode_models\barcode_ssd_mobilenet_v1_dmp25_quant.tflite"/><file name="mlkit_barcode_models/oned_auto_regressor_mobile.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f3ffc0304c3c0f21b526f53f0d7750c\transformed\barcode-scanning-17.2.0\assets\mlkit_barcode_models\oned_auto_regressor_mobile.tflite"/><file name="mlkit_barcode_models/oned_feature_extractor_mobile.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f3ffc0304c3c0f21b526f53f0d7750c\transformed\barcode-scanning-17.2.0\assets\mlkit_barcode_models\oned_feature_extractor_mobile.tflite"/></source></dataSet><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-modules-core\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-web-browser" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-web-browser\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-system-ui" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-system-ui\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-splash-screen" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-splash-screen\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-notifications\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-media-library" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-media-library\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-location" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-location\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-linking" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-linking\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-linear-gradient" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-linear-gradient\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-keep-awake" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-keep-awake\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-image-picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-picker\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-image-loader" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-image-loader\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-font" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-font\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-file-system" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-file-system\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-document-picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-document-picker\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-device" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-device\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-constants\android\build\intermediates\library_assets\release\packageReleaseAssets\out"><file name="app.config" path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-constants\android\build\intermediates\library_assets\release\packageReleaseAssets\out\app.config"/></source></dataSet><dataSet config=":expo-camera" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-camera\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-calendar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-calendar\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-av" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-av\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-asset" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-asset\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-application" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo-application\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-svg" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-svg\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-razorpay" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-razorpay\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-quick-base64" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-quick-base64\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-maps" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-maps\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":shopify_react-native-skia" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\@shopify\react-native-skia\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-community_slider" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\@react-native-community\slider\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-community_datetimepicker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\@react-native-community\datetimepicker\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-screens\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\react-native-gesture-handler\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\node_modules\expo\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\main\assets"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\android\app\src\release\assets"/></dataSet><dataSet config="assets-createBundleReleaseJsAndAssets" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\generated\assets\createBundleReleaseJsAndAssets"><file name="index.android.bundle" path="D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\generated\assets\createBundleReleaseJsAndAssets\index.android.bundle"/></source></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\App\Delivery-Partner\Delivery-Partner-App\android\app\build\intermediates\shader_assets\release\compileReleaseShaders\out"/></dataSet></merger>