import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { loadDashboard } from "@/redux/dashboard/asyncReducers";
import { loadOrders } from "@/redux/orders/asyncReducers";
import { loadProfile } from "@/redux/profile/asyncReducers";
import { selectProfile } from "@/redux/profile/meno";
import { AppDispacher } from "@/redux/store";

const useLoadData = () => {
  const [isLoading, setLoading] = useState(false);
  const dispacher = useDispatch<AppDispacher>();
  const profile = useSelector(selectProfile);

  const LoadData = async () => {
    setLoading(true);
    try {
      await dispacher(loadProfile());
      await dispacher(loadDashboard());
      // await dispacher(loadOrders({ id: profile.id }));
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    LoadData();
  }, []);
  return { isLoading, LoadData };
};

export default useLoadData;
