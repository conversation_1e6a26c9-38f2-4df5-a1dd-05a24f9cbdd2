{"logs": [{"outputFile": "com.mohandhass2.myapp-mergeReleaseResources-83:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9c569f151f8eecc9b25adff1be1284e4\\transformed\\Android-Image-Cropper-4.3.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1888,1889,1890,1969,1970,1971,1972,1973,2064,2065,2066", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "119259,119371,119418,125909,125953,126023,126089,126160,139617,139670,139737", "endColumns": "111,46,53,43,69,65,70,53,52,66,54", "endOffsets": "119366,119413,119467,125948,126018,126084,126155,126209,139665,139732,139787"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c70b754ff3b176c20bb896d71baebb6b\\transformed\\camera-view-1.4.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "206,427", "startColumns": "4,4", "startOffsets": "9877,20692", "endLines": "209,434", "endColumns": "11,11", "endOffsets": "10024,20994"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6063bb0af1d30b1b1d98ea038715db0d\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1641", "startColumns": "4", "startOffsets": "101035", "endColumns": "65", "endOffsets": "101096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ea79fc2a98096845c57bc3721521abee\\transformed\\work-runtime-2.7.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "567,568,569,572", "startColumns": "4,4,4,4", "startOffsets": "27821,27886,27956,28121", "endColumns": "64,69,63,60", "endOffsets": "27881,27951,28015,28177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\030dd61fccce7abad1860c46c9bc2833\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1823", "startColumns": "4", "startOffsets": "111834", "endColumns": "82", "endOffsets": "111912"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a39c8a492fe916611935c0d3835604e6\\transformed\\material-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "15,23,39,40,41,42,46,47,48,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,101,102,122,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,201,203,211,212,213,214,215,216,218,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,388,389,390,411,426,435,441,442,443,444,445,460,462,464,465,473,474,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,515,516,528,536,538,571,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,814,815,816,817,818,819,820,821,822,823,824,825,848,943,944,951,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1539,1540,1651,1652,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1676,1679,1680,1681,1699,1700,1701,1702,1703,1704,1705,1716,1727,1728,1732,1733,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1781,1791,1825,1826,1827,1861,1862,1863,1864,1865,1896,1955,1956,1957,1968,1974,1980,1981,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2059,2060,2061,2062,2063,2100,2114,2118,2168,2176,2183,2353,2363,2372,2381,2450,2451,2452,2453,2454,2455,2456,2457,2458,2463,2464,2468,2469,2475,2479,2480,2481,2482,2492,2493,2494,2498,2499,2505,2509,2582,2585,2586,2589,2592,2593,2594,2595,2795,2801,2999,3199,3205,3403,3466,3548,3600,3682,3744,3826,3890,3942,4024,4031,4042,4046,4050,4823,4839,4846,4852,4864,4883,4900,4905,4910,4917,4927,4940,4954,4958,4965,4969,4975,4986,4989,4993,5008,5009,5057,5061,5065,5069,5070,5071,5074,5088,5095,5109,5151,5200,5370,5375,5379,5384,5391,5397,5398,5401,5405,5410,5422,5426,5431,5436,5441,5444,5447,5450,5454,5601,5602,5603,5604,5686,5690,5696,5700,5706,5710,5714,5720,5726,5730,5734,5738,5739,5740,5741,5742,5743,5744,5745,5749,5753,5756,5760,5763,5766,5769,5772,5775,5779,5783,5784,5787,5790,5793,5796,5799,5802,5805,5808,5812,5815,5819,5822,5825,5828,5831,5834,5837,5841,5844,5861,5865,5868,5878,5886,5894,5897,5900,5903,5906,5909,5984,5987,5988,5991,5994,5995,5998,5999,6000,6004,6005,6010,6018,6026,6034,6042,6050,6058,6066,6074,6082,6091,6100,6109,6117,6126,6135,6138,6141,6142,6143,6144,6145,6146,6147,6148,6149,6150,6151,6152,6153,6156,6157,6158,6159,6160,6168,6176,6177,6185,6189,6197,6205,6213,6221,6229,6230,6238,6246,6247,6250,6253,6254,6257,6335,6337,6342,6344,6349,6353,6370,6371,6372,6373,6377,6381,6382,6386,6387,6388,6389,6390,6391,6392,6393,6394,6395,6396,6397,6398,6399,6403,6407,6408,6412,6413,6418,6419,6420,6421,6422,6423,6424,6425,6426,6427,6428,6429,6430,6431,6432,6433,6434,6435,6436,6437,6438,6442,6443,6444,6450,6451,6455,6457,6458,6461,6466,6467,6468,6469,6470,6471,6475,6476,6477,6483,6484,6488,6490,6493,6497,6501,6505,6559,6560,6561,6562,6565,6568,6571,6574,6577,6581,6582,6587,6591,6596,6600,6605,6609,6613,6646,6647,6648,6649,6653,6654,6655,6656,6660,6664,6668,6672,6678,6679,6712,6725,6730,6756,6762,6765,6776,6781,6784,6787,6790,6793,6796,6799,6802,6806,6809,6810,6811,6819,6827,6830,6835,6840,6845,6850,6854,6858,6859,6867,6868,6869,6870,6871,6879,6884,6889,6890,6891,6892,6917,6922,6927,6930,6934,6937,6941,6951,6954,6959,6962,6966,6970,7136,7144,7157,7170,7174,7189,7200,7203,7214,7219,7223,7258,7259,7260,7267,7271,7275,7279,7283,7294,7310,7324,7331,7340,7343,7363,7373,7377,7381,7392,7398,7402,7419,7427,7431,7435,7439,7442,7446,7450,7454,7461,7465,7466,7470,7491,7501,7521,7530,7546,7556,7560,7570,7590,7600,7603,7604,7605,7606,7607,7611,7615,7619,7620,7621,7622,7625,7628,7631,7634,7637,7640,7643,7646,7649,7652,7655,7658,7661,7664,7667,7670,7673,7674,7677,7688,7696,7700,7705,7710,7713,7716,7722,7726,7729,7732,7736,7741,7745,7748,7756,7760,7764,7775,7780,7785,7786,7789,7792,7793,7803,7808,7816,7819,7823,7836,7839,7843,7858,7865,7891,7894,7897,7900,7903,7913,7919,7920,7925,7926,7927,7928,7932,7936,7940,7944,7973,7976,7980,7984,8012,8015,8019,8023,8032,8038,8041,8050,8054,8055,8062,8066,8073,8074,8075,8078,8083,8088,8089,8093,8101,8116,8120,8121,8133,8143,8144,8156,8161,8185,8188,8194,8197,8206,8214,8218,8221,8224,8227,8231,8234,8251,8255,8258,8273,8276,8284,8289,8296,8301,8302,8307,8308,8314,8320,8326,8357,8368,8385,8392,8396,8399,8411,8420,8424,8429,8433,8437,8441,8445,8449,8453,8457,8460,8469,8474,8483,8486,8493,8494,8498,8507,8513,8517,8518,8522,8543,8549,8553,8557,8558,8576,8577,8578,8579,8580,8585,8588,8589,8595,8596,8608,8620,8627,8628,8633,8638,8639,8643,8657,8662,8668,8674,8680,8685,8691,8697,8698,8703,8717,8722,8731,8740,8743,8756,8759,8770,8774,8783,8792,8793,8800", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "595,932,1560,1616,1676,1737,1941,1991,2041,2094,2152,2251,2320,2368,2439,2511,2583,2656,2723,2772,2826,2863,2914,2961,3017,3066,3124,3180,3231,3291,3340,3396,3452,3502,3561,3608,3664,3718,3772,3826,3875,3933,3989,4036,4090,4144,4192,4868,4922,5755,5866,5928,5984,6044,6097,6158,6237,6318,6390,6469,6549,6625,6703,6772,6848,6925,6996,7069,7133,7204,9626,9722,10078,10131,10183,10233,10291,10356,10469,15963,16030,16096,16154,16223,16281,16350,16420,16493,16567,16635,16702,16772,16838,16911,16971,17047,17107,17167,17242,17310,17376,17444,17504,17563,17620,17686,17748,17805,17864,17926,17988,18055,18112,18168,18224,18282,18340,18397,18454,18513,18572,18630,18684,18740,19075,19128,19186,20086,20646,20999,21345,21399,21467,21536,21604,22324,22422,22531,22577,22914,22970,23064,23122,23180,23242,23305,23367,23426,23486,23551,23617,23682,23744,23806,23868,23930,23992,24054,24120,24187,24253,24316,24380,24443,24511,24572,24634,24696,24759,24823,24886,24950,25028,25087,25153,25233,25294,25347,25507,25565,26056,26394,26507,28059,31327,31401,31472,31538,31612,31681,31752,31825,31896,31964,32037,32113,32183,32261,32329,32395,32456,32525,32589,32655,32723,32789,32852,32920,32991,33056,33129,33192,33273,33337,33403,33473,33543,33613,33683,34967,35024,35082,35141,35201,35260,35319,35378,35437,35496,35555,35614,35673,35732,35791,35851,35912,35974,36035,36096,36157,36218,36279,36340,36401,36462,36523,36584,36652,36721,36791,36860,36929,36998,37067,37136,37205,37274,37343,37412,37481,37541,37602,37664,37725,37786,37847,37908,37969,38030,38091,38152,38213,38274,38336,38399,38463,38526,38589,38652,38715,38778,38841,38904,38967,39030,39093,39154,39216,39279,39341,39403,39465,39527,39589,39651,39713,39775,39837,39899,39956,40043,40123,40213,40308,40400,40492,40582,40665,40758,40845,40942,41033,41134,41221,41324,41413,41512,41604,41688,41782,41870,41968,42052,42152,42238,42334,42422,42503,42594,42690,42783,42876,42967,43052,43146,43235,43333,43426,43528,43616,43720,43811,43911,44004,44089,44184,44273,44372,44457,44558,44645,45414,45480,45556,45625,45704,45769,45835,45888,45964,46030,46117,46193,47833,54204,54251,54697,55292,55342,55396,55475,55553,55626,55691,55754,55820,55891,55962,56032,56094,56163,56229,56289,56356,56423,56479,56530,56583,56635,56689,56760,56823,56882,56944,57003,57076,57143,57213,57273,57336,57411,57483,57579,57650,57706,57777,57834,57891,57957,58021,58092,58149,58202,58265,58317,58375,61691,61760,61826,61885,61968,62027,62084,62151,62221,62295,62357,62426,62496,62550,62603,62657,62716,62762,62819,62886,62942,63007,63081,63165,63238,63303,63365,63421,63504,63593,63657,63736,63810,63869,63925,63981,64041,64101,64148,64208,64269,64333,64394,64454,64512,64555,64604,64656,64707,64759,64808,64857,64922,64988,65048,65109,65165,65224,65299,65356,65440,65497,65572,65647,65698,65766,65816,65878,65938,65995,66055,66104,66185,66242,66303,66362,66422,66480,66541,66599,66649,66698,66765,66824,66883,66932,67007,67078,67147,67210,67278,67344,67412,67477,67543,67620,67698,67762,67841,67930,68008,68074,68143,68209,68307,68403,68499,68597,68706,68762,68854,68903,68957,69011,69065,69119,69173,69228,69338,69448,69558,69668,69778,69888,69998,70108,70214,70320,70426,70532,70627,70722,70817,70912,71018,71124,71230,71336,71431,71526,71621,71716,71824,71932,72040,72148,72245,72342,72439,72536,72644,72752,72860,72968,73066,73162,73258,73356,73421,73509,73573,73635,73697,73765,73823,73886,73952,74015,74082,74154,74220,74272,74332,74390,74442,74499,74583,74678,74763,74844,74924,75001,75080,75157,75231,75305,75376,75456,75528,75603,75656,75709,75777,75854,75919,75980,76040,76115,76189,76266,76339,76409,76481,76551,76624,76688,76758,76806,76875,76927,77012,77095,77153,77219,77286,77352,77433,77508,77564,77617,77678,77736,77786,77835,77884,77933,77995,78047,78092,78173,78224,78278,78331,78385,78436,78485,78551,78602,78663,78724,78786,78836,78877,78954,79013,79072,79131,79192,79248,79304,79371,79432,79497,79552,79617,79686,79754,79832,79901,79961,80032,80106,80171,80243,80313,80380,80464,80533,80600,80670,80733,80800,80868,80951,81030,81120,81197,81265,81332,81410,81467,81524,81592,81658,81714,81774,81833,81887,81937,81987,82035,82097,82148,82221,82301,82381,82445,82508,82575,82646,82704,82765,82831,82890,82957,83017,83077,83140,83208,83269,83336,83414,83484,83533,83590,83659,83720,83808,83896,83984,84072,84128,84215,84302,84389,84476,84534,84608,84678,84734,84805,84870,84932,85007,85080,85170,85236,85302,85363,85427,85489,85547,85618,85701,85760,85831,85897,85962,86023,86082,86153,86219,86284,86367,86443,86518,86599,86659,86728,86798,86867,86922,86978,87034,87095,87153,87209,87264,87326,87379,87436,87530,87599,87700,87751,87821,87884,87942,88012,88081,88151,88221,88291,88358,88425,88500,88567,88626,88680,88734,88788,88841,88893,90459,90500,90565,90639,90712,90780,90840,90898,90959,91025,91091,91156,91220,95574,95711,101522,101571,101768,101816,101872,101930,101992,102047,102105,102176,102240,102299,102361,102427,102862,103007,103051,103096,104074,104125,104172,104217,104268,104319,104370,104887,105487,105553,105800,105863,106159,106216,106271,106329,106384,106443,106499,106560,106623,106684,106745,106806,106867,106928,106989,107050,107109,107170,107231,107292,107353,107414,107475,107526,107592,107658,107726,107794,107860,107927,108001,108064,108121,108181,108246,108313,108378,108435,108496,108554,108624,108671,108723,108837,109531,111971,112121,112252,116144,116242,116357,116442,116490,119833,124435,124524,124681,125756,126214,126881,126940,127092,127188,127278,127374,127464,127630,127755,127880,128050,128160,128281,128402,128512,128624,128747,128870,128952,129126,129274,129433,129588,129761,129878,129995,130163,130275,130389,130563,130741,130874,130986,131132,131284,131416,131559,132075,132253,132435,132752,132934,133116,133306,133496,133695,133852,133962,134145,134282,134502,134686,134846,135004,135188,135391,135562,135782,136004,136159,136359,136543,136646,136787,136952,137123,137323,137527,137729,137934,138135,138334,138538,138815,138893,139194,139360,139515,143027,143931,144208,147443,147894,148350,159487,160001,160438,160872,165212,165297,165418,165517,165604,165727,165828,165921,166028,166371,166478,166723,166844,167253,167501,167601,167706,167825,168334,168481,168600,168851,168984,169399,169653,175134,175340,175465,175682,175882,176003,176136,176283,190996,191432,205985,220734,221172,235761,240602,246019,250110,255541,260283,265660,269644,273636,279027,279518,280274,280504,280747,329121,330025,330489,330882,331552,332711,333708,334001,334300,334683,335458,336223,337110,337376,337852,338126,338523,339196,339374,339603,340528,340619,342813,343079,343401,343611,343720,343839,344023,344995,345465,346216,348693,350939,360445,360773,361001,361260,361836,362190,362312,362451,362725,362985,363855,364141,364544,364946,365289,365501,365702,365915,366204,377288,377361,377448,377533,382447,382619,382889,383060,383328,383493,383660,383929,384194,384362,384527,384693,384813,384933,385041,385151,385263,385371,385481,385646,385812,385991,386156,386311,386431,386592,386755,386916,387081,387248,387301,387434,387554,387652,387765,387884,388001,388156,388288,388517,388680,388873,388999,389151,389293,389463,389619,389791,390082,390194,391267,391496,391714,392569,393156,393770,393938,394080,394241,394384,394552,401563,401758,401850,402023,402185,402280,402449,402543,402632,402875,402964,403257,403722,404191,404661,405136,405602,406067,406533,407000,407463,407933,408406,408878,409338,409809,410281,410471,410650,410756,410864,410970,411082,411196,411308,411422,411538,411652,411760,411870,411978,412174,412282,412392,412500,412614,413023,413437,413553,413971,414212,414642,415077,415487,415909,416319,416441,416850,417266,417388,417606,417790,417851,418023,422969,423037,423381,423461,423817,423967,424845,424921,425033,425123,425385,425650,425749,425901,425977,426089,426179,426281,426389,426497,426597,426682,426786,426873,426951,427065,427157,427421,427688,427789,427942,428026,428415,428513,428621,428715,428845,428953,429075,429211,429319,429439,429573,429695,429823,429965,430091,430231,430357,430475,430607,430705,430815,431115,431227,431345,431809,431925,432228,432354,432450,432580,432981,433091,433215,433353,433463,433585,433897,434021,434151,434627,434755,435070,435208,435354,435516,435732,435888,439348,439416,439500,439604,439807,439996,440197,440390,440595,440800,440916,441111,441299,441511,441663,441882,442024,442185,444683,444797,444917,445011,445332,445431,445549,445650,445880,446116,446326,446559,446983,447059,449567,450822,451266,453120,453577,453785,454795,455175,455316,455451,455648,455819,456002,456177,456364,456586,456744,456828,456932,457419,457975,458133,458352,458583,458806,459041,459263,459529,459667,460266,460380,460518,460630,460754,461325,461820,462366,462511,462604,462696,464623,465066,465364,465553,465759,465952,466162,467046,467191,467583,467741,467958,468219,480069,480501,481327,481947,482144,483092,483857,483980,484753,484974,485174,487151,487251,487341,487712,488011,488322,488625,488940,489509,490260,491335,491882,492440,492606,494003,494619,494855,495076,495866,496261,496497,497766,498215,498402,498651,498893,499069,499310,499543,499768,500110,500323,500418,500679,501534,501987,502706,503154,503749,504201,504408,504865,505574,506031,506181,506305,506451,506589,506725,507013,507314,507616,507732,507854,507966,508115,508373,508635,508893,509153,509401,509653,509901,510151,510395,510643,510887,511133,511365,511601,511833,512067,512179,512370,513229,513827,514023,514260,514528,514744,514962,515234,515533,515725,515937,516229,516493,516782,517001,517526,517763,518055,518794,519032,519301,519441,519611,519755,519857,520539,520879,521501,521727,522024,522748,523012,523327,524493,525053,527148,527342,527560,527786,527998,528673,528938,529034,529381,529469,529577,529685,529979,530285,530583,530893,532972,533160,533421,533670,535647,535839,536104,536357,536873,537223,537392,537906,538141,538265,538677,538891,539293,539396,539526,539701,539953,540149,540289,540483,540981,541862,542150,542280,543057,543714,543860,544566,544804,546344,546494,546911,547076,547762,548232,548428,548519,548603,548747,548981,549148,550076,550362,550522,551137,551296,551624,551851,552363,552725,552804,553143,553248,553613,553984,554345,556167,556796,557872,558296,558549,558701,559691,560428,560631,560877,561124,561342,561584,561905,562169,562474,562697,562869,563410,563679,564173,564399,564839,564998,565282,566027,566392,566697,566855,567093,568412,568810,569038,569258,569400,570690,570796,570926,571064,571188,571476,571645,571745,572030,572144,573027,573782,574221,574345,574591,574784,574918,575109,575888,576106,576397,576676,576993,577215,577510,577793,577897,578180,578912,579228,579789,580295,580500,581229,581504,582165,582354,582905,583471,583591,583993", "endLines": "15,23,39,40,41,42,46,47,48,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,101,102,122,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,201,203,211,212,213,214,215,216,218,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,383,388,389,390,411,426,435,441,442,443,444,445,460,462,464,465,473,474,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,515,516,528,536,538,571,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,814,815,816,817,818,819,820,821,822,823,824,825,848,943,944,951,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1539,1540,1651,1652,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1676,1679,1680,1681,1699,1700,1701,1702,1703,1704,1705,1716,1727,1728,1732,1733,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1781,1794,1825,1826,1827,1861,1862,1863,1864,1865,1896,1955,1956,1957,1968,1974,1980,1981,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2059,2060,2061,2062,2063,2102,2117,2121,2175,2182,2190,2362,2371,2380,2389,2450,2451,2452,2453,2454,2455,2456,2457,2462,2463,2467,2468,2474,2478,2479,2480,2481,2491,2492,2493,2497,2498,2504,2508,2509,2584,2585,2588,2591,2592,2593,2594,2794,2800,2998,3198,3204,3402,3465,3547,3599,3681,3743,3825,3889,3941,4023,4030,4041,4045,4049,4062,4838,4845,4851,4863,4882,4899,4904,4909,4916,4926,4939,4953,4957,4964,4968,4974,4985,4988,4992,5007,5008,5056,5060,5064,5068,5069,5070,5073,5087,5094,5108,5150,5151,5200,5374,5378,5383,5390,5396,5397,5400,5404,5409,5421,5425,5430,5435,5440,5443,5446,5449,5453,5457,5601,5602,5603,5604,5689,5695,5699,5705,5709,5713,5719,5725,5729,5733,5737,5738,5739,5740,5741,5742,5743,5744,5748,5752,5755,5759,5762,5765,5768,5771,5774,5778,5782,5783,5786,5789,5792,5795,5798,5801,5804,5807,5811,5814,5818,5821,5824,5827,5830,5833,5836,5840,5843,5846,5864,5867,5877,5885,5893,5896,5899,5902,5905,5908,5911,5986,5987,5990,5993,5994,5997,5998,5999,6003,6004,6009,6017,6025,6033,6041,6049,6057,6065,6073,6081,6090,6099,6108,6116,6125,6134,6137,6140,6141,6142,6143,6144,6145,6146,6147,6148,6149,6150,6151,6152,6155,6156,6157,6158,6159,6167,6175,6176,6184,6188,6196,6204,6212,6220,6228,6229,6237,6245,6246,6249,6252,6253,6256,6259,6336,6341,6343,6348,6352,6356,6370,6371,6372,6376,6380,6381,6385,6386,6387,6388,6389,6390,6391,6392,6393,6394,6395,6396,6397,6398,6402,6406,6407,6411,6412,6417,6418,6419,6420,6421,6422,6423,6424,6425,6426,6427,6428,6429,6430,6431,6432,6433,6434,6435,6436,6437,6441,6442,6443,6449,6450,6454,6456,6457,6460,6465,6466,6467,6468,6469,6470,6474,6475,6476,6482,6483,6487,6489,6492,6496,6500,6504,6508,6559,6560,6561,6564,6567,6570,6573,6576,6580,6581,6586,6590,6595,6599,6604,6608,6612,6645,6646,6647,6648,6652,6653,6654,6655,6659,6663,6667,6671,6677,6678,6711,6724,6729,6755,6761,6764,6775,6780,6783,6786,6789,6792,6795,6798,6801,6805,6808,6809,6810,6818,6826,6829,6834,6839,6844,6849,6853,6857,6858,6866,6867,6868,6869,6870,6878,6883,6888,6889,6890,6891,6916,6921,6926,6929,6933,6936,6940,6950,6953,6958,6961,6965,6969,6972,7143,7156,7169,7173,7188,7199,7202,7213,7218,7222,7257,7258,7259,7266,7270,7274,7278,7282,7293,7309,7323,7330,7339,7342,7362,7372,7376,7380,7391,7397,7401,7418,7426,7430,7434,7438,7441,7445,7449,7453,7460,7464,7465,7469,7490,7500,7520,7529,7545,7555,7559,7569,7589,7599,7602,7603,7604,7605,7606,7610,7614,7618,7619,7620,7621,7624,7627,7630,7633,7636,7639,7642,7645,7648,7651,7654,7657,7660,7663,7666,7669,7672,7673,7676,7687,7695,7699,7704,7709,7712,7715,7721,7725,7728,7731,7735,7740,7744,7747,7755,7759,7763,7774,7779,7784,7785,7788,7791,7792,7802,7807,7815,7818,7822,7835,7838,7842,7857,7864,7890,7893,7896,7899,7902,7912,7918,7919,7924,7925,7926,7927,7931,7935,7939,7943,7972,7975,7979,7983,8011,8014,8018,8022,8031,8037,8040,8049,8053,8054,8061,8065,8072,8073,8074,8077,8082,8087,8088,8092,8100,8115,8119,8120,8132,8142,8143,8155,8160,8184,8187,8193,8196,8205,8213,8217,8220,8223,8226,8230,8233,8250,8254,8257,8272,8275,8283,8288,8295,8300,8301,8306,8307,8313,8319,8325,8356,8367,8384,8391,8395,8398,8410,8419,8423,8428,8432,8436,8440,8444,8448,8452,8456,8459,8468,8473,8482,8485,8492,8493,8497,8506,8512,8516,8517,8521,8542,8548,8552,8556,8557,8575,8576,8577,8578,8579,8584,8587,8588,8594,8595,8607,8619,8626,8627,8632,8637,8638,8642,8656,8661,8667,8673,8679,8684,8690,8696,8697,8702,8716,8721,8730,8739,8742,8755,8758,8769,8773,8782,8791,8792,8799,8807", "endColumns": "55,48,55,59,60,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,46,55,48,57,55,50,59,48,55,55,49,58,46,55,53,53,53,48,57,55,46,53,53,47,56,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,63,70,71,50,52,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,58,61,61,66,56,55,55,57,57,56,56,58,58,57,53,55,9,52,57,57,57,45,59,53,67,68,67,52,51,49,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,53,57,50,44,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,86,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,83,93,87,97,83,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,84,94,88,98,84,100,86,96,65,75,68,78,64,65,52,75,65,86,75,75,41,46,64,54,49,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,53,52,53,58,45,56,66,55,64,73,83,72,64,61,55,82,88,63,78,73,58,55,55,59,59,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,74,56,83,56,74,74,50,67,49,61,59,56,59,48,80,56,60,58,59,57,60,57,49,48,66,58,58,48,74,70,68,62,67,65,67,64,65,76,77,63,78,88,77,65,68,65,97,95,95,97,108,55,91,48,53,53,53,53,53,54,109,109,109,109,109,109,109,109,105,105,105,105,94,94,94,94,105,105,105,105,94,94,94,94,107,107,107,107,96,96,96,96,107,107,107,107,97,95,95,97,64,87,63,61,61,67,57,62,65,62,66,71,65,51,59,57,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,52,52,67,76,64,60,59,74,73,76,72,69,71,69,72,63,69,47,68,51,84,82,57,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,62,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,55,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,54,61,52,56,93,68,100,50,69,62,57,69,68,69,69,69,66,66,74,66,58,53,53,53,52,51,73,40,64,73,72,67,59,57,60,65,65,64,63,60,136,139,48,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,54,57,54,58,55,60,62,60,60,60,60,60,60,60,58,60,60,60,60,60,60,50,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,69,46,51,49,56,12,149,130,237,97,114,84,47,78,64,88,156,156,152,153,58,39,95,89,95,89,165,124,124,169,109,120,120,109,111,122,122,81,173,147,158,154,172,116,116,167,111,113,173,177,132,111,145,151,131,142,121,177,181,316,181,181,189,189,198,156,109,182,136,219,183,159,157,183,202,170,219,221,154,199,183,102,140,164,170,199,203,201,204,200,198,203,160,77,300,165,154,101,10,10,10,10,10,10,10,10,10,10,84,120,98,86,122,100,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,90,10,10,10,10,108,118,10,10,10,10,10,94,30,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,10,10,10,10,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,10,10,10,10,10,10,10,10,10,52,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,60,10,10,10,10,10,10,10,10,75,111,89,10,10,98,10,75,111,89,101,107,107,99,84,103,86,77,113,91,10,10,100,10,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,113,119,93,10,98,117,100,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,94,10,10,10,10,10,10,10,10,10,10,10,10,123,145,137,135,10,10,10,115,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,101,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,95,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10", "endOffsets": "646,976,1611,1671,1732,1787,1986,2036,2089,2147,2195,2315,2363,2434,2506,2578,2651,2718,2767,2821,2858,2909,2956,3012,3061,3119,3175,3226,3286,3335,3391,3447,3497,3556,3603,3659,3713,3767,3821,3870,3928,3984,4031,4085,4139,4187,4244,4917,4973,5813,5923,5979,6039,6092,6153,6232,6313,6385,6464,6544,6620,6698,6767,6843,6920,6991,7064,7128,7199,7271,9672,9770,10126,10178,10228,10286,10351,10399,10515,16025,16091,16149,16218,16276,16345,16415,16488,16562,16630,16697,16767,16833,16906,16966,17042,17102,17162,17237,17305,17371,17439,17499,17558,17615,17681,17743,17800,17859,17921,17983,18050,18107,18163,18219,18277,18335,18392,18449,18508,18567,18625,18679,18735,18864,19123,19181,19239,20139,20687,21054,21394,21462,21531,21599,21652,22371,22467,22572,22622,22965,23012,23117,23175,23237,23300,23362,23421,23481,23546,23612,23677,23739,23801,23863,23925,23987,24049,24115,24182,24248,24311,24375,24438,24506,24567,24629,24691,24754,24818,24881,24945,25023,25082,25148,25228,25289,25342,25396,25560,25611,26096,26453,26561,28116,31396,31467,31533,31607,31676,31747,31820,31891,31959,32032,32108,32178,32256,32324,32390,32451,32520,32584,32650,32718,32784,32847,32915,32986,33051,33124,33187,33268,33332,33398,33468,33538,33608,33678,33745,35019,35077,35136,35196,35255,35314,35373,35432,35491,35550,35609,35668,35727,35786,35846,35907,35969,36030,36091,36152,36213,36274,36335,36396,36457,36518,36579,36647,36716,36786,36855,36924,36993,37062,37131,37200,37269,37338,37407,37476,37536,37597,37659,37720,37781,37842,37903,37964,38025,38086,38147,38208,38269,38331,38394,38458,38521,38584,38647,38710,38773,38836,38899,38962,39025,39088,39149,39211,39274,39336,39398,39460,39522,39584,39646,39708,39770,39832,39894,39951,40038,40118,40208,40303,40395,40487,40577,40660,40753,40840,40937,41028,41129,41216,41319,41408,41507,41599,41683,41777,41865,41963,42047,42147,42233,42329,42417,42498,42589,42685,42778,42871,42962,43047,43141,43230,43328,43421,43523,43611,43715,43806,43906,43999,44084,44179,44268,44367,44452,44553,44640,44737,45475,45551,45620,45699,45764,45830,45883,45959,46025,46112,46188,46264,47870,54246,54311,54747,55337,55391,55470,55548,55621,55686,55749,55815,55886,55957,56027,56089,56158,56224,56284,56351,56418,56474,56525,56578,56630,56684,56755,56818,56877,56939,56998,57071,57138,57208,57268,57331,57406,57478,57574,57645,57701,57772,57829,57886,57952,58016,58087,58144,58197,58260,58312,58370,58437,61755,61821,61880,61963,62022,62079,62146,62216,62290,62352,62421,62491,62545,62598,62652,62711,62757,62814,62881,62937,63002,63076,63160,63233,63298,63360,63416,63499,63588,63652,63731,63805,63864,63920,63976,64036,64096,64143,64203,64264,64328,64389,64449,64507,64550,64599,64651,64702,64754,64803,64852,64917,64983,65043,65104,65160,65219,65294,65351,65435,65492,65567,65642,65693,65761,65811,65873,65933,65990,66050,66099,66180,66237,66298,66357,66417,66475,66536,66594,66644,66693,66760,66819,66878,66927,67002,67073,67142,67205,67273,67339,67407,67472,67538,67615,67693,67757,67836,67925,68003,68069,68138,68204,68302,68398,68494,68592,68701,68757,68849,68898,68952,69006,69060,69114,69168,69223,69333,69443,69553,69663,69773,69883,69993,70103,70209,70315,70421,70527,70622,70717,70812,70907,71013,71119,71225,71331,71426,71521,71616,71711,71819,71927,72035,72143,72240,72337,72434,72531,72639,72747,72855,72963,73061,73157,73253,73351,73416,73504,73568,73630,73692,73760,73818,73881,73947,74010,74077,74149,74215,74267,74327,74385,74437,74494,74578,74673,74758,74839,74919,74996,75075,75152,75226,75300,75371,75451,75523,75598,75651,75704,75772,75849,75914,75975,76035,76110,76184,76261,76334,76404,76476,76546,76619,76683,76753,76801,76870,76922,77007,77090,77148,77214,77281,77347,77428,77503,77559,77612,77673,77731,77781,77830,77879,77928,77990,78042,78087,78168,78219,78273,78326,78380,78431,78480,78546,78597,78658,78719,78781,78831,78872,78949,79008,79067,79126,79187,79243,79299,79366,79427,79492,79547,79612,79681,79749,79827,79896,79956,80027,80101,80166,80238,80308,80375,80459,80528,80595,80665,80728,80795,80863,80946,81025,81115,81192,81260,81327,81405,81462,81519,81587,81653,81709,81769,81828,81882,81932,81982,82030,82092,82143,82216,82296,82376,82440,82503,82570,82641,82699,82760,82826,82885,82952,83012,83072,83135,83203,83264,83331,83409,83479,83528,83585,83654,83715,83803,83891,83979,84067,84123,84210,84297,84384,84471,84529,84603,84673,84729,84800,84865,84927,85002,85075,85165,85231,85297,85358,85422,85484,85542,85613,85696,85755,85826,85892,85957,86018,86077,86148,86214,86279,86362,86438,86513,86594,86654,86723,86793,86862,86917,86973,87029,87090,87148,87204,87259,87321,87374,87431,87525,87594,87695,87746,87816,87879,87937,88007,88076,88146,88216,88286,88353,88420,88495,88562,88621,88675,88729,88783,88836,88888,88962,90495,90560,90634,90707,90775,90835,90893,90954,91020,91086,91151,91215,91276,95706,95846,101566,101616,101811,101867,101925,101987,102042,102100,102171,102235,102294,102356,102422,102488,102900,103046,103091,103134,104120,104167,104212,104263,104314,104365,104416,104930,105548,105610,105858,105930,106211,106266,106324,106379,106438,106494,106555,106618,106679,106740,106801,106862,106923,106984,107045,107104,107165,107226,107287,107348,107409,107470,107521,107587,107653,107721,107789,107855,107922,107996,108059,108116,108176,108241,108308,108373,108430,108491,108549,108619,108666,108718,108768,108889,109846,112116,112247,112485,116237,116352,116437,116485,116564,119893,124519,124676,124833,125904,126363,126935,126975,127183,127273,127369,127459,127625,127750,127875,128045,128155,128276,128397,128507,128619,128742,128865,128947,129121,129269,129428,129583,129756,129873,129990,130158,130270,130384,130558,130736,130869,130981,131127,131279,131411,131554,131676,132248,132430,132747,132929,133111,133301,133491,133690,133847,133957,134140,134277,134497,134681,134841,134999,135183,135386,135557,135777,135999,136154,136354,136538,136641,136782,136947,137118,137318,137522,137724,137929,138130,138329,138533,138694,138888,139189,139355,139510,139612,143156,144203,144488,147889,148345,148854,159996,160433,160867,161310,165292,165413,165512,165599,165722,165823,165916,166023,166366,166473,166718,166839,167248,167496,167596,167701,167820,168329,168476,168595,168846,168979,169394,169648,169760,175335,175460,175677,175877,175998,176131,176278,190991,191427,205980,220729,221167,235756,240597,246014,250105,255536,260278,265655,269639,273631,279022,279513,280269,280499,280742,281875,330020,330484,330877,331547,332706,333703,333996,334295,334678,335453,336218,337105,337371,337847,338121,338518,339191,339369,339598,340523,340614,342808,343074,343396,343606,343715,343834,344018,344990,345460,346211,348688,348783,350965,360768,360996,361255,361831,362185,362307,362446,362720,362980,363850,364136,364539,364941,365284,365496,365697,365910,366199,366484,377356,377443,377528,377627,382614,382884,383055,383323,383488,383655,383924,384189,384357,384522,384688,384808,384928,385036,385146,385258,385366,385476,385641,385807,385986,386151,386306,386426,386587,386750,386911,387076,387243,387296,387429,387549,387647,387760,387879,387996,388151,388283,388512,388675,388868,388994,389146,389288,389458,389614,389786,390077,390189,390318,391491,391709,392564,393151,393765,393933,394075,394236,394379,394547,394704,401753,401845,402018,402180,402275,402444,402538,402627,402870,402959,403252,403717,404186,404656,405131,405597,406062,406528,406995,407458,407928,408401,408873,409333,409804,410276,410466,410645,410751,410859,410965,411077,411191,411303,411417,411533,411647,411755,411865,411973,412169,412277,412387,412495,412609,413018,413432,413548,413966,414207,414637,415072,415482,415904,416314,416436,416845,417261,417383,417601,417785,417846,418018,418190,423032,423376,423456,423812,423962,424106,424916,425028,425118,425380,425645,425744,425896,425972,426084,426174,426276,426384,426492,426592,426677,426781,426868,426946,427060,427152,427416,427683,427784,427937,428021,428410,428508,428616,428710,428840,428948,429070,429206,429314,429434,429568,429690,429818,429960,430086,430226,430352,430470,430602,430700,430810,431110,431222,431340,431804,431920,432223,432349,432445,432575,432976,433086,433210,433348,433458,433580,433892,434016,434146,434622,434750,435065,435203,435349,435511,435727,435883,436087,439411,439495,439599,439802,439991,440192,440385,440590,440795,440911,441106,441294,441506,441658,441877,442019,442180,444678,444792,444912,445006,445327,445426,445544,445645,445875,446111,446321,446554,446978,447054,449562,450817,451261,453115,453572,453780,454790,455170,455311,455446,455643,455814,455997,456172,456359,456581,456739,456823,456927,457414,457970,458128,458347,458578,458801,459036,459258,459524,459662,460261,460375,460513,460625,460749,461320,461815,462361,462506,462599,462691,464618,465061,465359,465548,465754,465947,466157,467041,467186,467578,467736,467953,468214,468345,480496,481322,481942,482139,483087,483852,483975,484748,484969,485169,487146,487246,487336,487707,488006,488317,488620,488935,489504,490255,491330,491877,492435,492601,493998,494614,494850,495071,495861,496256,496492,497761,498210,498397,498646,498888,499064,499305,499538,499763,500105,500318,500413,500674,501529,501982,502701,503149,503744,504196,504403,504860,505569,506026,506176,506300,506446,506584,506720,507008,507309,507611,507727,507849,507961,508110,508368,508630,508888,509148,509396,509648,509896,510146,510390,510638,510882,511128,511360,511596,511828,512062,512174,512365,513224,513822,514018,514255,514523,514739,514957,515229,515528,515720,515932,516224,516488,516777,516996,517521,517758,518050,518789,519027,519296,519436,519606,519750,519852,520534,520874,521496,521722,522019,522743,523007,523322,524488,525048,527143,527337,527555,527781,527993,528668,528933,529029,529376,529464,529572,529680,529974,530280,530578,530888,532967,533155,533416,533665,535642,535834,536099,536352,536868,537218,537387,537901,538136,538260,538672,538886,539288,539391,539521,539696,539948,540144,540284,540478,540976,541857,542145,542275,543052,543709,543855,544561,544799,546339,546489,546906,547071,547757,548227,548423,548514,548598,548742,548976,549143,550071,550357,550517,551132,551291,551619,551846,552358,552720,552799,553138,553243,553608,553979,554340,556162,556791,557867,558291,558544,558696,559686,560423,560626,560872,561119,561337,561579,561900,562164,562469,562692,562864,563405,563674,564168,564394,564834,564993,565277,566022,566387,566692,566850,567088,568407,568805,569033,569253,569395,570685,570791,570921,571059,571183,571471,571640,571740,572025,572139,573022,573777,574216,574340,574586,574779,574913,575104,575883,576101,576392,576671,576988,577210,577505,577788,577892,578175,578907,579223,579784,580290,580495,581224,581499,582160,582349,582900,583466,583586,583988,584522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7d870e92d279060797355c3afa632fe0\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "120,123,959", "startColumns": "4,4,4", "startOffsets": "5654,5818,55240", "endColumns": "55,47,51", "endOffsets": "5705,5861,55287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "604,605,1544,1545,1546,1579,1580,1581,1582,1583,1584,1585,1636,1645,1648,1654,1669,1673,1675,1707,1708,1715,1717,1718,1822,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1868,1967,1975,1976,1982,2018,2019,2020,2067,2069,2072,2073,2076,2077,2078,2080,2081,2083,2085,2086,2088,2091,2093,2094,2106,2110,5157,5161,5189,5193,5458,6266,6318,6319,6328,6357,6364,6367,6509,6512,6518,8823", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "30432,30495,96064,96115,96169,98138,98186,98235,98284,98332,98381,98439,100797,101241,101411,101683,102536,102743,102828,104456,104495,104839,104935,104989,111717,113001,113124,113220,113340,113460,113562,113702,113824,113934,114041,114144,114255,114424,114592,114709,114828,114941,115127,115235,115348,115439,115550,115719,115817,115942,116037,116688,125658,126368,126551,126980,131681,131782,131941,139792,140007,140213,140318,140548,140717,140834,141028,141173,141365,141504,141600,141867,142133,142322,142506,143435,143683,349064,349332,350419,350621,366489,418623,421978,422013,422551,424111,424489,424666,436092,436275,436640,585292", "endLines": "604,605,1544,1545,1546,1579,1580,1581,1582,1583,1584,1585,1636,1645,1648,1654,1669,1673,1675,1707,1708,1715,1717,1718,1822,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1868,1967,1975,1976,1982,2018,2019,2020,2067,2069,2072,2073,2076,2077,2078,2080,2081,2083,2085,2086,2088,2091,2093,2094,2109,2113,5160,5163,5192,5196,5461,6266,6318,6327,6334,6363,6366,6369,6511,6517,6520,8832", "endColumns": "62,62,50,53,58,47,48,48,47,48,57,48,35,49,40,43,43,42,33,38,45,47,53,47,116,122,95,119,119,101,139,121,109,106,102,110,168,167,116,118,112,185,107,112,90,110,168,97,124,94,106,169,97,182,172,111,100,158,133,139,101,104,130,168,116,147,144,149,98,95,195,182,98,183,166,10,10,12,12,10,10,10,25,34,10,10,10,10,10,12,12,12,10", "endOffsets": "30490,30553,96110,96164,96223,98181,98230,98279,98327,98376,98434,98483,100828,101286,101447,101722,102575,102781,102857,104490,104536,104882,104984,105032,111829,113119,113215,113335,113455,113557,113697,113819,113929,114036,114139,114250,114419,114587,114704,114823,114936,115122,115230,115343,115434,115545,115714,115812,115937,116032,116139,116853,125751,126546,126719,127087,131777,131936,132070,139927,140104,140313,140444,140712,140829,140977,141168,141318,141459,141595,141791,142045,142227,142501,142668,143678,143926,349327,349512,350616,350822,366681,418644,422008,422546,422964,424484,424661,424840,436270,436635,436832,585728"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1619f5c30df2cf616e20d2bf30ab0293\\transformed\\constraintlayout-2.0.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "13,16,29,30,38,45,51,88,89,90,93,97,99,100,103,111,121,144,145,150,151,156,161,162,163,168,169,174,175,180,181,182,188,189,190,195,200,219,220,224,225,226,227,230,231,234,237,238,239,240,241,244,247,248,249,250,255,258,261,262,263,268,269,270,273,276,277,280,283,286,289,290,291,294,297,298,303,304,309,312,315,316,317,318,319,320,321,322,323,324,384,385,386,387,392,398,399,400,403,461,475,517,518,529,535,541,545,546,547,548,557,1655", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "484,651,1156,1217,1508,1893,2200,4249,4301,4362,4528,4661,4767,4817,4978,5287,5710,7276,7335,7532,7589,7784,7966,8020,8077,8269,8327,8523,8579,8773,8830,8881,9103,9155,9210,9400,9576,10520,10576,10736,10797,10857,10927,11060,11128,11257,11383,11445,11510,11578,11645,11768,11893,11960,12025,12090,12271,12392,12513,12579,12646,12856,12925,12991,13116,13242,13309,13435,13562,13687,13814,13870,13935,14061,14184,14249,14457,14524,14704,14824,14944,15009,15071,15133,15195,15254,15314,15375,15436,15495,18869,18920,18969,19017,19304,19534,19581,19641,19747,22376,23017,25616,25668,26101,26339,26665,26804,26850,26905,26950,27291,101727", "endLines": "13,20,29,37,38,45,51,88,89,90,96,97,99,100,110,118,121,144,149,150,155,160,161,162,167,168,173,174,179,180,181,187,188,189,194,199,200,219,223,224,225,226,229,230,233,236,237,238,239,240,243,246,247,248,249,254,257,260,261,262,267,268,269,272,275,276,279,282,285,288,289,290,293,296,297,302,303,308,311,314,315,316,317,318,319,320,321,322,323,335,384,385,386,387,397,398,399,402,407,461,475,517,526,534,535,544,545,546,547,556,560,1655", "endColumns": "56,11,60,11,51,47,50,51,60,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,55,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11,50,48,47,57,11,46,59,11,11,45,46,51,11,11,54,11,45,54,44,11,11,40", "endOffsets": "536,832,1212,1503,1555,1936,2246,4296,4357,4403,4656,4708,4812,4863,5282,5594,5750,7330,7527,7584,7779,7961,8015,8072,8264,8322,8518,8574,8768,8825,8876,9098,9150,9205,9395,9571,9621,10571,10731,10792,10852,10922,11055,11123,11252,11378,11440,11505,11573,11640,11763,11888,11955,12020,12085,12266,12387,12508,12574,12641,12851,12920,12986,13111,13237,13304,13430,13557,13682,13809,13865,13930,14056,14179,14244,14452,14519,14699,14819,14939,15004,15066,15128,15190,15249,15309,15370,15431,15490,15958,18915,18964,19012,19070,19529,19576,19636,19742,19922,22417,23059,25663,25993,26334,26389,26799,26845,26900,26945,27286,27423,101763"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2d10be2ca6f02e3c7fd72b76301c06b9\\transformed\\glide-4.16.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1640", "startColumns": "4", "startOffsets": "100977", "endColumns": "57", "endOffsets": "101030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7684a13bf28674155e26ec50b0113afc\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1646,1670", "startColumns": "4,4", "startOffsets": "101291,102580", "endColumns": "53,66", "endOffsets": "101340,102642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b3921f3c0ec9fec3b31ebed685fc140\\transformed\\recyclerview-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "413,1046,1047,1048,1056,1057,1058,1647", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "20204,60694,60753,60801,61468,61543,61619,101345", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "20255,60748,60796,60852,61538,61614,61686,101406"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\expo-av\\android\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "1892,1895,1897", "startColumns": "4,4,4", "startOffsets": "119538,119761,119898", "endColumns": "63,71,69", "endOffsets": "119597,119828,119963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b84b30b80c5949bd0f3610f30992b047\\transformed\\play-services-base-18.3.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "608,609,610,611,612,613,614,615,1869,1870,1871,1872,1873,1874,1875,1876,1878,1879,1880,1881,1882,1883,1884,1885,1886", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "30656,30746,30826,30916,31006,31086,31167,31247,116858,116963,117144,117269,117376,117556,117679,117795,118065,118253,118358,118539,118664,118839,118987,119050,119112", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "30741,30821,30911,31001,31081,31162,31242,31322,116958,117139,117264,117371,117551,117674,117790,117893,118248,118353,118534,118659,118834,118982,119045,119107,119186"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1,-1,-1,-1", "startColumns": "2,-1,-1,-1", "startOffsets": "14,-1,-1,-1", "endColumns": "51,-1,-1,-1", "endOffsets": "63,-1,-1,-1"}, "to": {"startLines": "1824,1952,1953,1954", "startColumns": "4,4,4,4", "startOffsets": "111917,124155,124243,124340", "endColumns": "53,87,96,94", "endOffsets": "111966,124238,124335,124430"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "2122,5626,6267", "startColumns": "4,4,4", "startOffsets": "144493,378947,418649", "endLines": "2128,5630,6271", "endColumns": "10,10,10", "endOffsets": "144895,379187,418954"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\expo-notifications\\android\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1951", "startColumns": "4", "startOffsets": "124072", "endColumns": "82", "endOffsets": "124150"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b825b073e97da6f5b520ec2775c042b7\\transformed\\fragment-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "1637,1682,1723", "startColumns": "4,4,4", "startOffsets": "100833,103139,105244", "endColumns": "56,64,63", "endOffsets": "100885,103199,105303"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4d414adee14b24510c264c21c70961f0\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "119,202,514,565,566,573,574,575,576,577,578,579,582,583,584,585,586,587,588,589,590,591,596,597,651,652,653,654,655,656,665,666,667,668,802,803,804,805,806,807,808,809,810,811,812,813,829,830,831,832,833,834,835,836,837,838,839,840,841,842,844,845,846,847,849,850,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,1009,1010,1049,1050,1051,1052,1053,1054,1055,1487,1488,1489,1490,1491,1492,1493,1494,1586,1587,1588,1589,1643,1671,1672,1683,1714,1725,1726,1729,1730,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,2074,2098,2099,2103,2104,2105,2129,2137,2138,2142,2146,2157,2162,2191,2198,2202,2206,2211,2215,2219,2223,2227,2231,2235,2241,2245,2251,2255,2261,2265,2270,2274,2277,2281,2287,2291,2297,2301,2307,2310,2314,2318,2322,2326,2330,2331,2332,2333,2336,2339,2342,2345,2349,2350,2351,2352,2390,2393,2395,2397,2399,2404,2405,2409,2415,2419,2420,2422,2434,2435,2439,2445,2449,2513,2514,2518,2545,2549,2550,2554,4063,4235,4261,4432,4458,4489,4497,4503,4519,4541,4546,4551,4561,4570,4579,4583,4590,4609,4616,4617,4626,4629,4632,4636,4640,4644,4647,4648,4653,4658,4668,4673,4680,4686,4687,4690,4694,4699,4701,4703,4706,4709,4711,4715,4718,4725,4728,4731,4735,4737,4741,4743,4745,4747,4751,4759,4767,4779,4785,4794,4797,4808,4811,4812,4817,4818,5462,5531,5605,5606,5616,5625,5631,5633,5637,5640,5643,5646,5649,5652,5655,5658,5662,5665,5668,5671,5675,5678,5682,5912,5913,5914,5915,5916,5917,5918,5919,5920,5921,5922,5923,5924,5925,5926,5927,5928,5929,5930,5931,5932,5934,5936,5937,5938,5939,5940,5941,5942,5943,5945,5946,5948,5949,5951,5953,5954,5956,5957,5958,5959,5960,5961,5963,5964,5965,5966,5967,6260,6262,6264,6272,6273,6274,6275,6276,6277,6278,6279,6280,6281,6282,6283,6284,6286,6287,6288,6289,6290,6291,6292,6294,6298,6547,6548,6549,6550,6551,6552,6556,6557,6558,7005,7007,7009,7011,7013,7015,7016,7017,7018,7020,7022,7024,7025,7026,7027,7028,7029,7030,7031,7032,7033,7034,7035,7038,7039,7040,7041,7043,7045,7046,7048,7049,7051,7053,7055,7056,7057,7058,7059,7060,7061,7062,7063,7064,7065,7066,7068,7069,7070,7071,7073,7074,7075,7076,7077,7079,7081,7083,7085,7086,7087,7088,7089,7090,7091,7092,7093,7094,7095,7096,7097,7098,7099", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5599,9677,25466,27704,27759,28182,28246,28316,28377,28452,28528,28605,28843,28928,29010,29086,29162,29239,29317,29423,29529,29608,29937,29994,33750,33824,33899,33964,34030,34090,34638,34710,34783,34850,44742,44801,44860,44919,44978,45037,45091,45145,45198,45252,45306,45360,46490,46564,46643,46716,46790,46861,46933,47005,47078,47135,47193,47266,47340,47414,47547,47619,47692,47762,47875,47935,48755,48824,48893,48963,49037,49113,49177,49254,49330,49407,49472,49541,49618,49693,49762,49830,49907,49973,50034,50131,50196,50265,50364,50435,50494,50552,50609,50668,50732,50803,50875,50947,51019,51091,51158,51226,51294,51353,51416,51480,51570,51661,51721,51787,51854,51920,51990,52054,52107,52174,52235,52302,52415,52473,52536,52601,52666,52741,52814,52886,52930,52977,53023,53072,53133,53194,53255,53317,53381,53445,53509,53574,53637,53697,53758,53824,53883,53943,54005,54076,54136,58442,58528,60857,60947,61034,61122,61204,61287,61377,91281,91333,91391,91436,91502,91566,91623,91680,98488,98545,98593,98642,101152,102647,102694,103204,104807,105361,105425,105615,105675,109851,109925,109995,110073,110127,110197,110282,110330,110376,110437,110500,110566,110630,110701,110764,110829,110893,110954,111015,111067,111140,111214,111283,111358,111432,111506,111647,140449,142859,142937,143161,143249,143345,144900,145482,145571,145818,146099,146765,147050,148859,149336,149558,149780,150056,150283,150513,150743,150973,151203,151430,151849,152075,152500,152730,153158,153377,153660,153868,153999,154226,154652,154877,155304,155525,155950,156070,156346,156647,156971,157262,157576,157713,157844,157949,158191,158358,158562,158770,159041,159153,159265,159370,161315,161529,161675,161815,161901,162249,162337,162583,163001,163250,163332,163430,164087,164187,164439,164863,165118,170031,170120,170357,172381,172623,172725,172978,281880,292561,294077,304772,306300,308057,308683,309103,310364,311629,311885,312121,312668,313162,313767,313965,314545,315913,316288,316406,316944,317101,317297,317570,317826,317996,318137,318201,318566,318933,319609,319873,320211,320564,320658,320844,321150,321412,321537,321664,321903,322114,322233,322426,322603,323058,323239,323361,323620,323733,323920,324022,324129,324258,324533,325041,325537,326414,326708,327278,327427,328159,328331,328415,328751,328843,366686,371917,377632,377694,378272,378856,379192,379305,379534,379694,379846,380017,380183,380352,380519,380682,380925,381095,381268,381439,381713,381912,382117,394709,394793,394889,394985,395083,395183,395285,395387,395489,395591,395693,395793,395889,396001,396130,396253,396384,396515,396613,396727,396821,396961,397095,397191,397303,397403,397519,397615,397727,397827,397967,398103,398267,398397,398555,398705,398846,398990,399125,399237,399387,399515,399643,399779,399911,400041,400171,400283,418195,418341,418485,418959,419025,419115,419191,419295,419385,419487,419595,419703,419803,419883,419975,420073,420183,420235,420313,420419,420511,420615,420725,420847,421010,438345,438425,438525,438615,438725,438815,439056,439150,439256,470388,470488,470600,470714,470830,470946,471040,471154,471266,471368,471488,471610,471692,471796,471916,472042,472140,472234,472322,472434,472550,472672,472784,472959,473075,473161,473253,473365,473489,473556,473682,473750,473878,474022,474150,474219,474314,474429,474542,474641,474750,474861,474972,475073,475178,475278,475408,475499,475622,475716,475828,475914,476018,476114,476202,476320,476424,476528,476654,476742,476850,476950,477040,477150,477234,477336,477420,477474,477538,477644,477730,477840,477924", "endLines": "119,202,514,565,566,573,574,575,576,577,578,579,582,583,584,585,586,587,588,589,590,591,596,597,651,652,653,654,655,656,665,666,667,668,802,803,804,805,806,807,808,809,810,811,812,813,829,830,831,832,833,834,835,836,837,838,839,840,841,842,844,845,846,847,849,850,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,1009,1010,1049,1050,1051,1052,1053,1054,1055,1487,1488,1489,1490,1491,1492,1493,1494,1586,1587,1588,1589,1643,1671,1672,1683,1714,1725,1726,1729,1730,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,2074,2098,2099,2103,2104,2105,2136,2137,2141,2145,2149,2161,2167,2197,2201,2205,2210,2214,2218,2222,2226,2230,2234,2240,2244,2250,2254,2260,2264,2269,2273,2276,2280,2286,2290,2296,2300,2306,2309,2313,2317,2321,2325,2329,2330,2331,2332,2335,2338,2341,2344,2348,2349,2350,2351,2352,2392,2394,2396,2398,2403,2404,2408,2414,2418,2419,2421,2433,2434,2438,2444,2448,2449,2513,2517,2544,2548,2549,2553,2581,4234,4260,4431,4457,4488,4496,4502,4518,4540,4545,4550,4560,4569,4578,4582,4589,4608,4615,4616,4625,4628,4631,4635,4639,4643,4646,4647,4652,4657,4667,4672,4679,4685,4686,4689,4693,4698,4700,4702,4705,4708,4710,4714,4717,4724,4727,4730,4734,4736,4740,4742,4744,4746,4750,4758,4766,4778,4784,4793,4796,4807,4810,4811,4816,4817,4822,5530,5600,5605,5615,5624,5625,5632,5636,5639,5642,5645,5648,5651,5654,5657,5661,5664,5667,5670,5674,5677,5681,5685,5912,5913,5914,5915,5916,5917,5918,5919,5920,5921,5922,5923,5924,5925,5926,5927,5928,5929,5930,5931,5933,5935,5936,5937,5938,5939,5940,5941,5942,5944,5945,5947,5948,5950,5952,5953,5955,5956,5957,5958,5959,5960,5962,5963,5964,5965,5966,5967,6261,6263,6265,6272,6273,6274,6275,6276,6277,6278,6279,6280,6281,6282,6283,6285,6286,6287,6288,6289,6290,6291,6293,6297,6301,6547,6548,6549,6550,6551,6555,6556,6557,6558,7006,7008,7010,7012,7014,7015,7016,7017,7019,7021,7023,7024,7025,7026,7027,7028,7029,7030,7031,7032,7033,7034,7037,7038,7039,7040,7042,7044,7045,7047,7048,7050,7052,7054,7055,7056,7057,7058,7059,7060,7061,7062,7063,7064,7065,7067,7068,7069,7070,7072,7073,7074,7075,7076,7078,7080,7082,7084,7085,7086,7087,7088,7089,7090,7091,7092,7093,7094,7095,7096,7097,7098,7099", "endColumns": "54,44,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "5649,9717,25502,27754,27816,28241,28311,28372,28447,28523,28600,28678,28923,29005,29081,29157,29234,29312,29418,29524,29603,29683,29989,30047,33819,33894,33959,34025,34085,34146,34705,34778,34845,34913,44796,44855,44914,44973,45032,45086,45140,45193,45247,45301,45355,45409,46559,46638,46711,46785,46856,46928,47000,47073,47130,47188,47261,47335,47409,47484,47614,47687,47757,47828,47930,47991,48819,48888,48958,49032,49108,49172,49249,49325,49402,49467,49536,49613,49688,49757,49825,49902,49968,50029,50126,50191,50260,50359,50430,50489,50547,50604,50663,50727,50798,50870,50942,51014,51086,51153,51221,51289,51348,51411,51475,51565,51656,51716,51782,51849,51915,51985,52049,52102,52169,52230,52297,52410,52468,52531,52596,52661,52736,52809,52881,52925,52972,53018,53067,53128,53189,53250,53312,53376,53440,53504,53569,53632,53692,53753,53819,53878,53938,54000,54071,54131,54199,58523,58610,60942,61029,61117,61199,61282,61372,61463,91328,91386,91431,91497,91561,91618,91675,91729,98540,98588,98637,98688,101181,102689,102738,103245,104834,105420,105482,105670,105727,109920,109990,110068,110122,110192,110277,110325,110371,110432,110495,110561,110625,110696,110759,110824,110888,110949,111010,111062,111135,111209,111278,111353,111427,111501,111642,111712,140497,142932,143022,143244,143340,143430,145477,145566,145813,146094,146346,147045,147438,149331,149553,149775,150051,150278,150508,150738,150968,151198,151425,151844,152070,152495,152725,153153,153372,153655,153863,153994,154221,154647,154872,155299,155520,155945,156065,156341,156642,156966,157257,157571,157708,157839,157944,158186,158353,158557,158765,159036,159148,159260,159365,159482,161524,161670,161810,161896,162244,162332,162578,162996,163245,163327,163425,164082,164182,164434,164858,165113,165207,170115,170352,172376,172618,172720,172973,175129,292556,294072,304767,306295,308052,308678,309098,310359,311624,311880,312116,312663,313157,313762,313960,314540,315908,316283,316401,316939,317096,317292,317565,317821,317991,318132,318196,318561,318928,319604,319868,320206,320559,320653,320839,321145,321407,321532,321659,321898,322109,322228,322421,322598,323053,323234,323356,323615,323728,323915,324017,324124,324253,324528,325036,325532,326409,326703,327273,327422,328154,328326,328410,328746,328838,329116,371912,377283,377689,378267,378851,378942,379300,379529,379689,379841,380012,380178,380347,380514,380677,380920,381090,381263,381434,381708,381907,382112,382442,394788,394884,394980,395078,395178,395280,395382,395484,395586,395688,395788,395884,395996,396125,396248,396379,396510,396608,396722,396816,396956,397090,397186,397298,397398,397514,397610,397722,397822,397962,398098,398262,398392,398550,398700,398841,398985,399120,399232,399382,399510,399638,399774,399906,400036,400166,400278,400418,418336,418480,418618,419020,419110,419186,419290,419380,419482,419590,419698,419798,419878,419970,420068,420178,420230,420308,420414,420506,420610,420720,420842,421005,421162,438420,438520,438610,438720,438810,439051,439145,439251,439343,470483,470595,470709,470825,470941,471035,471149,471261,471363,471483,471605,471687,471791,471911,472037,472135,472229,472317,472429,472545,472667,472779,472954,473070,473156,473248,473360,473484,473551,473677,473745,473873,474017,474145,474214,474309,474424,474537,474636,474745,474856,474967,475068,475173,475273,475403,475494,475617,475711,475823,475909,476013,476109,476197,476315,476419,476523,476649,476737,476845,476945,477035,477145,477229,477331,477415,477469,477533,477639,477725,477835,477919,478039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\222c8bd50fbb99823117f37113651644\\transformed\\standard-core-1.6.49\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "570,5172", "startColumns": "4,4", "startOffsets": "28020,349835", "endLines": "570,5188", "endColumns": "38,12", "endOffsets": "28054,350414"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\448e2a47c2b2ada0bf8cd80bd6ec7e39\\transformed\\media-1.4.3\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "828,1653,5973,5975,5976,5981,5983", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "46401,101621,400741,400917,401039,401301,401496", "endColumns": "88,61,65,121,60,65,66", "endOffsets": "46485,101678,400802,401034,401095,401362,401558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eeb7a35c1d2dda21edaace2253c1f099\\transformed\\exoplayer-ui-2.18.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,11,12,14,21,22,24,28,43,91,98,204,205,217,408,409,410,414,419,436,437,438,439,440,446,451,452,453,454,455,456,457,458,459,466,513,527,537,539,540,657,658,659,660,661,662,663,664,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1734,1735,1783,1787,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,5201,5206,5210,5214,5218,5222,5226,5230,5234,5235,5241,5252,5256,5260,5264,5268,5272,5276,5280,5284,5288,5292,5296,5307,5312,5317,5322,5333,5341,5351,5355,5359", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,541,837,883,981,1107,1792,4408,4713,9775,9827,10404,19927,19984,20031,20260,20408,21059,21108,21169,21229,21285,21657,21827,21887,21940,21997,22052,22108,22165,22214,22265,22627,25401,25998,26458,26566,26614,34151,34208,34265,34327,34394,34465,34537,34581,58615,58671,58734,58807,58877,58936,58993,59040,59095,59140,59189,59244,59298,59348,59399,59453,59512,59562,59620,59676,59729,59792,59857,59920,59972,60032,60096,60162,60220,60292,60353,60423,60493,60558,60623,91734,91822,91920,92016,92090,92166,92240,92322,92408,92494,92580,92658,92746,92832,92902,92994,93072,93152,93230,93316,93398,93491,93569,93660,93741,93830,93933,94034,94118,94214,94311,94406,94499,94591,94684,94777,94870,94953,95040,95135,95228,95309,95404,95497,98918,98962,99003,99048,99096,99140,99183,99232,99279,99323,99379,99432,99474,99521,99569,99629,99667,99717,99761,99811,99863,99901,99948,99995,100036,100075,100113,100157,100205,100247,100285,100327,100381,100428,100465,100514,100556,100597,100638,100680,100723,100761,105935,106013,108964,109261,119968,120050,120132,120274,120352,120439,120524,120591,120654,120746,120838,120903,120966,121028,121099,121209,121320,121430,121497,121577,121648,121715,121800,121885,121948,122036,122746,122888,122988,123036,123179,123242,123304,123369,123440,123498,123556,123622,123686,123752,123804,123866,123942,124018,350970,351249,351473,351676,351882,352085,352300,352509,352706,352744,353098,353885,354126,354366,354623,354876,355129,355364,355611,355850,356094,356315,356510,357085,357376,357672,357975,358544,359078,359552,359763,359963", "endLines": "10,11,12,14,21,22,27,28,43,91,98,204,205,217,408,409,410,418,425,436,437,438,439,440,450,451,452,453,454,455,456,457,458,459,472,513,527,537,539,540,657,658,659,660,661,662,663,664,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1734,1735,1786,1790,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,5205,5209,5213,5217,5221,5225,5229,5233,5234,5240,5251,5255,5259,5263,5267,5271,5275,5279,5283,5287,5291,5295,5306,5311,5316,5321,5332,5340,5350,5354,5358,5362", "endColumns": "17,49,53,53,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,87,97,95,73,75,73,81,85,85,85,77,87,85,69,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10", "endOffsets": "375,425,479,590,878,927,1102,1151,1836,4462,4762,9822,9872,10464,19979,20026,20081,20403,20641,21103,21164,21224,21280,21340,21822,21882,21935,21992,22047,22103,22160,22209,22260,22319,22909,25461,26051,26502,26609,26660,34203,34260,34322,34389,34460,34532,34576,34633,58666,58729,58802,58872,58931,58988,59035,59090,59135,59184,59239,59293,59343,59394,59448,59507,59557,59615,59671,59724,59787,59852,59915,59967,60027,60091,60157,60215,60287,60348,60418,60488,60553,60618,60689,91817,91915,92011,92085,92161,92235,92317,92403,92489,92575,92653,92741,92827,92897,92989,93067,93147,93225,93311,93393,93486,93564,93655,93736,93825,93928,94029,94113,94209,94306,94401,94494,94586,94679,94772,94865,94948,95035,95130,95223,95304,95399,95492,95569,98957,98998,99043,99091,99135,99178,99227,99274,99318,99374,99427,99469,99516,99564,99624,99662,99712,99756,99806,99858,99896,99943,99990,100031,100070,100108,100152,100200,100242,100280,100322,100376,100423,100460,100509,100551,100592,100633,100675,100718,100756,100792,106008,106086,109256,109526,120045,120127,120269,120347,120434,120519,120586,120649,120741,120833,120898,120961,121023,121094,121204,121315,121425,121492,121572,121643,121710,121795,121880,121943,122031,122095,122883,122983,123031,123174,123237,123299,123364,123435,123493,123551,123617,123681,123747,123799,123861,123937,124013,124067,351244,351468,351671,351877,352080,352295,352504,352701,352739,353093,353880,354121,354361,354618,354871,355124,355359,355606,355845,356089,356310,356505,357080,357371,357667,357970,358539,359073,359547,359758,359958,360134"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ae58c5a64fce4f723365c9edcca52b81\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "44,600,601,602,603,948,949,950,2150,5164,5166,5169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1841,30183,30244,30306,30368,54527,54586,54643,146351,349517,349581,349707", "endLines": "44,600,601,602,603,948,949,950,2156,5165,5168,5171", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "1888,30239,30301,30363,30427,54581,54638,54692,146760,349576,349702,349830"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c057469839ed01cf70753c0e493d65de\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1719", "startColumns": "4", "startOffsets": "105037", "endColumns": "42", "endOffsets": "105075"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\generated\\res\\processReleaseGoogleServices\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1962,1963,1964,1965,1966,2068", "startColumns": "4,4,4,4,4,4", "startOffsets": "125135,125218,125322,125432,125552,139932", "endColumns": "82,103,109,119,105,74", "endOffsets": "125213,125317,125427,125547,125653,140002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\01f5868e1a3bbc746a0df16a210183c2\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1721", "startColumns": "4", "startOffsets": "105140", "endColumns": "53", "endOffsets": "105189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e50a7041aff24bf45b5302e9c4f92086\\transformed\\activity-1.7.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1674,1720", "startColumns": "4,4", "startOffsets": "102786,105080", "endColumns": "41,59", "endOffsets": "102823,105135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\05887ea86031039b7ecbf23129389746\\transformed\\firebase-messaging-24.0.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1961", "startColumns": "4", "startOffsets": "125053", "endColumns": "81", "endOffsets": "125130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fc222b694c9ca04a566560b361d90b0\\transformed\\core-splashscreen-1.2.0-alpha02\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "210,412,463,561,562,563,564,1467,1468,1469,1470,1471,1472,1473,1731,2510,2511,2512,5152,5154,6521,6530,6543", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10029,20144,22472,27428,27497,27569,27632,89945,90019,90095,90171,90248,90319,90388,105732,169765,169846,169938,348788,348897,436837,437297,438072", "endLines": "210,412,463,561,562,563,564,1467,1468,1469,1470,1471,1472,1473,1731,2510,2511,2512,5153,5156,6529,6542,6546", "endColumns": "48,59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "10073,20199,22526,27492,27564,27627,27699,90014,90090,90166,90243,90314,90383,90454,105795,169841,169933,170026,348892,349059,437292,438067,438340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8ee05b4940e071ad5d1d7c7e68ff5e05\\transformed\\autofill-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "945,6302,7100,7101,7108,7113,7118,7125", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "54316,421167,478044,478104,478486,478766,479048,479432", "endLines": "945,6317,7100,7107,7112,7117,7124,7133", "endColumns": "67,12,59,12,12,12,12,12", "endOffsets": "54379,421973,478099,478481,478761,479043,479427,479925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e9bc6c234e70326f0babefb2238fc4f\\transformed\\android-maps-utils-3.8.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "1590,8811,8815,8818", "startColumns": "4,4,4,4", "startOffsets": "98693,584672,584880,585040", "endLines": "1590,8814,8817,8822", "endColumns": "37,12,12,12", "endOffsets": "98726,584875,585035,585287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9208c5cce24d430e4aba003c6876b859\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1722", "startColumns": "4", "startOffsets": "105194", "endColumns": "49", "endOffsets": "105239"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "5847,5848,5851,5854,5855,5858", "startColumns": "4,4,4,4,4,4", "startOffsets": "390323,390404,390612,390795,390876,391084", "endLines": "5847,5850,5853,5854,5857,5860", "endColumns": "80,12,12,80,12,12", "endOffsets": "390399,390607,390790,390871,391079,391262"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "606,607,669,843", "startColumns": "4,4,4,4", "startOffsets": "30558,30605,34918,47489", "endColumns": "46,50,48,57", "endOffsets": "30600,30651,34962,47542"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e9aa3803274a6cf3eaa3a68ef58f1ff5\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1591,1592,1593,1642,1644,1724,1866,1867,1891,1893,1894,1977,1978,2057,2058,2070,2071,2075,2079,2082,2084,2089,2090,2092,5197,5363,5366", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "96228,96287,96346,96406,96466,96526,96586,96646,96706,96766,96826,96886,96946,97005,97065,97125,97185,97245,97305,97365,97425,97485,97545,97605,97664,97724,97784,97843,97902,97961,98020,98079,98731,98805,98863,101101,101186,105308,116569,116634,119472,119602,119703,126724,126776,138699,138761,140109,140159,140502,140982,141323,141464,142050,142097,142232,350827,360139,360250", "endLines": "1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1591,1592,1593,1642,1644,1724,1866,1867,1891,1893,1894,1977,1978,2057,2058,2070,2071,2075,2079,2082,2084,2089,2090,2092,5199,5365,5369", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "96282,96341,96401,96461,96521,96581,96641,96701,96761,96821,96881,96941,97000,97060,97120,97180,97240,97300,97360,97420,97480,97540,97600,97659,97719,97779,97838,97897,97956,98015,98074,98133,98800,98858,98913,101147,101236,105356,116629,116683,119533,119698,119756,126771,126831,138756,138810,140154,140208,140543,141023,141360,141499,142092,142128,142317,350934,360245,360440"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6591935515ee6cd5ec7881ab1d3c64e\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "391,580,581,598,599,826,827,952,953,954,955,956,957,958,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1541,1542,1543,1649,1650,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1706,1782,1828,1829,1830,1831,1832,1833,1834,2087,5968,5969,5974,5977,5982,7134,7135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19244,28683,28755,30052,30117,46269,46338,54752,54822,54890,54962,55032,55093,55167,88967,89028,89089,89151,89215,89277,89338,89406,89506,89566,89632,89705,89774,89831,89883,95851,95923,95999,101452,101487,103250,103305,103368,103423,103481,103539,103600,103663,103720,103771,103821,103882,103939,104005,104039,104421,108894,112490,112557,112629,112698,112767,112841,112913,141796,400423,400540,400807,401100,401367,479930,480002", "endLines": "391,580,581,598,599,826,827,952,953,954,955,956,957,958,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1541,1542,1543,1649,1650,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1706,1782,1828,1829,1830,1831,1832,1833,1834,2087,5968,5972,5974,5980,5982,7134,7135", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "19299,28750,28838,30112,30178,46333,46396,54817,54885,54957,55027,55088,55162,55235,89023,89084,89146,89210,89272,89333,89401,89501,89561,89627,89700,89769,89826,89878,89940,95918,95994,96059,101482,101517,103300,103363,103418,103476,103534,103595,103658,103715,103766,103816,103877,103934,104000,104034,104069,104451,108959,112552,112624,112693,112762,112836,112908,112996,141862,400535,400736,400912,401296,401491,479997,480064"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\generated\\res\\resValues\\release\\values\\gradleResValues.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1780", "startColumns": "4", "startOffsets": "108773", "endColumns": "63", "endOffsets": "108832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f0ad3059e848ec2624bad4e9e6fc7d3\\transformed\\browser-1.6.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "592,593,594,595,946,947,1887,1958,1959,1960", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "29688,29746,29812,29875,54384,54455,119191,124838,124905,124984", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "29741,29807,29870,29932,54450,54522,119254,124900,124979,125048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7460a359c4c281b49833222722dfee74\\transformed\\play-services-wallet-18.1.3\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "851,852,853,854,855,856,857,858,859,860,861,2097,6973,6981,6984,6992", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "47996,48077,48148,48220,48297,48363,48433,48504,48571,48639,48698,142784,468350,468827,468999,469476", "endLines": "851,852,853,854,855,856,857,858,859,860,861,2097,6980,6983,6991,7004", "endColumns": "80,70,71,76,65,69,70,66,67,58,56,74,8,8,8,8", "endOffsets": "48072,48143,48215,48292,48358,48428,48499,48566,48634,48693,48750,142854,468822,468994,469471,470383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ac31c4583d570f588270ebf42a86bc82\\transformed\\transition-1.2.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1638,1639,1668,1677,1678,1709,1710,1711,1712,1713", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "100890,100930,102493,102905,102960,104541,104595,104647,104696,104757", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "100925,100972,102531,102955,103002,104590,104642,104691,104752,104802"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b14b43fc1759186e6ca5ed92b9460e08\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2095,2096", "startColumns": "4,4", "startOffsets": "142673,142729", "endColumns": "55,54", "endOffsets": "142724,142779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32b9987bc127d003de604ba580089c06\\transformed\\coordinatorlayout-1.2.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "92,8808", "startColumns": "4,4", "startOffsets": "4467,584527", "endLines": "92,8810", "endColumns": "60,12", "endOffsets": "4523,584667"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\00636ad238812e00d92433007e026a91\\transformed\\exoplayer-core-2.18.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1924,1925,1926,1927,1928,1929,1930,1931,1932", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "122100,122170,122232,122297,122361,122438,122503,122593,122677", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "122165,122227,122292,122356,122433,122498,122588,122672,122741"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\expo-media-library\\android\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1979", "startColumns": "4", "startOffsets": "126836", "endColumns": "44", "endOffsets": "126876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2d12819c5c8831c86b34929884541b49\\transformed\\play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1736,1877", "startColumns": "4,4", "startOffsets": "106091,117898", "endColumns": "67,166", "endOffsets": "106154,118060"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.mohandhass2.myapp-mergeReleaseResources-83:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9c569f151f8eecc9b25adff1be1284e4\\transformed\\Android-Image-Cropper-4.3.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,214,268,312,382,448,519,573,626,693,748", "endLines": "2,3,4,5,6,7,8,9,10,11,12,71", "endColumns": "111,46,53,43,69,65,70,53,52,66,54,24", "endOffsets": "162,209,263,307,377,443,514,568,621,688,743,3836"}, "to": {"startLines": "1888,1889,1890,1969,1970,1971,1972,1973,2064,2065,2066,10212", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "119249,119361,119408,125899,125943,126013,126079,126150,139607,139660,139727,647056", "endLines": "1888,1889,1890,1969,1970,1971,1972,1973,2064,2065,2066,10270", "endColumns": "111,46,53,43,69,65,70,53,52,66,54,24", "endOffsets": "119356,119403,119457,125938,126008,126074,126145,126199,139655,139722,139777,650144"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6063bb0af1d30b1b1d98ea038715db0d\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "1641", "startColumns": "4", "startOffsets": "101035", "endColumns": "65", "endOffsets": "101096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ea79fc2a98096845c57bc3721521abee\\transformed\\work-runtime-2.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "567,568,569,572", "startColumns": "4,4,4,4", "startOffsets": "27821,27886,27956,28121", "endColumns": "64,69,63,60", "endOffsets": "27881,27951,28015,28177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a39c8a492fe916611935c0d3835604e6\\transformed\\material-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1002,1006,1010,1018,1025,1033,1043,1052,1061,1070,1071,1072,1073,1074,1075,1076,1077,1078,1083,1084,1088,1089,1095,1099,1100,1101,1102,1112,1113,1114,1118,1119,1125,1129,1130,1133,1134,1137,1140,1141,1142,1143,1343,1349,1547,1747,1753,1951,2014,2096,2148,2230,2292,2374,2438,2490,2572,2579,2590,2594,2598,2611,2627,2634,2640,2652,2671,2688,2693,2698,2705,2715,2728,2742,2746,2753,2757,2763,2774,2777,2781,2797,2798,2846,2850,2854,2858,2859,2860,2863,2877,2886,2901,2943,2944,2945,2950,2954,2959,2966,2972,2973,2976,2980,2985,2997,3001,3006,3011,3016,3019,3022,3025,3029,3033,3034,3035,3036,3037,3041,3047,3051,3057,3061,3065,3071,3077,3081,3085,3089,3090,3091,3092,3093,3094,3095,3096,3100,3104,3107,3111,3114,3117,3120,3123,3126,3130,3134,3135,3138,3141,3144,3147,3150,3153,3156,3159,3163,3166,3170,3173,3176,3179,3182,3185,3188,3192,3195,3198,3202,3205,3215,3223,3231,3234,3237,3240,3243,3246,3249,3252,3253,3256,3259,3260,3263,3264,3265,3269,3270,3275,3283,3291,3299,3307,3315,3323,3331,3339,3347,3356,3365,3374,3382,3391,3400,3403,3406,3407,3408,3409,3410,3411,3412,3413,3414,3415,3416,3417,3418,3421,3422,3423,3424,3425,3433,3441,3442,3450,3454,3462,3470,3478,3486,3494,3495,3503,3511,3512,3515,3518,3519,3522,3525,3527,3532,3534,3539,3543,3547,3548,3549,3550,3554,3558,3559,3563,3564,3565,3566,3567,3568,3569,3570,3571,3572,3573,3574,3575,3576,3580,3584,3585,3589,3590,3595,3596,3597,3598,3599,3600,3601,3602,3603,3604,3605,3606,3607,3608,3609,3610,3611,3612,3613,3614,3615,3619,3620,3621,3627,3628,3632,3634,3635,3638,3643,3644,3645,3646,3647,3648,3652,3653,3654,3660,3661,3665,3667,3670,3674,3678,3682,3686,3687,3688,3689,3692,3695,3698,3701,3704,3708,3709,3714,3718,3723,3727,3732,3736,3740,3773,3774,3775,3776,3780,3781,3782,3783,3787,3791,3795,3799,3805,3806,3839,3852,3857,3883,3889,3892,3903,3908,3911,3914,3917,3920,3923,3926,3929,3933,3936,3937,3938,3946,3954,3957,3962,3967,3972,3977,3981,3985,3986,3994,3995,3996,3997,3998,4006,4011,4016,4017,4018,4019,4044,4049,4054,4057,4061,4064,4068,4078,4081,4086,4089,4093,4097,4100,4108,4121,4134,4138,4153,4164,4167,4178,4183,4187,4222,4223,4224,4231,4235,4239,4243,4247,4258,4274,4288,4295,4304,4307,4327,4337,4341,4345,4356,4362,4366,4383,4391,4395,4399,4403,4406,4410,4414,4418,4425,4429,4430,4434,4455,4465,4485,4494,4510,4520,4524,4534,4554,4564,4567,4568,4569,4570,4571,4575,4579,4583,4584,4585,4586,4589,4592,4595,4598,4601,4604,4607,4610,4613,4616,4619,4622,4625,4628,4631,4634,4637,4638,4641,4652,4660,4664,4669,4674,4677,4680,4686,4690,4693,4696,4700,4705,4709,4712,4720,4724,4728,4739,4744,4749,4750,4753,4756,4757,4767,4772,4780,4783,4787,4800,4803,4807,4822,4829,4855,4858,4861,4864,4867,4877,4884,4885,4890,4891,4892,4893,4897,4901,4905,4909,4938,4941,4945,4949,4977,4980,4984,4988,4997,5003,5006,5015,5019,5020,5027,5031,5038,5039,5040,5043,5048,5053,5054,5058,5066,5081,5085,5086,5098,5108,5109,5121,5126,5150,5153,5159,5162,5171,5179,5183,5186,5189,5192,5196,5199,5216,5220,5223,5238,5241,5249,5254,5261,5266,5267,5272,5273,5279,5285,5291,5322,5333,5350,5357,5361,5364,5376,5385,5389,5394,5398,5402,5406,5410,5414,5418,5422,5425,5434,5439,5448,5451,5458,5459,5463,5472,5478,5482,5483,5487,5508,5514,5518,5522,5523,5545,5546,5547,5548,5549,5554,5557,5558,5565,5566,5578,5590,5597,5598,5603,5608,5609,5613,5627,5632,5638,5644,5650,5655,5661,5667,5668,5673,5687,5692,5701,5710,5713,5726,5729,5740,5744,5753,5762,5763,5770,5778,5803,5815,5863,5900,5965,6002,6008,6074,6187,6213,6242,6248,6256,6373,6388,6402,6408,6458,6462,6468,6474,6485,6520,6526,6534,6542,6608,6620,6632,6644,6687,6699,6712,6721,6727,6732,6737,6743,6783,6795,6853,6878,6956,6962,6969,6972,6976,7014,7037,7090,7098,7121,7128,7136,7237,7242,7497,7513", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,206,255,311,371,432,487,537,587,640,698,746,815,863,934,1006,1078,1151,1218,1267,1321,1358,1409,1456,1512,1561,1619,1675,1726,1786,1835,1891,1947,1997,2056,2103,2159,2213,2267,2321,2370,2428,2484,2531,2585,2639,2687,2744,2798,2854,2917,2979,3035,3095,3148,3209,3288,3369,3441,3520,3600,3676,3754,3823,3899,3976,4047,4120,4184,4255,4327,4378,4431,4484,4536,4586,4644,4709,4757,4808,4875,4941,4999,5068,5126,5195,5265,5338,5412,5480,5547,5617,5683,5756,5816,5892,5952,6012,6087,6155,6221,6289,6349,6408,6465,6531,6593,6650,6709,6771,6833,6900,6957,7013,7069,7127,7185,7242,7299,7358,7417,7475,7529,7585,7714,7767,7825,7883,7941,7987,8047,8101,8169,8238,8306,8359,8411,8461,8507,8557,8613,8660,8718,8776,8838,8901,8963,9022,9082,9147,9213,9278,9340,9402,9464,9526,9588,9650,9716,9783,9849,9912,9976,10039,10107,10168,10230,10292,10355,10419,10482,10546,10624,10683,10749,10829,10890,10943,10997,11055,11106,11151,11215,11274,11336,11410,11481,11547,11621,11690,11761,11834,11905,11973,12046,12122,12192,12270,12338,12404,12465,12534,12598,12664,12732,12798,12861,12929,13000,13065,13138,13201,13282,13346,13412,13482,13552,13622,13692,13759,13816,13874,13933,13993,14052,14111,14170,14229,14288,14347,14406,14465,14524,14583,14643,14704,14766,14827,14888,14949,15010,15071,15132,15193,15254,15315,15376,15444,15513,15583,15652,15721,15790,15859,15928,15997,16066,16135,16204,16273,16333,16394,16456,16517,16578,16639,16700,16761,16822,16883,16944,17005,17066,17128,17191,17255,17318,17381,17444,17507,17570,17633,17696,17759,17822,17885,17946,18008,18071,18133,18195,18257,18319,18381,18443,18505,18567,18629,18691,18748,18835,18915,19005,19100,19192,19284,19374,19457,19550,19637,19734,19825,19926,20013,20116,20205,20304,20396,20480,20574,20662,20760,20844,20944,21030,21126,21214,21295,21386,21482,21575,21668,21759,21844,21938,22027,22125,22218,22320,22408,22512,22603,22703,22796,22881,22976,23065,23164,23249,23350,23437,23534,23600,23676,23745,23824,23889,23955,24008,24084,24150,24237,24313,24389,24431,24478,24543,24598,24648,24702,24781,24859,24932,24997,25060,25126,25197,25268,25338,25400,25469,25535,25595,25662,25729,25785,25836,25889,25941,25995,26066,26129,26188,26250,26309,26382,26449,26519,26579,26642,26717,26789,26885,26956,27012,27083,27140,27197,27263,27327,27398,27455,27508,27571,27623,27681,27748,27817,27883,27942,28025,28084,28141,28208,28278,28352,28414,28483,28553,28607,28660,28714,28773,28819,28876,28943,28999,29064,29138,29222,29295,29360,29422,29478,29561,29650,29714,29793,29867,29926,29982,30038,30098,30158,30205,30265,30326,30390,30451,30511,30569,30612,30661,30713,30764,30816,30865,30914,30979,31045,31105,31166,31222,31281,31356,31413,31497,31554,31629,31704,31755,31823,31873,31935,31995,32052,32112,32161,32242,32299,32360,32419,32479,32537,32598,32656,32706,32755,32822,32881,32940,32989,33064,33135,33204,33267,33335,33401,33469,33534,33600,33677,33755,33819,33898,33987,34065,34131,34200,34266,34364,34460,34556,34654,34763,34819,34911,34960,35014,35068,35122,35176,35230,35285,35395,35505,35615,35725,35835,35945,36055,36165,36271,36377,36483,36589,36684,36779,36874,36969,37075,37181,37287,37393,37488,37583,37678,37773,37881,37989,38097,38205,38302,38399,38496,38593,38701,38809,38917,39025,39123,39219,39315,39413,39478,39566,39630,39692,39754,39822,39880,39943,40009,40072,40139,40211,40277,40329,40389,40447,40499,40556,40640,40735,40820,40901,40981,41058,41137,41214,41288,41362,41433,41513,41585,41660,41713,41766,41834,41911,41976,42037,42097,42172,42246,42323,42396,42466,42538,42608,42681,42745,42815,42863,42932,42984,43069,43152,43210,43276,43343,43409,43490,43565,43621,43674,43735,43793,43843,43892,43941,43990,44052,44104,44149,44230,44281,44335,44388,44442,44493,44542,44608,44659,44720,44781,44843,44893,44934,45011,45070,45129,45188,45249,45305,45361,45428,45489,45554,45609,45674,45743,45811,45889,45958,46018,46089,46163,46228,46300,46370,46437,46521,46590,46657,46727,46790,46857,46925,47008,47087,47177,47254,47322,47389,47467,47524,47581,47649,47715,47771,47831,47890,47944,47994,48044,48092,48154,48205,48278,48358,48438,48502,48565,48632,48703,48761,48822,48888,48947,49014,49074,49134,49197,49265,49326,49393,49471,49541,49590,49647,49716,49777,49865,49953,50041,50129,50185,50272,50359,50446,50533,50591,50665,50735,50791,50862,50927,50989,51064,51137,51227,51293,51359,51420,51484,51546,51604,51675,51758,51817,51888,51954,52019,52080,52139,52210,52276,52341,52424,52500,52575,52656,52716,52785,52855,52924,52979,53035,53091,53152,53210,53266,53321,53383,53436,53493,53587,53656,53757,53808,53878,53941,53999,54069,54138,54208,54278,54348,54415,54482,54557,54624,54683,54737,54791,54845,54898,54950,55024,55065,55130,55204,55277,55345,55405,55463,55524,55590,55656,55721,55785,55846,55983,56123,56172,56222,56270,56326,56384,56446,56501,56559,56630,56694,56753,56815,56881,56947,56990,57034,57079,57122,57173,57220,57265,57316,57367,57418,57469,57517,57583,57645,57708,57780,57837,57892,57950,58005,58064,58120,58181,58244,58305,58366,58427,58488,58549,58610,58671,58730,58791,58852,58913,58974,59035,59096,59147,59213,59279,59347,59415,59481,59548,59622,59685,59742,59802,59867,59934,59999,60056,60117,60175,60245,60292,60344,60394,60451,60771,60921,61052,61290,61388,61503,61588,61636,61715,61780,61869,62026,62183,62336,62490,62549,62589,62685,62775,62871,62961,63127,63252,63377,63547,63657,63778,63899,64009,64121,64244,64367,64449,64623,64771,64930,65085,65258,65375,65492,65660,65772,65886,66060,66238,66371,66483,66629,66781,66913,67056,67178,67356,67538,67855,68037,68219,68409,68599,68798,68955,69065,69248,69385,69605,69789,69949,70107,70291,70494,70665,70885,71107,71262,71462,71646,71749,71890,72055,72226,72426,72630,72832,73037,73238,73437,73641,73802,73880,74181,74347,74502,74604,74738,75015,75300,75751,76207,76716,77258,77723,78185,78656,78741,78862,78961,79048,79171,79272,79365,79472,79815,79922,80167,80288,80697,80945,81045,81150,81269,81803,81950,82069,82320,82453,82868,83122,83234,83440,83565,83782,83982,84103,84236,84383,99559,99995,115027,130239,130677,145745,150750,156291,160475,166030,170936,176437,180504,184604,190119,190610,191366,191618,191883,193038,193942,194406,194799,195469,196628,197731,198103,198481,198943,199718,200483,201468,201734,202210,202484,202881,203554,203732,203961,205060,205151,207345,207611,207933,208143,208252,208371,208555,209527,210123,211052,213703,213798,213829,214157,214385,214644,215220,215574,215696,215835,216109,216369,217239,217525,217928,218330,218673,218885,219086,219299,219588,219873,219946,220033,220118,220217,220389,220659,220830,221098,221263,221430,221699,221964,222132,222297,222463,222583,222703,222811,222921,223033,223141,223251,223416,223582,223761,223926,224081,224201,224362,224525,224686,224851,225018,225071,225204,225324,225422,225535,225654,225771,225926,226058,226287,226450,226643,226769,226921,227063,227233,227389,227561,227852,227964,228093,228322,228540,229395,229982,230596,230764,230906,231067,231210,231378,231535,231730,231822,231995,232157,232252,232421,232515,232604,232847,232936,233229,233694,234163,234633,235108,235574,236039,236505,236972,237435,238015,238598,239180,239640,240221,240803,240993,241172,241278,241386,241492,241604,241718,241830,241944,242060,242174,242282,242392,242500,242696,242804,242914,243022,243136,243545,243959,244075,244493,244734,245164,245599,246009,246431,246841,246963,247372,247788,247910,248128,248312,248373,248545,248717,248785,249129,249209,249565,249715,249859,249935,250047,250137,250399,250664,250763,250915,250991,251103,251193,251295,251403,251511,251611,251696,251800,251887,251965,252079,252171,252435,252702,252803,252956,253040,253429,253527,253635,253729,253859,253967,254089,254225,254333,254453,254587,254709,254837,254979,255105,255245,255371,255489,255621,255719,255829,256129,256241,256359,256823,256939,257242,257368,257464,257594,257995,258105,258229,258367,258477,258599,258911,259035,259165,259641,259769,260084,260222,260368,260530,260746,260902,261106,261174,261258,261362,261565,261754,261955,262148,262353,262558,262674,262916,263151,263410,263609,263875,264072,264288,266848,266962,267082,267176,267497,267596,267714,267815,268045,268281,268491,268724,269148,269224,271794,273049,273493,275437,275894,276102,277112,277583,277724,277859,278056,278227,278410,278585,278772,278994,279152,279236,279340,279902,280533,280691,280910,281141,281364,281599,281821,282087,282225,282824,282938,283076,283188,283312,283883,284378,284924,285069,285162,285254,287271,287714,288012,288201,288407,288600,288810,289694,289839,290322,290480,290697,290958,291089,291521,292347,292967,293164,294112,294877,295000,295773,295994,296194,298171,298271,298361,298799,299098,299409,299712,300027,300653,301404,302479,303026,303584,303750,305147,305763,305999,306220,307010,307405,307641,308910,309359,309546,309795,310037,310213,310454,310687,310912,311254,311467,311562,311823,312678,313131,313995,314443,315111,315563,315770,316227,317081,317538,317688,317812,317958,318096,318232,318520,318821,319123,319239,319361,319473,319622,319880,320142,320400,320660,320908,321160,321408,321658,321902,322150,322394,322640,322872,323108,323340,323574,323686,323877,324736,325334,325530,325767,326035,326251,326469,326741,327040,327232,327444,327736,328000,328289,328508,329033,329270,329562,330301,330539,330808,330948,331118,331262,331364,332146,332486,333108,333334,333631,334355,334619,334934,336100,336660,338755,338949,339167,339393,339605,340280,340719,340815,341162,341250,341358,341466,341760,342066,342364,342674,344753,344941,345202,345451,347428,347620,347885,348138,348721,349071,349240,349754,349989,350113,350621,350835,351333,351436,351566,351741,352060,352346,352486,352680,353178,354059,354347,354477,355254,355911,356057,356763,357001,358541,358691,359108,359273,359959,360429,360625,360787,360942,361086,361320,361487,362415,362701,362861,363476,363635,363963,364190,364702,365064,365143,365482,365587,365952,366323,366684,368506,369135,370211,370731,370984,371136,372126,372863,373066,373312,373559,373777,374019,374340,374604,374909,375132,375304,375845,376114,376608,376834,377274,377433,377717,378462,378827,379132,379290,379528,380847,381245,381473,381693,381835,383535,383641,383771,383909,384033,384321,384490,384590,385049,385163,386046,386801,387240,387364,387677,387960,388094,388285,389064,389282,389573,389852,390169,390391,390686,390969,391073,391356,392088,392404,393030,393536,393741,394470,394745,395406,395595,396242,396903,397023,397520,398054,399671,400368,402822,404729,407576,409668,410000,414168,419544,420822,421917,422101,422312,429484,430192,430969,431353,433781,434012,434316,434633,435350,436806,437113,437621,438019,441475,442093,442675,443170,445157,445867,446482,446976,447349,447578,447786,447922,450229,450905,454165,455583,460750,460971,461264,461401,461636,463490,464480,467124,467565,468795,469216,469532,475045,475310,489618,490687", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,1001,1005,1009,1017,1024,1032,1042,1051,1060,1069,1070,1071,1072,1073,1074,1075,1076,1077,1082,1083,1087,1088,1094,1098,1099,1100,1101,1111,1112,1113,1117,1118,1124,1128,1129,1132,1133,1136,1139,1140,1141,1142,1342,1348,1546,1746,1752,1950,2013,2095,2147,2229,2291,2373,2437,2489,2571,2578,2589,2593,2597,2610,2626,2633,2639,2651,2670,2687,2692,2697,2704,2714,2727,2741,2745,2752,2756,2762,2773,2776,2780,2796,2797,2845,2849,2853,2857,2858,2859,2862,2876,2885,2900,2942,2943,2944,2949,2953,2958,2965,2971,2972,2975,2979,2984,2996,3000,3005,3010,3015,3018,3021,3024,3028,3032,3033,3034,3035,3036,3040,3046,3050,3056,3060,3064,3070,3076,3080,3084,3088,3089,3090,3091,3092,3093,3094,3095,3099,3103,3106,3110,3113,3116,3119,3122,3125,3129,3133,3134,3137,3140,3143,3146,3149,3152,3155,3158,3162,3165,3169,3172,3175,3178,3181,3184,3187,3191,3194,3197,3201,3204,3214,3222,3230,3233,3236,3239,3242,3245,3248,3251,3252,3255,3258,3259,3262,3263,3264,3268,3269,3274,3282,3290,3298,3306,3314,3322,3330,3338,3346,3355,3364,3373,3381,3390,3399,3402,3405,3406,3407,3408,3409,3410,3411,3412,3413,3414,3415,3416,3417,3420,3421,3422,3423,3424,3432,3440,3441,3449,3453,3461,3469,3477,3485,3493,3494,3502,3510,3511,3514,3517,3518,3521,3524,3526,3531,3533,3538,3542,3546,3547,3548,3549,3553,3557,3558,3562,3563,3564,3565,3566,3567,3568,3569,3570,3571,3572,3573,3574,3575,3579,3583,3584,3588,3589,3594,3595,3596,3597,3598,3599,3600,3601,3602,3603,3604,3605,3606,3607,3608,3609,3610,3611,3612,3613,3614,3618,3619,3620,3626,3627,3631,3633,3634,3637,3642,3643,3644,3645,3646,3647,3651,3652,3653,3659,3660,3664,3666,3669,3673,3677,3681,3685,3686,3687,3688,3691,3694,3697,3700,3703,3707,3708,3713,3717,3722,3726,3731,3735,3739,3772,3773,3774,3775,3779,3780,3781,3782,3786,3790,3794,3798,3804,3805,3838,3851,3856,3882,3888,3891,3902,3907,3910,3913,3916,3919,3922,3925,3928,3932,3935,3936,3937,3945,3953,3956,3961,3966,3971,3976,3980,3984,3985,3993,3994,3995,3996,3997,4005,4010,4015,4016,4017,4018,4043,4048,4053,4056,4060,4063,4067,4077,4080,4085,4088,4092,4096,4099,4107,4120,4133,4137,4152,4163,4166,4177,4182,4186,4221,4222,4223,4230,4234,4238,4242,4246,4257,4273,4287,4294,4303,4306,4326,4336,4340,4344,4355,4361,4365,4382,4390,4394,4398,4402,4405,4409,4413,4417,4424,4428,4429,4433,4454,4464,4484,4493,4509,4519,4523,4533,4553,4563,4566,4567,4568,4569,4570,4574,4578,4582,4583,4584,4585,4588,4591,4594,4597,4600,4603,4606,4609,4612,4615,4618,4621,4624,4627,4630,4633,4636,4637,4640,4651,4659,4663,4668,4673,4676,4679,4685,4689,4692,4695,4699,4704,4708,4711,4719,4723,4727,4738,4743,4748,4749,4752,4755,4756,4766,4771,4779,4782,4786,4799,4802,4806,4821,4828,4854,4857,4860,4863,4866,4876,4883,4884,4889,4890,4891,4892,4896,4900,4904,4908,4937,4940,4944,4948,4976,4979,4983,4987,4996,5002,5005,5014,5018,5019,5026,5030,5037,5038,5039,5042,5047,5052,5053,5057,5065,5080,5084,5085,5097,5107,5108,5120,5125,5149,5152,5158,5161,5170,5178,5182,5185,5188,5191,5195,5198,5215,5219,5222,5237,5240,5248,5253,5260,5265,5266,5271,5272,5278,5284,5290,5321,5332,5349,5356,5360,5363,5375,5384,5388,5393,5397,5401,5405,5409,5413,5417,5421,5424,5433,5438,5447,5450,5457,5458,5462,5471,5477,5481,5482,5486,5507,5513,5517,5521,5522,5544,5545,5546,5547,5548,5553,5556,5557,5564,5565,5577,5589,5596,5597,5602,5607,5608,5612,5626,5631,5637,5643,5649,5654,5660,5666,5667,5672,5686,5691,5700,5709,5712,5725,5728,5739,5743,5752,5761,5762,5769,5777,5802,5814,5862,5899,5964,6001,6007,6073,6186,6212,6241,6247,6255,6372,6387,6401,6407,6457,6461,6467,6473,6484,6519,6525,6533,6541,6607,6619,6631,6643,6686,6698,6711,6720,6726,6731,6736,6742,6782,6794,6852,6877,6955,6961,6968,6971,6975,7013,7036,7089,7097,7120,7127,7135,7236,7241,7496,7512,7522", "endColumns": "55,48,55,59,60,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,46,55,48,57,55,50,59,48,55,55,49,58,46,55,53,53,53,48,57,55,46,53,53,47,56,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,63,70,71,50,52,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,58,61,61,66,56,55,55,57,57,56,56,58,58,57,53,55,9,52,57,57,57,45,59,53,67,68,67,52,51,49,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,53,57,50,44,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,86,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,83,93,87,97,83,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,84,94,88,98,84,100,86,96,65,75,68,78,64,65,52,75,65,86,75,75,41,46,64,54,49,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,53,52,53,58,45,56,66,55,64,73,83,72,64,61,55,82,88,63,78,73,58,55,55,59,59,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,74,56,83,56,74,74,50,67,49,61,59,56,59,48,80,56,60,58,59,57,60,57,49,48,66,58,58,48,74,70,68,62,67,65,67,64,65,76,77,63,78,88,77,65,68,65,97,95,95,97,108,55,91,48,53,53,53,53,53,54,109,109,109,109,109,109,109,109,105,105,105,105,94,94,94,94,105,105,105,105,94,94,94,94,107,107,107,107,96,96,96,96,107,107,107,107,97,95,95,97,64,87,63,61,61,67,57,62,65,62,66,71,65,51,59,57,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,52,52,67,76,64,60,59,74,73,76,72,69,71,69,72,63,69,47,68,51,84,82,57,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,62,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,55,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,54,61,52,56,93,68,100,50,69,62,57,69,68,69,69,69,66,66,74,66,58,53,53,53,52,51,73,40,64,73,72,67,59,57,60,65,65,64,63,60,136,139,48,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,54,57,54,58,55,60,62,60,60,60,60,60,60,60,58,60,60,60,60,60,60,50,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,69,46,51,49,56,12,149,130,237,97,114,84,47,78,64,88,156,156,152,153,58,39,95,89,95,89,165,124,124,169,109,120,120,109,111,122,122,81,173,147,158,154,172,116,116,167,111,113,173,177,132,111,145,151,131,142,121,177,181,316,181,181,189,189,198,156,109,182,136,219,183,159,157,183,202,170,219,221,154,199,183,102,140,164,170,199,203,201,204,200,198,203,160,77,300,165,154,101,10,10,10,10,10,10,10,10,10,10,84,120,98,86,122,100,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,90,10,10,10,10,108,118,10,10,10,10,10,94,30,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,10,10,10,10,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,10,10,10,10,10,10,10,10,10,52,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,60,10,10,10,10,10,10,10,10,75,111,89,10,10,98,10,75,111,89,101,107,107,99,84,103,86,77,113,91,10,10,100,10,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,113,119,93,10,98,117,100,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,94,10,10,10,10,10,10,10,10,10,10,10,10,123,145,137,135,10,10,10,115,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,101,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,95,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22", "endOffsets": "201,250,306,366,427,482,532,582,635,693,741,810,858,929,1001,1073,1146,1213,1262,1316,1353,1404,1451,1507,1556,1614,1670,1721,1781,1830,1886,1942,1992,2051,2098,2154,2208,2262,2316,2365,2423,2479,2526,2580,2634,2682,2739,2793,2849,2912,2974,3030,3090,3143,3204,3283,3364,3436,3515,3595,3671,3749,3818,3894,3971,4042,4115,4179,4250,4322,4373,4426,4479,4531,4581,4639,4704,4752,4803,4870,4936,4994,5063,5121,5190,5260,5333,5407,5475,5542,5612,5678,5751,5811,5887,5947,6007,6082,6150,6216,6284,6344,6403,6460,6526,6588,6645,6704,6766,6828,6895,6952,7008,7064,7122,7180,7237,7294,7353,7412,7470,7524,7580,7709,7762,7820,7878,7936,7982,8042,8096,8164,8233,8301,8354,8406,8456,8502,8552,8608,8655,8713,8771,8833,8896,8958,9017,9077,9142,9208,9273,9335,9397,9459,9521,9583,9645,9711,9778,9844,9907,9971,10034,10102,10163,10225,10287,10350,10414,10477,10541,10619,10678,10744,10824,10885,10938,10992,11050,11101,11146,11210,11269,11331,11405,11476,11542,11616,11685,11756,11829,11900,11968,12041,12117,12187,12265,12333,12399,12460,12529,12593,12659,12727,12793,12856,12924,12995,13060,13133,13196,13277,13341,13407,13477,13547,13617,13687,13754,13811,13869,13928,13988,14047,14106,14165,14224,14283,14342,14401,14460,14519,14578,14638,14699,14761,14822,14883,14944,15005,15066,15127,15188,15249,15310,15371,15439,15508,15578,15647,15716,15785,15854,15923,15992,16061,16130,16199,16268,16328,16389,16451,16512,16573,16634,16695,16756,16817,16878,16939,17000,17061,17123,17186,17250,17313,17376,17439,17502,17565,17628,17691,17754,17817,17880,17941,18003,18066,18128,18190,18252,18314,18376,18438,18500,18562,18624,18686,18743,18830,18910,19000,19095,19187,19279,19369,19452,19545,19632,19729,19820,19921,20008,20111,20200,20299,20391,20475,20569,20657,20755,20839,20939,21025,21121,21209,21290,21381,21477,21570,21663,21754,21839,21933,22022,22120,22213,22315,22403,22507,22598,22698,22791,22876,22971,23060,23159,23244,23345,23432,23529,23595,23671,23740,23819,23884,23950,24003,24079,24145,24232,24308,24384,24426,24473,24538,24593,24643,24697,24776,24854,24927,24992,25055,25121,25192,25263,25333,25395,25464,25530,25590,25657,25724,25780,25831,25884,25936,25990,26061,26124,26183,26245,26304,26377,26444,26514,26574,26637,26712,26784,26880,26951,27007,27078,27135,27192,27258,27322,27393,27450,27503,27566,27618,27676,27743,27812,27878,27937,28020,28079,28136,28203,28273,28347,28409,28478,28548,28602,28655,28709,28768,28814,28871,28938,28994,29059,29133,29217,29290,29355,29417,29473,29556,29645,29709,29788,29862,29921,29977,30033,30093,30153,30200,30260,30321,30385,30446,30506,30564,30607,30656,30708,30759,30811,30860,30909,30974,31040,31100,31161,31217,31276,31351,31408,31492,31549,31624,31699,31750,31818,31868,31930,31990,32047,32107,32156,32237,32294,32355,32414,32474,32532,32593,32651,32701,32750,32817,32876,32935,32984,33059,33130,33199,33262,33330,33396,33464,33529,33595,33672,33750,33814,33893,33982,34060,34126,34195,34261,34359,34455,34551,34649,34758,34814,34906,34955,35009,35063,35117,35171,35225,35280,35390,35500,35610,35720,35830,35940,36050,36160,36266,36372,36478,36584,36679,36774,36869,36964,37070,37176,37282,37388,37483,37578,37673,37768,37876,37984,38092,38200,38297,38394,38491,38588,38696,38804,38912,39020,39118,39214,39310,39408,39473,39561,39625,39687,39749,39817,39875,39938,40004,40067,40134,40206,40272,40324,40384,40442,40494,40551,40635,40730,40815,40896,40976,41053,41132,41209,41283,41357,41428,41508,41580,41655,41708,41761,41829,41906,41971,42032,42092,42167,42241,42318,42391,42461,42533,42603,42676,42740,42810,42858,42927,42979,43064,43147,43205,43271,43338,43404,43485,43560,43616,43669,43730,43788,43838,43887,43936,43985,44047,44099,44144,44225,44276,44330,44383,44437,44488,44537,44603,44654,44715,44776,44838,44888,44929,45006,45065,45124,45183,45244,45300,45356,45423,45484,45549,45604,45669,45738,45806,45884,45953,46013,46084,46158,46223,46295,46365,46432,46516,46585,46652,46722,46785,46852,46920,47003,47082,47172,47249,47317,47384,47462,47519,47576,47644,47710,47766,47826,47885,47939,47989,48039,48087,48149,48200,48273,48353,48433,48497,48560,48627,48698,48756,48817,48883,48942,49009,49069,49129,49192,49260,49321,49388,49466,49536,49585,49642,49711,49772,49860,49948,50036,50124,50180,50267,50354,50441,50528,50586,50660,50730,50786,50857,50922,50984,51059,51132,51222,51288,51354,51415,51479,51541,51599,51670,51753,51812,51883,51949,52014,52075,52134,52205,52271,52336,52419,52495,52570,52651,52711,52780,52850,52919,52974,53030,53086,53147,53205,53261,53316,53378,53431,53488,53582,53651,53752,53803,53873,53936,53994,54064,54133,54203,54273,54343,54410,54477,54552,54619,54678,54732,54786,54840,54893,54945,55019,55060,55125,55199,55272,55340,55400,55458,55519,55585,55651,55716,55780,55841,55978,56118,56167,56217,56265,56321,56379,56441,56496,56554,56625,56689,56748,56810,56876,56942,56985,57029,57074,57117,57168,57215,57260,57311,57362,57413,57464,57512,57578,57640,57703,57775,57832,57887,57945,58000,58059,58115,58176,58239,58300,58361,58422,58483,58544,58605,58666,58725,58786,58847,58908,58969,59030,59091,59142,59208,59274,59342,59410,59476,59543,59617,59680,59737,59797,59862,59929,59994,60051,60112,60170,60240,60287,60339,60389,60446,60766,60916,61047,61285,61383,61498,61583,61631,61710,61775,61864,62021,62178,62331,62485,62544,62584,62680,62770,62866,62956,63122,63247,63372,63542,63652,63773,63894,64004,64116,64239,64362,64444,64618,64766,64925,65080,65253,65370,65487,65655,65767,65881,66055,66233,66366,66478,66624,66776,66908,67051,67173,67351,67533,67850,68032,68214,68404,68594,68793,68950,69060,69243,69380,69600,69784,69944,70102,70286,70489,70660,70880,71102,71257,71457,71641,71744,71885,72050,72221,72421,72625,72827,73032,73233,73432,73636,73797,73875,74176,74342,74497,74599,74733,75010,75295,75746,76202,76711,77253,77718,78180,78651,78736,78857,78956,79043,79166,79267,79360,79467,79810,79917,80162,80283,80692,80940,81040,81145,81264,81798,81945,82064,82315,82448,82863,83117,83229,83435,83560,83777,83977,84098,84231,84378,99554,99990,115022,130234,130672,145740,150745,156286,160470,166025,170931,176432,180499,184599,190114,190605,191361,191613,191878,193033,193937,194401,194794,195464,196623,197726,198098,198476,198938,199713,200478,201463,201729,202205,202479,202876,203549,203727,203956,205055,205146,207340,207606,207928,208138,208247,208366,208550,209522,210118,211047,213698,213793,213824,214152,214380,214639,215215,215569,215691,215830,216104,216364,217234,217520,217923,218325,218668,218880,219081,219294,219583,219868,219941,220028,220113,220212,220384,220654,220825,221093,221258,221425,221694,221959,222127,222292,222458,222578,222698,222806,222916,223028,223136,223246,223411,223577,223756,223921,224076,224196,224357,224520,224681,224846,225013,225066,225199,225319,225417,225530,225649,225766,225921,226053,226282,226445,226638,226764,226916,227058,227228,227384,227556,227847,227959,228088,228317,228535,229390,229977,230591,230759,230901,231062,231205,231373,231530,231725,231817,231990,232152,232247,232416,232510,232599,232842,232931,233224,233689,234158,234628,235103,235569,236034,236500,236967,237430,238010,238593,239175,239635,240216,240798,240988,241167,241273,241381,241487,241599,241713,241825,241939,242055,242169,242277,242387,242495,242691,242799,242909,243017,243131,243540,243954,244070,244488,244729,245159,245594,246004,246426,246836,246958,247367,247783,247905,248123,248307,248368,248540,248712,248780,249124,249204,249560,249710,249854,249930,250042,250132,250394,250659,250758,250910,250986,251098,251188,251290,251398,251506,251606,251691,251795,251882,251960,252074,252166,252430,252697,252798,252951,253035,253424,253522,253630,253724,253854,253962,254084,254220,254328,254448,254582,254704,254832,254974,255100,255240,255366,255484,255616,255714,255824,256124,256236,256354,256818,256934,257237,257363,257459,257589,257990,258100,258224,258362,258472,258594,258906,259030,259160,259636,259764,260079,260217,260363,260525,260741,260897,261101,261169,261253,261357,261560,261749,261950,262143,262348,262553,262669,262911,263146,263405,263604,263870,264067,264283,266843,266957,267077,267171,267492,267591,267709,267810,268040,268276,268486,268719,269143,269219,271789,273044,273488,275432,275889,276097,277107,277578,277719,277854,278051,278222,278405,278580,278767,278989,279147,279231,279335,279897,280528,280686,280905,281136,281359,281594,281816,282082,282220,282819,282933,283071,283183,283307,283878,284373,284919,285064,285157,285249,287266,287709,288007,288196,288402,288595,288805,289689,289834,290317,290475,290692,290953,291084,291516,292342,292962,293159,294107,294872,294995,295768,295989,296189,298166,298266,298356,298794,299093,299404,299707,300022,300648,301399,302474,303021,303579,303745,305142,305758,305994,306215,307005,307400,307636,308905,309354,309541,309790,310032,310208,310449,310682,310907,311249,311462,311557,311818,312673,313126,313990,314438,315106,315558,315765,316222,317076,317533,317683,317807,317953,318091,318227,318515,318816,319118,319234,319356,319468,319617,319875,320137,320395,320655,320903,321155,321403,321653,321897,322145,322389,322635,322867,323103,323335,323569,323681,323872,324731,325329,325525,325762,326030,326246,326464,326736,327035,327227,327439,327731,327995,328284,328503,329028,329265,329557,330296,330534,330803,330943,331113,331257,331359,332141,332481,333103,333329,333626,334350,334614,334929,336095,336655,338750,338944,339162,339388,339600,340275,340714,340810,341157,341245,341353,341461,341755,342061,342359,342669,344748,344936,345197,345446,347423,347615,347880,348133,348716,349066,349235,349749,349984,350108,350616,350830,351328,351431,351561,351736,352055,352341,352481,352675,353173,354054,354342,354472,355249,355906,356052,356758,356996,358536,358686,359103,359268,359954,360424,360620,360782,360937,361081,361315,361482,362410,362696,362856,363471,363630,363958,364185,364697,365059,365138,365477,365582,365947,366318,366679,368501,369130,370206,370726,370979,371131,372121,372858,373061,373307,373554,373772,374014,374335,374599,374904,375127,375299,375840,376109,376603,376829,377269,377428,377712,378457,378822,379127,379285,379523,380842,381240,381468,381688,381830,383530,383636,383766,383904,384028,384316,384485,384585,385044,385158,386041,386796,387235,387359,387672,387955,388089,388280,389059,389277,389568,389847,390164,390386,390681,390964,391068,391351,392083,392399,393025,393531,393736,394465,394740,395401,395590,396237,396898,397018,397515,398049,399666,400363,402817,404724,407571,409663,409995,414163,419539,420817,421912,422096,422307,429479,430187,430964,431348,433776,434007,434311,434628,435345,436801,437108,437616,438014,441470,442088,442670,443165,445152,445862,446477,446971,447344,447573,447781,447917,450224,450900,454160,455578,460745,460966,461259,461396,461631,463485,464475,467119,467560,468790,469211,469527,475040,475305,489613,490682,491053"}, "to": {"startLines": "15,23,39,40,41,42,46,47,48,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,101,102,122,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,201,203,211,212,213,214,215,216,218,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,388,389,390,411,426,435,441,442,443,444,445,460,462,464,465,473,474,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,515,516,528,536,538,571,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,814,815,816,817,818,819,820,821,822,823,824,825,848,943,944,951,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1539,1540,1651,1652,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1676,1679,1680,1681,1699,1700,1701,1702,1703,1704,1705,1716,1727,1728,1732,1733,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1781,1791,1825,1826,1827,1861,1862,1863,1864,1865,1896,1955,1956,1957,1968,1974,1980,1981,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2059,2060,2061,2062,2063,2100,2114,2118,2168,2176,2183,2353,2363,2372,2381,2450,2451,2452,2453,2454,2455,2456,2457,2458,2463,2464,2468,2469,2475,2479,2480,2481,2482,2492,2493,2494,2498,2499,2505,2509,2582,2585,2586,2589,2592,2593,2594,2595,2795,2801,2999,3199,3205,3403,3466,3548,3600,3682,3744,3826,3890,3942,4024,4031,4042,4046,4050,4823,4839,4846,4852,4864,4883,4900,4905,4910,4917,4927,4940,4954,4958,4965,4969,4975,4986,4989,4993,5008,5009,5057,5061,5065,5069,5070,5071,5074,5088,5095,5109,5151,5200,5370,5375,5379,5384,5391,5397,5398,5401,5405,5410,5422,5426,5431,5436,5441,5444,5447,5450,5454,5601,5602,5603,5604,5686,5690,5696,5700,5706,5710,5714,5720,5726,5730,5734,5738,5739,5740,5741,5742,5743,5744,5745,5749,5753,5756,5760,5763,5766,5769,5772,5775,5779,5783,5784,5787,5790,5793,5796,5799,5802,5805,5808,5812,5815,5819,5822,5825,5828,5831,5834,5837,5841,5844,5861,5865,5868,5878,5886,5894,5897,5900,5903,5906,5909,5984,5987,5988,5991,5994,5995,5998,5999,6000,6004,6005,6010,6018,6026,6034,6042,6050,6058,6066,6074,6082,6091,6100,6109,6117,6126,6135,6138,6141,6142,6143,6144,6145,6146,6147,6148,6149,6150,6151,6152,6153,6156,6157,6158,6159,6160,6168,6176,6177,6185,6189,6197,6205,6213,6221,6229,6230,6238,6246,6247,6250,6253,6254,6257,6335,6337,6342,6344,6349,6353,6370,6371,6372,6373,6377,6381,6382,6386,6387,6388,6389,6390,6391,6392,6393,6394,6395,6396,6397,6398,6399,6403,6407,6408,6412,6413,6418,6419,6420,6421,6422,6423,6424,6425,6426,6427,6428,6429,6430,6431,6432,6433,6434,6435,6436,6437,6438,6442,6443,6444,6450,6451,6455,6457,6458,6461,6466,6467,6468,6469,6470,6471,6475,6476,6477,6483,6484,6488,6490,6493,6497,6501,6505,6559,6560,6561,6562,6565,6568,6571,6574,6577,6581,6582,6587,6591,6596,6600,6605,6609,6613,6646,6647,6648,6649,6653,6654,6655,6656,6660,6664,6668,6672,6678,6679,6712,6725,6730,6756,6762,6765,6776,6781,6784,6787,6790,6793,6796,6799,6802,6806,6809,6810,6811,6819,6827,6830,6835,6840,6845,6850,6854,6858,6859,6867,6868,6869,6870,6871,6879,6884,6889,6890,6891,6892,6917,6922,6927,6930,6934,6937,6941,6951,6954,6959,6962,6966,6970,7136,7144,7157,7170,7174,7189,7200,7203,7214,7219,7223,7258,7259,7260,7267,7271,7275,7279,7283,7294,7310,7324,7331,7340,7343,7363,7373,7377,7381,7392,7398,7402,7419,7427,7431,7435,7439,7442,7446,7450,7454,7461,7465,7466,7470,7491,7501,7521,7530,7546,7556,7560,7570,7590,7600,7603,7604,7605,7606,7607,7611,7615,7619,7620,7621,7622,7625,7628,7631,7634,7637,7640,7643,7646,7649,7652,7655,7658,7661,7664,7667,7670,7673,7674,7677,7688,7696,7700,7705,7710,7713,7716,7722,7726,7729,7732,7736,7741,7745,7748,7756,7760,7764,7775,7780,7785,7786,7789,7792,7793,7803,7808,7816,7819,7823,7836,7839,7843,7858,7865,7891,7894,7897,7900,7903,7913,7919,7920,7925,7926,7927,7928,7932,7936,7940,7944,7973,7976,7980,7984,8012,8015,8019,8023,8032,8038,8041,8050,8054,8055,8062,8066,8073,8074,8075,8078,8083,8088,8089,8093,8101,8116,8120,8121,8133,8143,8144,8156,8161,8185,8188,8194,8197,8206,8214,8218,8221,8224,8227,8231,8234,8251,8255,8258,8273,8276,8284,8289,8296,8301,8302,8307,8308,8314,8320,8326,8357,8368,8385,8392,8396,8399,8411,8420,8424,8429,8433,8437,8441,8445,8449,8453,8457,8460,8469,8474,8483,8486,8493,8494,8498,8507,8513,8517,8518,8522,8543,8549,8553,8557,8558,8576,8577,8578,8579,8580,8585,8588,8589,8595,8596,8608,8620,8627,8628,8633,8638,8639,8643,8657,8662,8668,8674,8680,8685,8691,8697,8698,8703,8717,8722,8731,8740,8743,8756,8759,8770,8774,8783,8792,8793,8800,8979,8994,9004,9563,9594,9640,9676,9682,9803,9912,9932,9953,9959,9967,10066,10329,10343,10349,10393,10397,10457,10646,10699,10813,10819,10827,10835,10889,10898,10910,10922,10960,10968,10980,10986,10992,10997,11002,11008,11224,11236,11278,11301,11457,11462,11495,11498,11537,11575,11695,11746,11754,11946,11950,11958,12069,12073,12297,12360", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "595,932,1560,1616,1676,1737,1941,1991,2041,2094,2152,2251,2320,2368,2439,2511,2583,2656,2723,2772,2826,2863,2914,2961,3017,3066,3124,3180,3231,3291,3340,3396,3452,3502,3561,3608,3664,3718,3772,3826,3875,3933,3989,4036,4090,4144,4192,4868,4922,5755,5866,5928,5984,6044,6097,6158,6237,6318,6390,6469,6549,6625,6703,6772,6848,6925,6996,7069,7133,7204,9626,9722,10078,10131,10183,10233,10291,10356,10469,15963,16030,16096,16154,16223,16281,16350,16420,16493,16567,16635,16702,16772,16838,16911,16971,17047,17107,17167,17242,17310,17376,17444,17504,17563,17620,17686,17748,17805,17864,17926,17988,18055,18112,18168,18224,18282,18340,18397,18454,18513,18572,18630,18684,18740,19075,19128,19186,20086,20646,20999,21345,21399,21467,21536,21604,22324,22422,22531,22577,22914,22970,23064,23122,23180,23242,23305,23367,23426,23486,23551,23617,23682,23744,23806,23868,23930,23992,24054,24120,24187,24253,24316,24380,24443,24511,24572,24634,24696,24759,24823,24886,24950,25028,25087,25153,25233,25294,25347,25507,25565,26056,26394,26507,28059,31327,31401,31472,31538,31612,31681,31752,31825,31896,31964,32037,32113,32183,32261,32329,32395,32456,32525,32589,32655,32723,32789,32852,32920,32991,33056,33129,33192,33273,33337,33403,33473,33543,33613,33683,34967,35024,35082,35141,35201,35260,35319,35378,35437,35496,35555,35614,35673,35732,35791,35851,35912,35974,36035,36096,36157,36218,36279,36340,36401,36462,36523,36584,36652,36721,36791,36860,36929,36998,37067,37136,37205,37274,37343,37412,37481,37541,37602,37664,37725,37786,37847,37908,37969,38030,38091,38152,38213,38274,38336,38399,38463,38526,38589,38652,38715,38778,38841,38904,38967,39030,39093,39154,39216,39279,39341,39403,39465,39527,39589,39651,39713,39775,39837,39899,39956,40043,40123,40213,40308,40400,40492,40582,40665,40758,40845,40942,41033,41134,41221,41324,41413,41512,41604,41688,41782,41870,41968,42052,42152,42238,42334,42422,42503,42594,42690,42783,42876,42967,43052,43146,43235,43333,43426,43528,43616,43720,43811,43911,44004,44089,44184,44273,44372,44457,44558,44645,45414,45480,45556,45625,45704,45769,45835,45888,45964,46030,46117,46193,47833,54204,54251,54697,55292,55342,55396,55475,55553,55626,55691,55754,55820,55891,55962,56032,56094,56163,56229,56289,56356,56423,56479,56530,56583,56635,56689,56760,56823,56882,56944,57003,57076,57143,57213,57273,57336,57411,57483,57579,57650,57706,57777,57834,57891,57957,58021,58092,58149,58202,58265,58317,58375,61691,61760,61826,61885,61968,62027,62084,62151,62221,62295,62357,62426,62496,62550,62603,62657,62716,62762,62819,62886,62942,63007,63081,63165,63238,63303,63365,63421,63504,63593,63657,63736,63810,63869,63925,63981,64041,64101,64148,64208,64269,64333,64394,64454,64512,64555,64604,64656,64707,64759,64808,64857,64922,64988,65048,65109,65165,65224,65299,65356,65440,65497,65572,65647,65698,65766,65816,65878,65938,65995,66055,66104,66185,66242,66303,66362,66422,66480,66541,66599,66649,66698,66765,66824,66883,66932,67007,67078,67147,67210,67278,67344,67412,67477,67543,67620,67698,67762,67841,67930,68008,68074,68143,68209,68307,68403,68499,68597,68706,68762,68854,68903,68957,69011,69065,69119,69173,69228,69338,69448,69558,69668,69778,69888,69998,70108,70214,70320,70426,70532,70627,70722,70817,70912,71018,71124,71230,71336,71431,71526,71621,71716,71824,71932,72040,72148,72245,72342,72439,72536,72644,72752,72860,72968,73066,73162,73258,73356,73421,73509,73573,73635,73697,73765,73823,73886,73952,74015,74082,74154,74220,74272,74332,74390,74442,74499,74583,74678,74763,74844,74924,75001,75080,75157,75231,75305,75376,75456,75528,75603,75656,75709,75777,75854,75919,75980,76040,76115,76189,76266,76339,76409,76481,76551,76624,76688,76758,76806,76875,76927,77012,77095,77153,77219,77286,77352,77433,77508,77564,77617,77678,77736,77786,77835,77884,77933,77995,78047,78092,78173,78224,78278,78331,78385,78436,78485,78551,78602,78663,78724,78786,78836,78877,78954,79013,79072,79131,79192,79248,79304,79371,79432,79497,79552,79617,79686,79754,79832,79901,79961,80032,80106,80171,80243,80313,80380,80464,80533,80600,80670,80733,80800,80868,80951,81030,81120,81197,81265,81332,81410,81467,81524,81592,81658,81714,81774,81833,81887,81937,81987,82035,82097,82148,82221,82301,82381,82445,82508,82575,82646,82704,82765,82831,82890,82957,83017,83077,83140,83208,83269,83336,83414,83484,83533,83590,83659,83720,83808,83896,83984,84072,84128,84215,84302,84389,84476,84534,84608,84678,84734,84805,84870,84932,85007,85080,85170,85236,85302,85363,85427,85489,85547,85618,85701,85760,85831,85897,85962,86023,86082,86153,86219,86284,86367,86443,86518,86599,86659,86728,86798,86867,86922,86978,87034,87095,87153,87209,87264,87326,87379,87436,87530,87599,87700,87751,87821,87884,87942,88012,88081,88151,88221,88291,88358,88425,88500,88567,88626,88680,88734,88788,88841,88893,90459,90500,90565,90639,90712,90780,90840,90898,90959,91025,91091,91156,91220,95574,95711,101522,101571,101768,101816,101872,101930,101992,102047,102105,102176,102240,102299,102361,102427,102862,103007,103051,103096,104074,104125,104172,104217,104268,104319,104370,104887,105487,105553,105800,105863,106159,106216,106271,106329,106384,106443,106499,106560,106623,106684,106745,106806,106867,106928,106989,107050,107109,107170,107231,107292,107353,107414,107475,107526,107592,107658,107726,107794,107860,107927,108001,108064,108121,108181,108246,108313,108378,108435,108496,108554,108624,108671,108723,108837,109531,111961,112111,112242,116134,116232,116347,116432,116480,119823,124425,124514,124671,125746,126204,126871,126930,127082,127178,127268,127364,127454,127620,127745,127870,128040,128150,128271,128392,128502,128614,128737,128860,128942,129116,129264,129423,129578,129751,129868,129985,130153,130265,130379,130553,130731,130864,130976,131122,131274,131406,131549,132065,132243,132425,132742,132924,133106,133296,133486,133685,133842,133952,134135,134272,134492,134676,134836,134994,135178,135381,135552,135772,135994,136149,136349,136533,136636,136777,136942,137113,137313,137517,137719,137924,138125,138324,138528,138805,138883,139184,139350,139505,143017,143921,144198,147433,147884,148340,159477,159991,160428,160862,165202,165287,165408,165507,165594,165717,165818,165911,166018,166361,166468,166713,166834,167243,167491,167591,167696,167815,168324,168471,168590,168841,168974,169389,169643,175124,175330,175455,175672,175872,175993,176126,176273,190986,191422,205975,220724,221162,235751,240592,246009,250100,255531,260273,265650,269634,273626,279017,279508,280264,280494,280737,329111,330015,330479,330872,331542,332701,333698,333991,334290,334673,335448,336213,337100,337366,337842,338116,338513,339186,339364,339593,340518,340609,342803,343069,343391,343601,343710,343829,344013,344985,345455,346206,348683,350929,360435,360763,360991,361250,361826,362180,362302,362441,362715,362975,363845,364131,364534,364936,365279,365491,365692,365905,366194,377278,377351,377438,377523,382437,382609,382879,383050,383318,383483,383650,383919,384184,384352,384517,384683,384803,384923,385031,385141,385253,385361,385471,385636,385802,385981,386146,386301,386421,386582,386745,386906,387071,387238,387291,387424,387544,387642,387755,387874,387991,388146,388278,388507,388670,388863,388989,389141,389283,389453,389609,389781,390072,390184,391257,391486,391704,392559,393146,393760,393928,394070,394231,394374,394542,401553,401748,401840,402013,402175,402270,402439,402533,402622,402865,402954,403247,403712,404181,404651,405126,405592,406057,406523,406990,407453,407923,408396,408868,409328,409799,410271,410461,410640,410746,410854,410960,411072,411186,411298,411412,411528,411642,411750,411860,411968,412164,412272,412382,412490,412604,413013,413427,413543,413961,414202,414632,415067,415477,415899,416309,416431,416840,417256,417378,417596,417780,417841,418013,422959,423027,423371,423451,423807,423957,424835,424911,425023,425113,425375,425640,425739,425891,425967,426079,426169,426271,426379,426487,426587,426672,426776,426863,426941,427055,427147,427411,427678,427779,427932,428016,428405,428503,428611,428705,428835,428943,429065,429201,429309,429429,429563,429685,429813,429955,430081,430221,430347,430465,430597,430695,430805,431105,431217,431335,431799,431915,432218,432344,432440,432570,432971,433081,433205,433343,433453,433575,433887,434011,434141,434617,434745,435060,435198,435344,435506,435722,435878,439338,439406,439490,439594,439797,439986,440187,440380,440585,440790,440906,441101,441289,441501,441653,441872,442014,442175,444673,444787,444907,445001,445322,445421,445539,445640,445870,446106,446316,446549,446973,447049,449557,450812,451256,453110,453567,453775,454785,455165,455306,455441,455638,455809,455992,456167,456354,456576,456734,456818,456922,457409,457965,458123,458342,458573,458796,459031,459253,459519,459657,460256,460370,460508,460620,460744,461315,461810,462356,462501,462594,462686,464613,465056,465354,465543,465749,465942,466152,467036,467181,467573,467731,467948,468209,480059,480491,481317,481937,482134,483082,483847,483970,484743,484964,485164,487141,487241,487331,487702,488001,488312,488615,488930,489499,490250,491325,491872,492430,492596,493993,494609,494845,495066,495856,496251,496487,497756,498205,498392,498641,498883,499059,499300,499533,499758,500100,500313,500408,500669,501524,501977,502696,503144,503739,504191,504398,504855,505564,506021,506171,506295,506441,506579,506715,507003,507304,507606,507722,507844,507956,508105,508363,508625,508883,509143,509391,509643,509891,510141,510385,510633,510877,511123,511355,511591,511823,512057,512169,512360,513219,513817,514013,514250,514518,514734,514952,515224,515523,515715,515927,516219,516483,516772,516991,517516,517753,518045,518784,519022,519291,519431,519601,519745,519847,520529,520869,521491,521717,522014,522738,523002,523317,524483,525043,527138,527332,527550,527776,527988,528663,528928,529024,529371,529459,529567,529675,529969,530275,530573,530883,532962,533150,533411,533660,535637,535829,536094,536347,536863,537213,537382,537896,538131,538255,538667,538881,539283,539386,539516,539691,539943,540139,540279,540473,540971,541852,542140,542270,543047,543704,543850,544556,544794,546334,546484,546901,547066,547752,548222,548418,548509,548593,548737,548971,549138,550066,550352,550512,551127,551286,551614,551841,552353,552715,552794,553133,553238,553603,553974,554335,556157,556786,557862,558286,558539,558691,559681,560418,560621,560867,561114,561332,561574,561895,562159,562464,562687,562859,563400,563669,564163,564389,564829,564988,565272,566017,566382,566687,566845,567083,568402,568800,569028,569248,569390,570680,570786,570916,571054,571178,571466,571635,571735,572020,572134,573017,573772,574211,574335,574581,574774,574908,575099,575878,576096,576387,576666,576983,577205,577500,577783,577887,578170,578902,579218,579779,580285,580490,581219,581494,582155,582344,582895,583461,583581,583983,590650,591123,591422,610528,611622,613216,614363,614554,619491,621786,622226,622978,623162,623373,627814,652310,652682,652884,654019,654169,656756,662420,670038,673312,673619,674127,674402,676129,676349,676931,677426,678463,678679,679012,679168,679361,679590,679798,679934,688749,689047,690259,691127,697312,697428,698652,698789,700018,701156,703549,705117,705365,712708,712824,712999,716889,717036,724972,727346", "endLines": "15,23,39,40,41,42,46,47,48,49,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,101,102,122,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,201,203,211,212,213,214,215,216,218,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,383,388,389,390,411,426,435,441,442,443,444,445,460,462,464,465,473,474,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,515,516,528,536,538,571,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,814,815,816,817,818,819,820,821,822,823,824,825,848,943,944,951,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1539,1540,1651,1652,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1676,1679,1680,1681,1699,1700,1701,1702,1703,1704,1705,1716,1727,1728,1732,1733,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1781,1794,1825,1826,1827,1861,1862,1863,1864,1865,1896,1955,1956,1957,1968,1974,1980,1981,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2059,2060,2061,2062,2063,2102,2117,2121,2175,2182,2190,2362,2371,2380,2389,2450,2451,2452,2453,2454,2455,2456,2457,2462,2463,2467,2468,2474,2478,2479,2480,2481,2491,2492,2493,2497,2498,2504,2508,2509,2584,2585,2588,2591,2592,2593,2594,2794,2800,2998,3198,3204,3402,3465,3547,3599,3681,3743,3825,3889,3941,4023,4030,4041,4045,4049,4062,4838,4845,4851,4863,4882,4899,4904,4909,4916,4926,4939,4953,4957,4964,4968,4974,4985,4988,4992,5007,5008,5056,5060,5064,5068,5069,5070,5073,5087,5094,5108,5150,5151,5200,5374,5378,5383,5390,5396,5397,5400,5404,5409,5421,5425,5430,5435,5440,5443,5446,5449,5453,5457,5601,5602,5603,5604,5689,5695,5699,5705,5709,5713,5719,5725,5729,5733,5737,5738,5739,5740,5741,5742,5743,5744,5748,5752,5755,5759,5762,5765,5768,5771,5774,5778,5782,5783,5786,5789,5792,5795,5798,5801,5804,5807,5811,5814,5818,5821,5824,5827,5830,5833,5836,5840,5843,5846,5864,5867,5877,5885,5893,5896,5899,5902,5905,5908,5911,5986,5987,5990,5993,5994,5997,5998,5999,6003,6004,6009,6017,6025,6033,6041,6049,6057,6065,6073,6081,6090,6099,6108,6116,6125,6134,6137,6140,6141,6142,6143,6144,6145,6146,6147,6148,6149,6150,6151,6152,6155,6156,6157,6158,6159,6167,6175,6176,6184,6188,6196,6204,6212,6220,6228,6229,6237,6245,6246,6249,6252,6253,6256,6259,6336,6341,6343,6348,6352,6356,6370,6371,6372,6376,6380,6381,6385,6386,6387,6388,6389,6390,6391,6392,6393,6394,6395,6396,6397,6398,6402,6406,6407,6411,6412,6417,6418,6419,6420,6421,6422,6423,6424,6425,6426,6427,6428,6429,6430,6431,6432,6433,6434,6435,6436,6437,6441,6442,6443,6449,6450,6454,6456,6457,6460,6465,6466,6467,6468,6469,6470,6474,6475,6476,6482,6483,6487,6489,6492,6496,6500,6504,6508,6559,6560,6561,6564,6567,6570,6573,6576,6580,6581,6586,6590,6595,6599,6604,6608,6612,6645,6646,6647,6648,6652,6653,6654,6655,6659,6663,6667,6671,6677,6678,6711,6724,6729,6755,6761,6764,6775,6780,6783,6786,6789,6792,6795,6798,6801,6805,6808,6809,6810,6818,6826,6829,6834,6839,6844,6849,6853,6857,6858,6866,6867,6868,6869,6870,6878,6883,6888,6889,6890,6891,6916,6921,6926,6929,6933,6936,6940,6950,6953,6958,6961,6965,6969,6972,7143,7156,7169,7173,7188,7199,7202,7213,7218,7222,7257,7258,7259,7266,7270,7274,7278,7282,7293,7309,7323,7330,7339,7342,7362,7372,7376,7380,7391,7397,7401,7418,7426,7430,7434,7438,7441,7445,7449,7453,7460,7464,7465,7469,7490,7500,7520,7529,7545,7555,7559,7569,7589,7599,7602,7603,7604,7605,7606,7610,7614,7618,7619,7620,7621,7624,7627,7630,7633,7636,7639,7642,7645,7648,7651,7654,7657,7660,7663,7666,7669,7672,7673,7676,7687,7695,7699,7704,7709,7712,7715,7721,7725,7728,7731,7735,7740,7744,7747,7755,7759,7763,7774,7779,7784,7785,7788,7791,7792,7802,7807,7815,7818,7822,7835,7838,7842,7857,7864,7890,7893,7896,7899,7902,7912,7918,7919,7924,7925,7926,7927,7931,7935,7939,7943,7972,7975,7979,7983,8011,8014,8018,8022,8031,8037,8040,8049,8053,8054,8061,8065,8072,8073,8074,8077,8082,8087,8088,8092,8100,8115,8119,8120,8132,8142,8143,8155,8160,8184,8187,8193,8196,8205,8213,8217,8220,8223,8226,8230,8233,8250,8254,8257,8272,8275,8283,8288,8295,8300,8301,8306,8307,8313,8319,8325,8356,8367,8384,8391,8395,8398,8410,8419,8423,8428,8432,8436,8440,8444,8448,8452,8456,8459,8468,8473,8482,8485,8492,8493,8497,8506,8512,8516,8517,8521,8542,8548,8552,8556,8557,8575,8576,8577,8578,8579,8584,8587,8588,8594,8595,8607,8619,8626,8627,8632,8637,8638,8642,8656,8661,8667,8673,8679,8684,8690,8696,8697,8702,8716,8721,8730,8739,8742,8755,8758,8769,8773,8782,8791,8792,8799,8807,8993,9003,9050,9593,9639,9675,9681,9736,9911,9931,9952,9958,9966,10065,10079,10342,10348,10392,10396,10402,10462,10655,10730,10818,10826,10834,10888,10897,10909,10921,10959,10967,10979,10985,10991,10996,11001,11007,11042,11235,11277,11300,11361,11461,11467,11497,11501,11574,11592,11745,11753,11773,11949,11957,12048,12072,12296,12304,12369", "endColumns": "55,48,55,59,60,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,46,55,48,57,55,50,59,48,55,55,49,58,46,55,53,53,53,48,57,55,46,53,53,47,56,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,63,70,71,50,52,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,58,61,61,66,56,55,55,57,57,56,56,58,58,57,53,55,9,52,57,57,57,45,59,53,67,68,67,52,51,49,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,53,57,50,44,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,86,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,83,93,87,97,83,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,84,94,88,98,84,100,86,96,65,75,68,78,64,65,52,75,65,86,75,75,41,46,64,54,49,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,53,52,53,58,45,56,66,55,64,73,83,72,64,61,55,82,88,63,78,73,58,55,55,59,59,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,74,56,83,56,74,74,50,67,49,61,59,56,59,48,80,56,60,58,59,57,60,57,49,48,66,58,58,48,74,70,68,62,67,65,67,64,65,76,77,63,78,88,77,65,68,65,97,95,95,97,108,55,91,48,53,53,53,53,53,54,109,109,109,109,109,109,109,109,105,105,105,105,94,94,94,94,105,105,105,105,94,94,94,94,107,107,107,107,96,96,96,96,107,107,107,107,97,95,95,97,64,87,63,61,61,67,57,62,65,62,66,71,65,51,59,57,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,52,52,67,76,64,60,59,74,73,76,72,69,71,69,72,63,69,47,68,51,84,82,57,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,62,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,55,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,54,61,52,56,93,68,100,50,69,62,57,69,68,69,69,69,66,66,74,66,58,53,53,53,52,51,73,40,64,73,72,67,59,57,60,65,65,64,63,60,136,139,48,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,54,57,54,58,55,60,62,60,60,60,60,60,60,60,58,60,60,60,60,60,60,50,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,69,46,51,49,56,12,149,130,237,97,114,84,47,78,64,88,156,156,152,153,58,39,95,89,95,89,165,124,124,169,109,120,120,109,111,122,122,81,173,147,158,154,172,116,116,167,111,113,173,177,132,111,145,151,131,142,121,177,181,316,181,181,189,189,198,156,109,182,136,219,183,159,157,183,202,170,219,221,154,199,183,102,140,164,170,199,203,201,204,200,198,203,160,77,300,165,154,101,10,10,10,10,10,10,10,10,10,10,84,120,98,86,122,100,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,90,10,10,10,10,108,118,10,10,10,10,10,94,30,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,10,10,10,10,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,10,10,10,10,10,10,10,10,10,52,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,60,10,10,10,10,10,10,10,10,75,111,89,10,10,98,10,75,111,89,101,107,107,99,84,103,86,77,113,91,10,10,100,10,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,113,119,93,10,98,117,100,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,94,10,10,10,10,10,10,10,10,10,10,10,10,123,145,137,135,10,10,10,115,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,101,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,95,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22", "endOffsets": "646,976,1611,1671,1732,1787,1986,2036,2089,2147,2195,2315,2363,2434,2506,2578,2651,2718,2767,2821,2858,2909,2956,3012,3061,3119,3175,3226,3286,3335,3391,3447,3497,3556,3603,3659,3713,3767,3821,3870,3928,3984,4031,4085,4139,4187,4244,4917,4973,5813,5923,5979,6039,6092,6153,6232,6313,6385,6464,6544,6620,6698,6767,6843,6920,6991,7064,7128,7199,7271,9672,9770,10126,10178,10228,10286,10351,10399,10515,16025,16091,16149,16218,16276,16345,16415,16488,16562,16630,16697,16767,16833,16906,16966,17042,17102,17162,17237,17305,17371,17439,17499,17558,17615,17681,17743,17800,17859,17921,17983,18050,18107,18163,18219,18277,18335,18392,18449,18508,18567,18625,18679,18735,18864,19123,19181,19239,20139,20687,21054,21394,21462,21531,21599,21652,22371,22467,22572,22622,22965,23012,23117,23175,23237,23300,23362,23421,23481,23546,23612,23677,23739,23801,23863,23925,23987,24049,24115,24182,24248,24311,24375,24438,24506,24567,24629,24691,24754,24818,24881,24945,25023,25082,25148,25228,25289,25342,25396,25560,25611,26096,26453,26561,28116,31396,31467,31533,31607,31676,31747,31820,31891,31959,32032,32108,32178,32256,32324,32390,32451,32520,32584,32650,32718,32784,32847,32915,32986,33051,33124,33187,33268,33332,33398,33468,33538,33608,33678,33745,35019,35077,35136,35196,35255,35314,35373,35432,35491,35550,35609,35668,35727,35786,35846,35907,35969,36030,36091,36152,36213,36274,36335,36396,36457,36518,36579,36647,36716,36786,36855,36924,36993,37062,37131,37200,37269,37338,37407,37476,37536,37597,37659,37720,37781,37842,37903,37964,38025,38086,38147,38208,38269,38331,38394,38458,38521,38584,38647,38710,38773,38836,38899,38962,39025,39088,39149,39211,39274,39336,39398,39460,39522,39584,39646,39708,39770,39832,39894,39951,40038,40118,40208,40303,40395,40487,40577,40660,40753,40840,40937,41028,41129,41216,41319,41408,41507,41599,41683,41777,41865,41963,42047,42147,42233,42329,42417,42498,42589,42685,42778,42871,42962,43047,43141,43230,43328,43421,43523,43611,43715,43806,43906,43999,44084,44179,44268,44367,44452,44553,44640,44737,45475,45551,45620,45699,45764,45830,45883,45959,46025,46112,46188,46264,47870,54246,54311,54747,55337,55391,55470,55548,55621,55686,55749,55815,55886,55957,56027,56089,56158,56224,56284,56351,56418,56474,56525,56578,56630,56684,56755,56818,56877,56939,56998,57071,57138,57208,57268,57331,57406,57478,57574,57645,57701,57772,57829,57886,57952,58016,58087,58144,58197,58260,58312,58370,58437,61755,61821,61880,61963,62022,62079,62146,62216,62290,62352,62421,62491,62545,62598,62652,62711,62757,62814,62881,62937,63002,63076,63160,63233,63298,63360,63416,63499,63588,63652,63731,63805,63864,63920,63976,64036,64096,64143,64203,64264,64328,64389,64449,64507,64550,64599,64651,64702,64754,64803,64852,64917,64983,65043,65104,65160,65219,65294,65351,65435,65492,65567,65642,65693,65761,65811,65873,65933,65990,66050,66099,66180,66237,66298,66357,66417,66475,66536,66594,66644,66693,66760,66819,66878,66927,67002,67073,67142,67205,67273,67339,67407,67472,67538,67615,67693,67757,67836,67925,68003,68069,68138,68204,68302,68398,68494,68592,68701,68757,68849,68898,68952,69006,69060,69114,69168,69223,69333,69443,69553,69663,69773,69883,69993,70103,70209,70315,70421,70527,70622,70717,70812,70907,71013,71119,71225,71331,71426,71521,71616,71711,71819,71927,72035,72143,72240,72337,72434,72531,72639,72747,72855,72963,73061,73157,73253,73351,73416,73504,73568,73630,73692,73760,73818,73881,73947,74010,74077,74149,74215,74267,74327,74385,74437,74494,74578,74673,74758,74839,74919,74996,75075,75152,75226,75300,75371,75451,75523,75598,75651,75704,75772,75849,75914,75975,76035,76110,76184,76261,76334,76404,76476,76546,76619,76683,76753,76801,76870,76922,77007,77090,77148,77214,77281,77347,77428,77503,77559,77612,77673,77731,77781,77830,77879,77928,77990,78042,78087,78168,78219,78273,78326,78380,78431,78480,78546,78597,78658,78719,78781,78831,78872,78949,79008,79067,79126,79187,79243,79299,79366,79427,79492,79547,79612,79681,79749,79827,79896,79956,80027,80101,80166,80238,80308,80375,80459,80528,80595,80665,80728,80795,80863,80946,81025,81115,81192,81260,81327,81405,81462,81519,81587,81653,81709,81769,81828,81882,81932,81982,82030,82092,82143,82216,82296,82376,82440,82503,82570,82641,82699,82760,82826,82885,82952,83012,83072,83135,83203,83264,83331,83409,83479,83528,83585,83654,83715,83803,83891,83979,84067,84123,84210,84297,84384,84471,84529,84603,84673,84729,84800,84865,84927,85002,85075,85165,85231,85297,85358,85422,85484,85542,85613,85696,85755,85826,85892,85957,86018,86077,86148,86214,86279,86362,86438,86513,86594,86654,86723,86793,86862,86917,86973,87029,87090,87148,87204,87259,87321,87374,87431,87525,87594,87695,87746,87816,87879,87937,88007,88076,88146,88216,88286,88353,88420,88495,88562,88621,88675,88729,88783,88836,88888,88962,90495,90560,90634,90707,90775,90835,90893,90954,91020,91086,91151,91215,91276,95706,95846,101566,101616,101811,101867,101925,101987,102042,102100,102171,102235,102294,102356,102422,102488,102900,103046,103091,103134,104120,104167,104212,104263,104314,104365,104416,104930,105548,105610,105858,105930,106211,106266,106324,106379,106438,106494,106555,106618,106679,106740,106801,106862,106923,106984,107045,107104,107165,107226,107287,107348,107409,107470,107521,107587,107653,107721,107789,107855,107922,107996,108059,108116,108176,108241,108308,108373,108430,108491,108549,108619,108666,108718,108768,108889,109846,112106,112237,112475,116227,116342,116427,116475,116554,119883,124509,124666,124823,125894,126353,126925,126965,127173,127263,127359,127449,127615,127740,127865,128035,128145,128266,128387,128497,128609,128732,128855,128937,129111,129259,129418,129573,129746,129863,129980,130148,130260,130374,130548,130726,130859,130971,131117,131269,131401,131544,131666,132238,132420,132737,132919,133101,133291,133481,133680,133837,133947,134130,134267,134487,134671,134831,134989,135173,135376,135547,135767,135989,136144,136344,136528,136631,136772,136937,137108,137308,137512,137714,137919,138120,138319,138523,138684,138878,139179,139345,139500,139602,143146,144193,144478,147879,148335,148844,159986,160423,160857,161300,165282,165403,165502,165589,165712,165813,165906,166013,166356,166463,166708,166829,167238,167486,167586,167691,167810,168319,168466,168585,168836,168969,169384,169638,169750,175325,175450,175667,175867,175988,176121,176268,190981,191417,205970,220719,221157,235746,240587,246004,250095,255526,260268,265645,269629,273621,279012,279503,280259,280489,280732,281865,330010,330474,330867,331537,332696,333693,333986,334285,334668,335443,336208,337095,337361,337837,338111,338508,339181,339359,339588,340513,340604,342798,343064,343386,343596,343705,343824,344008,344980,345450,346201,348678,348773,350955,360758,360986,361245,361821,362175,362297,362436,362710,362970,363840,364126,364529,364931,365274,365486,365687,365900,366189,366474,377346,377433,377518,377617,382604,382874,383045,383313,383478,383645,383914,384179,384347,384512,384678,384798,384918,385026,385136,385248,385356,385466,385631,385797,385976,386141,386296,386416,386577,386740,386901,387066,387233,387286,387419,387539,387637,387750,387869,387986,388141,388273,388502,388665,388858,388984,389136,389278,389448,389604,389776,390067,390179,390308,391481,391699,392554,393141,393755,393923,394065,394226,394369,394537,394694,401743,401835,402008,402170,402265,402434,402528,402617,402860,402949,403242,403707,404176,404646,405121,405587,406052,406518,406985,407448,407918,408391,408863,409323,409794,410266,410456,410635,410741,410849,410955,411067,411181,411293,411407,411523,411637,411745,411855,411963,412159,412267,412377,412485,412599,413008,413422,413538,413956,414197,414627,415062,415472,415894,416304,416426,416835,417251,417373,417591,417775,417836,418008,418180,423022,423366,423446,423802,423952,424096,424906,425018,425108,425370,425635,425734,425886,425962,426074,426164,426266,426374,426482,426582,426667,426771,426858,426936,427050,427142,427406,427673,427774,427927,428011,428400,428498,428606,428700,428830,428938,429060,429196,429304,429424,429558,429680,429808,429950,430076,430216,430342,430460,430592,430690,430800,431100,431212,431330,431794,431910,432213,432339,432435,432565,432966,433076,433200,433338,433448,433570,433882,434006,434136,434612,434740,435055,435193,435339,435501,435717,435873,436077,439401,439485,439589,439792,439981,440182,440375,440580,440785,440901,441096,441284,441496,441648,441867,442009,442170,444668,444782,444902,444996,445317,445416,445534,445635,445865,446101,446311,446544,446968,447044,449552,450807,451251,453105,453562,453770,454780,455160,455301,455436,455633,455804,455987,456162,456349,456571,456729,456813,456917,457404,457960,458118,458337,458568,458791,459026,459248,459514,459652,460251,460365,460503,460615,460739,461310,461805,462351,462496,462589,462681,464608,465051,465349,465538,465744,465937,466147,467031,467176,467568,467726,467943,468204,468335,480486,481312,481932,482129,483077,483842,483965,484738,484959,485159,487136,487236,487326,487697,487996,488307,488610,488925,489494,490245,491320,491867,492425,492591,493988,494604,494840,495061,495851,496246,496482,497751,498200,498387,498636,498878,499054,499295,499528,499753,500095,500308,500403,500664,501519,501972,502691,503139,503734,504186,504393,504850,505559,506016,506166,506290,506436,506574,506710,506998,507299,507601,507717,507839,507951,508100,508358,508620,508878,509138,509386,509638,509886,510136,510380,510628,510872,511118,511350,511586,511818,512052,512164,512355,513214,513812,514008,514245,514513,514729,514947,515219,515518,515710,515922,516214,516478,516767,516986,517511,517748,518040,518779,519017,519286,519426,519596,519740,519842,520524,520864,521486,521712,522009,522733,522997,523312,524478,525038,527133,527327,527545,527771,527983,528658,528923,529019,529366,529454,529562,529670,529964,530270,530568,530878,532957,533145,533406,533655,535632,535824,536089,536342,536858,537208,537377,537891,538126,538250,538662,538876,539278,539381,539511,539686,539938,540134,540274,540468,540966,541847,542135,542265,543042,543699,543845,544551,544789,546329,546479,546896,547061,547747,548217,548413,548504,548588,548732,548966,549133,550061,550347,550507,551122,551281,551609,551836,552348,552710,552789,553128,553233,553598,553969,554330,556152,556781,557857,558281,558534,558686,559676,560413,560616,560862,561109,561327,561569,561890,562154,562459,562682,562854,563395,563664,564158,564384,564824,564983,565267,566012,566377,566682,566840,567078,568397,568795,569023,569243,569385,570675,570781,570911,571049,571173,571461,571630,571730,572015,572129,573012,573767,574206,574330,574576,574769,574903,575094,575873,576091,576382,576661,576978,577200,577495,577778,577882,578165,578897,579213,579774,580280,580485,581214,581489,582150,582339,582890,583456,583576,583978,584512,591118,591417,593762,611617,613211,614358,614549,616854,621781,622221,622973,623157,623368,627809,628392,652677,652879,654014,654164,654339,656976,662942,671285,673614,674122,674397,676124,676344,676926,677421,678458,678674,679007,679163,679356,679585,679793,679929,681701,689042,690254,691122,693010,697423,697592,698784,698930,701151,701682,705112,705360,706043,712819,712994,716140,717031,724967,725210,727712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0cc9e4ae2bfcac04a12a08baf379016\\transformed\\react-android-0.76.9-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,82,86,90,93,97,101,111,112,113,122,129,136,139,142,145,151,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,163,226,277,331,390,438,487,536,584,633,691,740,776,826,867,911,955,998,1032,1071,1117,1165,1219,1331,1448,1571,1667,1787,1907,2009,2149,2271,2381,2488,2591,2702,2871,3039,3156,3275,3388,3574,3682,3795,3886,3997,4166,4264,4389,4484,4591,4761,4859,5042,5215,5327,5428,5587,5721,5861,5963,6068,6199,6368,6485,6633,6778,6928,7027,7123,7319,7502,7601,7785,7952,8200,8448,8716,8901,9103,9309,9896,9922,9957,10495,10913,11291,11468,11647,11830,12195,12392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,81,85,89,92,96,100,104,111,112,121,128,135,138,141,144,150,153,163", "endColumns": "62,62,50,53,58,47,48,48,47,48,57,48,35,49,40,43,43,42,33,38,45,47,53,47,116,122,95,119,119,101,139,121,109,106,102,110,168,167,116,118,112,185,107,112,90,110,168,97,124,94,106,169,97,182,172,111,100,158,133,139,101,104,130,168,116,147,144,149,98,95,195,182,98,183,166,10,10,12,12,10,10,10,25,34,10,10,10,10,10,12,12,12,10", "endOffsets": "158,221,272,326,385,433,482,531,579,628,686,735,771,821,862,906,950,993,1027,1066,1112,1160,1214,1262,1443,1566,1662,1782,1902,2004,2144,2266,2376,2483,2586,2697,2866,3034,3151,3270,3383,3569,3677,3790,3881,3992,4161,4259,4384,4479,4586,4756,4854,5037,5210,5322,5423,5582,5716,5856,5958,6063,6194,6363,6480,6628,6773,6923,7022,7118,7314,7497,7596,7780,7947,8195,8443,8711,8896,9098,9304,9501,9917,9952,10490,10908,11286,11463,11642,11825,12190,12387,12828"}, "to": {"startLines": "604,605,1544,1545,1546,1579,1580,1581,1582,1583,1584,1585,1636,1645,1648,1654,1669,1673,1675,1707,1708,1715,1717,1718,1822,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1868,1967,1975,1976,1982,2018,2019,2020,2067,2069,2072,2073,2076,2077,2078,2080,2081,2083,2085,2086,2088,2091,2093,2094,2106,2110,5157,5161,5189,5193,5458,6266,6318,6319,6328,6357,6364,6367,6509,6512,6518,8823", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "30432,30495,96064,96115,96169,98138,98186,98235,98284,98332,98381,98439,100797,101241,101411,101683,102536,102743,102828,104456,104495,104839,104935,104989,111717,112991,113114,113210,113330,113450,113552,113692,113814,113924,114031,114134,114245,114414,114582,114699,114818,114931,115117,115225,115338,115429,115540,115709,115807,115932,116027,116678,125648,126358,126541,126970,131671,131772,131931,139782,139997,140203,140308,140538,140707,140824,141018,141163,141355,141494,141590,141857,142123,142312,142496,143425,143673,349054,349322,350409,350611,366479,418613,421968,422003,422541,424101,424479,424656,436082,436265,436630,585282", "endLines": "604,605,1544,1545,1546,1579,1580,1581,1582,1583,1584,1585,1636,1645,1648,1654,1669,1673,1675,1707,1708,1715,1717,1718,1822,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1868,1967,1975,1976,1982,2018,2019,2020,2067,2069,2072,2073,2076,2077,2078,2080,2081,2083,2085,2086,2088,2091,2093,2094,2109,2113,5160,5163,5192,5196,5461,6266,6318,6327,6334,6363,6366,6369,6511,6517,6520,8832", "endColumns": "62,62,50,53,58,47,48,48,47,48,57,48,35,49,40,43,43,42,33,38,45,47,53,47,116,122,95,119,119,101,139,121,109,106,102,110,168,167,116,118,112,185,107,112,90,110,168,97,124,94,106,169,97,182,172,111,100,158,133,139,101,104,130,168,116,147,144,149,98,95,195,182,98,183,166,10,10,12,12,10,10,10,25,34,10,10,10,10,10,12,12,12,10", "endOffsets": "30490,30553,96110,96164,96223,98181,98230,98279,98327,98376,98434,98483,100828,101286,101447,101722,102575,102781,102857,104490,104536,104882,104984,105032,111829,113109,113205,113325,113445,113547,113687,113809,113919,114026,114129,114240,114409,114577,114694,114813,114926,115112,115220,115333,115424,115535,115704,115802,115927,116022,116129,116843,125741,126536,126709,127077,131767,131926,132060,139917,140094,140303,140434,140702,140819,140967,141158,141308,141449,141585,141781,142035,142217,142491,142658,143668,143916,349317,349502,350606,350812,366671,418634,421998,422536,422954,424474,424651,424830,436260,436625,436822,585718"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1619f5c30df2cf616e20d2bf30ab0293\\transformed\\constraintlayout-2.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,8,9,17,18,19,20,21,22,23,27,28,29,30,38,46,47,48,53,54,59,64,65,66,71,72,77,78,83,84,85,91,92,93,98,103,104,105,109,110,111,112,115,116,119,122,123,124,125,126,129,132,133,134,135,140,143,146,147,148,153,154,155,158,161,162,165,168,171,174,175,176,179,182,183,188,189,194,197,200,201,202,203,204,205,206,207,208,209,221,222,223,224,225,231,232,233,236,241,242,243,244,253,259,260,264,265,266,267,276,356,280,281,282,283,284,285,286,287,288,289,290,291,296,297,298,299,300,301,302,308,309,315,322,334,335,336,337,338,355", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,112,298,359,650,702,750,801,853,914,960,1093,1145,1195,1246,1555,1867,1912,1971,2168,2225,2420,2602,2656,2713,2905,2963,3159,3215,3409,3466,3517,3739,3791,3846,4036,4212,4262,4318,4478,4539,4599,4669,4802,4870,4999,5125,5187,5252,5320,5387,5510,5635,5702,5767,5832,6013,6134,6255,6321,6388,6598,6667,6733,6858,6984,7051,7177,7304,7429,7556,7612,7677,7803,7926,7991,8199,8266,8446,8566,8686,8751,8813,8875,8937,8996,9056,9117,9178,9237,9738,9789,9838,9886,9944,10174,10221,10281,10387,10567,10613,10660,10712,11042,11280,11335,11474,11520,11575,11620,11961,36954,12098,16317,19922,20066,24372,24875,25295,25926,26578,26619,26673,26723,27345,27998,28496,31373,31719,31951,32091,32596,32726,33153,33548,34580,34812,34939,35045,35529,36651", "endLines": "2,7,8,16,17,18,19,20,21,22,26,27,28,29,37,45,46,47,52,53,58,63,64,65,70,71,76,77,82,83,84,90,91,92,97,102,103,104,108,109,110,111,114,115,118,121,122,123,124,125,128,131,132,133,134,139,142,145,146,147,152,153,154,157,160,161,164,167,170,173,174,175,178,181,182,187,188,193,196,199,200,201,202,203,204,205,206,207,208,220,221,222,223,224,230,231,232,235,240,241,242,243,252,258,259,263,264,265,266,275,279,356,280,281,282,283,284,285,286,287,288,289,290,295,296,297,298,299,300,301,307,308,314,321,333,334,335,336,337,354,355", "endColumns": "56,11,60,11,51,47,50,51,60,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,55,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11,50,48,47,57,11,46,59,11,11,45,46,51,11,11,54,11,45,54,44,11,11,40,4218,3604,143,4305,502,419,630,651,40,53,49,382,652,497,2876,345,231,139,35,129,133,35,35,231,126,105,483,35,302", "endOffsets": "107,293,354,645,697,745,796,848,909,955,1088,1140,1190,1241,1550,1862,1907,1966,2163,2220,2415,2597,2651,2708,2900,2958,3154,3210,3404,3461,3512,3734,3786,3841,4031,4207,4257,4313,4473,4534,4594,4664,4797,4865,4994,5120,5182,5247,5315,5382,5505,5630,5697,5762,5827,6008,6129,6250,6316,6383,6593,6662,6728,6853,6979,7046,7172,7299,7424,7551,7607,7672,7798,7921,7986,8194,8261,8441,8561,8681,8746,8808,8870,8932,8991,9051,9112,9173,9232,9733,9784,9833,9881,9939,10169,10216,10276,10382,10562,10608,10655,10707,11037,11275,11330,11469,11515,11570,11615,11956,12093,36990,16312,19917,20061,24367,24870,25290,25921,26573,26614,26668,26718,27340,27993,28491,31368,31714,31946,32086,32591,32721,33148,33543,34575,34807,34934,35040,35524,36646,36949"}, "to": {"startLines": "13,16,29,30,38,45,51,88,89,90,93,97,99,100,103,111,121,144,145,150,151,156,161,162,163,168,169,174,175,180,181,182,188,189,190,195,200,219,220,224,225,226,227,230,231,234,237,238,239,240,241,244,247,248,249,250,255,258,261,262,263,268,269,270,273,276,277,280,283,286,289,290,291,294,297,298,303,304,309,312,315,316,317,318,319,320,321,322,323,324,384,385,386,387,392,398,399,400,403,461,475,517,518,529,535,541,545,546,547,548,557,1655,10118,10119,10120,10121,10271,10645,10656,10657,10658,10659,10660,10661,10666,10667,10668,11208,11209,11210,11211,11217,11218,11362,11369,11456,11786,11807,12370,12371,12388", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "484,651,1156,1217,1508,1893,2200,4249,4301,4362,4528,4661,4767,4817,4978,5287,5710,7276,7335,7532,7589,7784,7966,8020,8077,8269,8327,8523,8579,8773,8830,8881,9103,9155,9210,9400,9576,10520,10576,10736,10797,10857,10927,11060,11128,11257,11383,11445,11510,11578,11645,11768,11893,11960,12025,12090,12271,12392,12513,12579,12646,12856,12925,12991,13116,13242,13309,13435,13562,13687,13814,13870,13935,14061,14184,14249,14457,14524,14704,14824,14944,15009,15071,15133,15195,15254,15314,15375,15436,15495,18869,18920,18969,19017,19304,19534,19581,19641,19747,22376,23017,25616,25668,26101,26339,26665,26804,26850,26905,26950,27291,101727,630055,634274,637879,638023,650149,662000,662947,663578,664230,664271,664325,664375,664997,665650,666148,686969,687315,687547,687687,688192,688322,693015,693410,697080,706360,707025,727717,728201,729323", "endLines": "13,20,29,37,38,45,51,88,89,90,96,97,99,100,110,118,121,144,149,150,155,160,161,162,167,168,173,174,179,180,181,187,188,189,194,199,200,219,223,224,225,226,229,230,233,236,237,238,239,240,243,246,247,248,249,254,257,260,261,262,267,268,269,272,275,276,279,282,285,288,289,290,293,296,297,302,303,308,311,314,315,316,317,318,319,320,321,322,323,335,384,385,386,387,397,398,399,402,407,461,475,517,526,534,535,544,545,546,547,556,560,1655,10118,10119,10120,10121,10271,10645,10656,10657,10658,10659,10660,10665,10666,10667,10668,11208,11209,11210,11216,11217,11223,11368,11380,11456,11786,11807,12370,12387,12388", "endColumns": "56,11,60,11,51,47,50,51,60,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,55,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11,50,48,47,57,11,46,59,11,11,45,46,51,11,11,54,11,45,54,44,11,11,40,4218,3604,143,4305,502,419,630,651,40,53,49,382,652,497,2876,345,231,139,35,129,133,35,35,231,126,105,483,35,302", "endOffsets": "536,832,1212,1503,1555,1936,2246,4296,4357,4403,4656,4708,4812,4863,5282,5594,5750,7330,7527,7584,7779,7961,8015,8072,8264,8322,8518,8574,8768,8825,8876,9098,9150,9205,9395,9571,9621,10571,10731,10792,10852,10922,11055,11123,11252,11378,11440,11505,11573,11640,11763,11888,11955,12020,12085,12266,12387,12508,12574,12641,12851,12920,12986,13111,13237,13304,13430,13557,13682,13809,13865,13930,14056,14179,14244,14452,14519,14699,14819,14939,15004,15066,15128,15190,15249,15309,15370,15431,15490,15958,18915,18964,19012,19070,19529,19576,19636,19742,19922,22417,23059,25663,25993,26334,26389,26799,26845,26900,26945,27286,27423,101763,634269,637874,638018,642324,650647,662415,663573,664225,664266,664320,664370,664992,665645,666143,669020,687310,687542,687682,688187,688317,688744,693405,694437,697307,706482,707126,728196,729318,729621"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2d10be2ca6f02e3c7fd72b76301c06b9\\transformed\\glide-4.16.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "1640", "startColumns": "4", "startOffsets": "100977", "endColumns": "57", "endOffsets": "101030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7684a13bf28674155e26ec50b0113afc\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "1646,1670", "startColumns": "4,4", "startOffsets": "101291,102580", "endColumns": "53,66", "endOffsets": "101340,102642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bc7e9b6dca1567bc491d9562fda28106\\transformed\\viewpager2-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "24", "endOffsets": "160"}, "to": {"startLines": "12427", "startColumns": "4", "startOffsets": "731277", "endLines": "12429", "endColumns": "24", "endOffsets": "731382"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\expo-notifications\\android\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "1951", "startColumns": "4", "startOffsets": "124062", "endColumns": "82", "endOffsets": "124140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b825b073e97da6f5b520ec2775c042b7\\transformed\\fragment-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "1637,1682,1723,10463,10468", "startColumns": "4,4,4,4,4", "startOffsets": "100833,103139,105244,656981,657151", "endLines": "1637,1682,1723,10467,10471", "endColumns": "56,64,63,24,24", "endOffsets": "100885,103199,105303,657146,657295"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\222c8bd50fbb99823117f37113651644\\transformed\\standard-core-1.6.49\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,94", "endLines": "2,19", "endColumns": "38,12", "endOffsets": "89,673"}, "to": {"startLines": "570,5172", "startColumns": "4,4", "startOffsets": "28020,349825", "endLines": "570,5188", "endColumns": "38,12", "endOffsets": "28054,350404"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ae58c5a64fce4f723365c9edcca52b81\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "44,600,601,602,603,948,949,950,2150,5164,5166,5169,9747", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1841,30183,30244,30306,30368,54527,54586,54643,146341,349507,349571,349697,617197", "endLines": "44,600,601,602,603,948,949,950,2156,5165,5168,5171,9774", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "1888,30239,30301,30363,30427,54581,54638,54692,146750,349566,349692,349820,618116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c057469839ed01cf70753c0e493d65de\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "1719", "startColumns": "4", "startOffsets": "105037", "endColumns": "42", "endOffsets": "105075"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\01f5868e1a3bbc746a0df16a210183c2\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "1721", "startColumns": "4", "startOffsets": "105140", "endColumns": "53", "endOffsets": "105189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6770489b2150f304c763c4521df37a82\\transformed\\swiperefreshlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "24", "endOffsets": "287"}, "to": {"startLines": "11876", "startColumns": "4", "startOffsets": "709511", "endLines": "11879", "endColumns": "24", "endOffsets": "709677"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e50a7041aff24bf45b5302e9c4f92086\\transformed\\activity-1.7.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "1674,1720", "startColumns": "4,4", "startOffsets": "102786,105080", "endColumns": "41,59", "endOffsets": "102823,105135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\05887ea86031039b7ecbf23129389746\\transformed\\firebase-messaging-24.0.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "1961", "startColumns": "4", "startOffsets": "125043", "endColumns": "81", "endOffsets": "125120"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fc222b694c9ca04a566560b361d90b0\\transformed\\core-splashscreen-1.2.0-alpha02\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,22,25,34,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,104,164,223,292,364,427,499,573,649,725,802,873,942,1013,1081,1162,1254,1347,1456,1623,2083,2858", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,24,33,46,50", "endColumns": "48,59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "99,159,218,287,359,422,494,568,644,720,797,868,937,1008,1076,1157,1249,1342,1451,1618,2078,2853,3126"}, "to": {"startLines": "210,412,463,561,562,563,564,1467,1468,1469,1470,1471,1472,1473,1731,2510,2511,2512,5152,5154,6521,6530,6543", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10029,20144,22472,27428,27497,27569,27632,89945,90019,90095,90171,90248,90319,90388,105732,169755,169836,169928,348778,348887,436827,437287,438062", "endLines": "210,412,463,561,562,563,564,1467,1468,1469,1470,1471,1472,1473,1731,2510,2511,2512,5153,5156,6529,6542,6546", "endColumns": "48,59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "10073,20199,22526,27492,27564,27627,27699,90014,90090,90166,90243,90314,90383,90454,105795,169831,169923,170016,348882,349049,437282,438057,438330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8ee05b4940e071ad5d1d7c7e68ff5e05\\transformed\\autofill-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,19,20,27,32,37,44,53", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,934,994,1376,1656,1938,2322,2820", "endLines": "2,18,19,26,31,36,43,52,66", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "118,929,989,1371,1651,1933,2317,2815,3867"}, "to": {"startLines": "945,6302,7100,7101,7108,7113,7118,7125,9549", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "54316,421157,478034,478094,478476,478756,479038,479422,609942", "endLines": "945,6317,7100,7107,7112,7117,7124,7133,9562", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "54379,421963,478089,478471,478751,479033,479417,479915,610523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8e9bc6c234e70326f0babefb2238fc4f\\transformed\\android-maps-utils-3.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3,7,10", "startColumns": "4,4,4,4", "startOffsets": "55,93,301,461", "endLines": "2,6,9,14", "endColumns": "37,12,12,12", "endOffsets": "88,296,456,708"}, "to": {"startLines": "1590,8811,8815,8818", "startColumns": "4,4,4,4", "startOffsets": "98693,584662,584870,585030", "endLines": "1590,8814,8817,8822", "endColumns": "37,12,12,12", "endOffsets": "98726,584865,585025,585277"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9208c5cce24d430e4aba003c6876b859\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "1722", "startColumns": "4", "startOffsets": "105194", "endColumns": "49", "endOffsets": "105239"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\values\\values.xml", "from": {"startLines": "2,3,6,9,10,13", "startColumns": "4,4,4,4,4,4", "startOffsets": "100,181,389,572,653,861", "endLines": "2,5,8,9,12,15", "endColumns": "80,12,12,80,12,12", "endOffsets": "176,384,567,648,856,1039"}, "to": {"startLines": "5847,5848,5851,5854,5855,5858", "startColumns": "4,4,4,4,4,4", "startOffsets": "390313,390394,390602,390785,390866,391074", "endLines": "5847,5850,5853,5854,5857,5860", "endColumns": "80,12,12,80,12,12", "endOffsets": "390389,390597,390780,390861,391069,391252"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "3,4,2,1", "startColumns": "2,2,2,2", "startOffsets": "117,162,70,14", "endColumns": "44,48,46,55", "endOffsets": "159,208,114,67"}, "to": {"startLines": "606,607,669,843", "startColumns": "4,4,4,4", "startOffsets": "30558,30605,34918,47489", "endColumns": "46,50,48,57", "endOffsets": "30600,30651,34962,47542"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f0ad3059e848ec2624bad4e9e6fc7d3\\transformed\\browser-1.6.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "592,593,594,595,946,947,1887,1958,1959,1960", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "29688,29746,29812,29875,54384,54455,119181,124828,124895,124974", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "29741,29807,29870,29932,54450,54522,119244,124890,124969,125038"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ac31c4583d570f588270ebf42a86bc82\\transformed\\transition-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "1638,1639,1668,1677,1678,1709,1710,1711,1712,1713", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "100890,100930,102493,102905,102960,104541,104595,104647,104696,104757", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "100925,100972,102531,102955,103002,104590,104642,104691,104752,104802"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b14b43fc1759186e6ca5ed92b9460e08\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "2095,2096", "startColumns": "4,4", "startOffsets": "142663,142719", "endColumns": "55,54", "endOffsets": "142714,142769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32b9987bc127d003de604ba580089c06\\transformed\\coordinatorlayout-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,6,16", "startColumns": "4,4,4,4", "startOffsets": "55,116,261,869", "endLines": "2,5,15,104", "endColumns": "60,12,24,24", "endOffsets": "111,256,864,6075"}, "to": {"startLines": "92,8808,10122,10128", "startColumns": "4,4,4,4", "startOffsets": "4467,584517,642329,642540", "endLines": "92,8810,10127,10211", "endColumns": "60,12,24,24", "endOffsets": "4523,584657,642535,647051"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\expo-media-library\\android\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "44", "endOffsets": "95"}, "to": {"startLines": "1979", "startColumns": "4", "startOffsets": "126826", "endColumns": "44", "endOffsets": "126866"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2d12819c5c8831c86b34929884541b49\\transformed\\play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "1736,1877", "startColumns": "4,4", "startOffsets": "106091,117888", "endColumns": "67,166", "endOffsets": "106154,118050"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c70b754ff3b176c20bb896d71baebb6b\\transformed\\camera-view-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "206,427,11452", "startColumns": "4,4,4", "startOffsets": "9877,20692,696937", "endLines": "209,434,11455", "endColumns": "11,11,24", "endOffsets": "10024,20994,697075"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\030dd61fccce7abad1860c46c9bc2833\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "1823", "startColumns": "4", "startOffsets": "111834", "endColumns": "82", "endOffsets": "111912"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7d870e92d279060797355c3afa632fe0\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,111,159,211", "endLines": "2,3,4,8", "endColumns": "55,47,51,24", "endOffsets": "106,154,206,426"}, "to": {"startLines": "120,123,959,10325", "startColumns": "4,4,4,4", "startOffsets": "5654,5818,55240,652199", "endLines": "120,123,959,10328", "endColumns": "55,47,51,24", "endOffsets": "5705,5861,55287,652305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b3921f3c0ec9fec3b31ebed685fc140\\transformed\\recyclerview-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,38", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,2084"}, "to": {"startLines": "413,1046,1047,1048,1056,1057,1058,1647,11474", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "20204,60694,60753,60801,61468,61543,61619,101345,697812", "endLines": "413,1046,1047,1048,1056,1057,1058,1647,11494", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "20255,60748,60796,60852,61538,61614,61686,101406,698647"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\node_modules\\expo-av\\android\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,119,191", "endColumns": "63,71,69", "endOffsets": "114,186,256"}, "to": {"startLines": "1892,1895,1897", "startColumns": "4,4,4", "startOffsets": "119528,119751,119888", "endColumns": "63,71,69", "endOffsets": "119587,119818,119953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b84b30b80c5949bd0f3610f30992b047\\transformed\\play-services-base-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "608,609,610,611,612,613,614,615,1869,1870,1871,1872,1873,1874,1875,1876,1878,1879,1880,1881,1882,1883,1884,1885,1886,10737,11593", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "30656,30746,30826,30916,31006,31086,31167,31247,116848,116953,117134,117259,117366,117546,117669,117785,118055,118243,118348,118529,118654,118829,118977,119040,119102,671490,701687", "endLines": "608,609,610,611,612,613,614,615,1869,1870,1871,1872,1873,1874,1875,1876,1878,1879,1880,1881,1882,1883,1884,1885,1886,10749,11611", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "30741,30821,30911,31001,31081,31162,31242,31322,116948,117129,117254,117361,117541,117664,117780,117883,118238,118343,118524,118649,118824,118972,119035,119097,119176,671800,702099"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1,2,3,4", "startColumns": "2,2,2,2", "startOffsets": "14,56,142,237", "endColumns": "41,85,94,92", "endOffsets": "53,139,234,327"}, "to": {"startLines": "1824,1952,1953,1954", "startColumns": "4,4,4,4", "startOffsets": "111917,124145,124233,124330", "endColumns": "43,87,96,94", "endOffsets": "111956,124228,124325,124420"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "1,8,13", "startColumns": "2,2,2", "startOffsets": "61,466,709", "endLines": "7,12,17", "endColumns": "10,10,10", "endOffsets": "463,706,1014"}, "to": {"startLines": "2122,5626,6267", "startColumns": "4,4,4", "startOffsets": "144483,378937,418639", "endLines": "2128,5630,6271", "endColumns": "10,10,10", "endOffsets": "144885,379177,418944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4d414adee14b24510c264c21c70961f0\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "119,202,514,565,566,573,574,575,576,577,578,579,582,583,584,585,586,587,588,589,590,591,596,597,651,652,653,654,655,656,665,666,667,668,802,803,804,805,806,807,808,809,810,811,812,813,829,830,831,832,833,834,835,836,837,838,839,840,841,842,844,845,846,847,849,850,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,1009,1010,1049,1050,1051,1052,1053,1054,1055,1487,1488,1489,1490,1491,1492,1493,1494,1586,1587,1588,1589,1643,1671,1672,1683,1714,1725,1726,1729,1730,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,2074,2098,2099,2103,2104,2105,2129,2137,2138,2142,2146,2157,2162,2191,2198,2202,2206,2211,2215,2219,2223,2227,2231,2235,2241,2245,2251,2255,2261,2265,2270,2274,2277,2281,2287,2291,2297,2301,2307,2310,2314,2318,2322,2326,2330,2331,2332,2333,2336,2339,2342,2345,2349,2350,2351,2352,2390,2393,2395,2397,2399,2404,2405,2409,2415,2419,2420,2422,2434,2435,2439,2445,2449,2513,2514,2518,2545,2549,2550,2554,4063,4235,4261,4432,4458,4489,4497,4503,4519,4541,4546,4551,4561,4570,4579,4583,4590,4609,4616,4617,4626,4629,4632,4636,4640,4644,4647,4648,4653,4658,4668,4673,4680,4686,4687,4690,4694,4699,4701,4703,4706,4709,4711,4715,4718,4725,4728,4731,4735,4737,4741,4743,4745,4747,4751,4759,4767,4779,4785,4794,4797,4808,4811,4812,4817,4818,5462,5531,5605,5606,5616,5625,5631,5633,5637,5640,5643,5646,5649,5652,5655,5658,5662,5665,5668,5671,5675,5678,5682,5912,5913,5914,5915,5916,5917,5918,5919,5920,5921,5922,5923,5924,5925,5926,5927,5928,5929,5930,5931,5932,5934,5936,5937,5938,5939,5940,5941,5942,5943,5945,5946,5948,5949,5951,5953,5954,5956,5957,5958,5959,5960,5961,5963,5964,5965,5966,5967,6260,6262,6264,6272,6273,6274,6275,6276,6277,6278,6279,6280,6281,6282,6283,6284,6286,6287,6288,6289,6290,6291,6292,6294,6298,6547,6548,6549,6550,6551,6552,6556,6557,6558,7005,7007,7009,7011,7013,7015,7016,7017,7018,7020,7022,7024,7025,7026,7027,7028,7029,7030,7031,7032,7033,7034,7035,7038,7039,7040,7041,7043,7045,7046,7048,7049,7051,7053,7055,7056,7057,7058,7059,7060,7061,7062,7063,7064,7065,7066,7068,7069,7070,7071,7073,7074,7075,7076,7077,7079,7081,7083,7085,7086,7087,7088,7089,7090,7091,7092,7093,7094,7095,7096,7097,7098,7099,8833,8908,8911,8914,8917,8931,8937,9051,9054,9083,9110,9119,9183,9737,9775,10090,10307,10669,10693,10731,11043,11064,11188,11442,11448,11468,11502,11774,11880,12049,12305,12389,12401,12430", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5599,9677,25466,27704,27759,28182,28246,28316,28377,28452,28528,28605,28843,28928,29010,29086,29162,29239,29317,29423,29529,29608,29937,29994,33750,33824,33899,33964,34030,34090,34638,34710,34783,34850,44742,44801,44860,44919,44978,45037,45091,45145,45198,45252,45306,45360,46490,46564,46643,46716,46790,46861,46933,47005,47078,47135,47193,47266,47340,47414,47547,47619,47692,47762,47875,47935,48755,48824,48893,48963,49037,49113,49177,49254,49330,49407,49472,49541,49618,49693,49762,49830,49907,49973,50034,50131,50196,50265,50364,50435,50494,50552,50609,50668,50732,50803,50875,50947,51019,51091,51158,51226,51294,51353,51416,51480,51570,51661,51721,51787,51854,51920,51990,52054,52107,52174,52235,52302,52415,52473,52536,52601,52666,52741,52814,52886,52930,52977,53023,53072,53133,53194,53255,53317,53381,53445,53509,53574,53637,53697,53758,53824,53883,53943,54005,54076,54136,58442,58528,60857,60947,61034,61122,61204,61287,61377,91281,91333,91391,91436,91502,91566,91623,91680,98488,98545,98593,98642,101152,102647,102694,103204,104807,105361,105425,105615,105675,109851,109925,109995,110073,110127,110197,110282,110330,110376,110437,110500,110566,110630,110701,110764,110829,110893,110954,111015,111067,111140,111214,111283,111358,111432,111506,111647,140439,142849,142927,143151,143239,143335,144890,145472,145561,145808,146089,146755,147040,148849,149326,149548,149770,150046,150273,150503,150733,150963,151193,151420,151839,152065,152490,152720,153148,153367,153650,153858,153989,154216,154642,154867,155294,155515,155940,156060,156336,156637,156961,157252,157566,157703,157834,157939,158181,158348,158552,158760,159031,159143,159255,159360,161305,161519,161665,161805,161891,162239,162327,162573,162991,163240,163322,163420,164077,164177,164429,164853,165108,170021,170110,170347,172371,172613,172715,172968,281870,292551,294067,304762,306290,308047,308673,309093,310354,311619,311875,312111,312658,313152,313757,313955,314535,315903,316278,316396,316934,317091,317287,317560,317816,317986,318127,318191,318556,318923,319599,319863,320201,320554,320648,320834,321140,321402,321527,321654,321893,322104,322223,322416,322593,323048,323229,323351,323610,323723,323910,324012,324119,324248,324523,325031,325527,326404,326698,327268,327417,328149,328321,328405,328741,328833,366676,371907,377622,377684,378262,378846,379182,379295,379524,379684,379836,380007,380173,380342,380509,380672,380915,381085,381258,381429,381703,381902,382107,394699,394783,394879,394975,395073,395173,395275,395377,395479,395581,395683,395783,395879,395991,396120,396243,396374,396505,396603,396717,396811,396951,397085,397181,397293,397393,397509,397605,397717,397817,397957,398093,398257,398387,398545,398695,398836,398980,399115,399227,399377,399505,399633,399769,399901,400031,400161,400273,418185,418331,418475,418949,419015,419105,419181,419285,419375,419477,419585,419693,419793,419873,419965,420063,420173,420225,420303,420409,420501,420605,420715,420837,421000,438335,438415,438515,438605,438715,438805,439046,439140,439246,470378,470478,470590,470704,470820,470936,471030,471144,471256,471358,471478,471600,471682,471786,471906,472032,472130,472224,472312,472424,472540,472662,472774,472949,473065,473151,473243,473355,473479,473546,473672,473740,473868,474012,474140,474209,474304,474419,474532,474631,474740,474851,474962,475063,475168,475268,475398,475489,475612,475706,475818,475904,476008,476104,476192,476310,476414,476518,476644,476732,476840,476940,477030,477140,477224,477326,477410,477464,477528,477634,477720,477830,477914,585723,588339,588457,588572,588652,589013,589246,593767,593845,595189,596550,596938,599781,616859,618121,628698,651627,669025,669776,671290,681706,682085,686363,696557,696786,697597,698935,706048,709682,716145,725215,729626,729966,731387", "endLines": "119,202,514,565,566,573,574,575,576,577,578,579,582,583,584,585,586,587,588,589,590,591,596,597,651,652,653,654,655,656,665,666,667,668,802,803,804,805,806,807,808,809,810,811,812,813,829,830,831,832,833,834,835,836,837,838,839,840,841,842,844,845,846,847,849,850,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,1009,1010,1049,1050,1051,1052,1053,1054,1055,1487,1488,1489,1490,1491,1492,1493,1494,1586,1587,1588,1589,1643,1671,1672,1683,1714,1725,1726,1729,1730,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,2074,2098,2099,2103,2104,2105,2136,2137,2141,2145,2149,2161,2167,2197,2201,2205,2210,2214,2218,2222,2226,2230,2234,2240,2244,2250,2254,2260,2264,2269,2273,2276,2280,2286,2290,2296,2300,2306,2309,2313,2317,2321,2325,2329,2330,2331,2332,2335,2338,2341,2344,2348,2349,2350,2351,2352,2392,2394,2396,2398,2403,2404,2408,2414,2418,2419,2421,2433,2434,2438,2444,2448,2449,2513,2517,2544,2548,2549,2553,2581,4234,4260,4431,4457,4488,4496,4502,4518,4540,4545,4550,4560,4569,4578,4582,4589,4608,4615,4616,4625,4628,4631,4635,4639,4643,4646,4647,4652,4657,4667,4672,4679,4685,4686,4689,4693,4698,4700,4702,4705,4708,4710,4714,4717,4724,4727,4730,4734,4736,4740,4742,4744,4746,4750,4758,4766,4778,4784,4793,4796,4807,4810,4811,4816,4817,4822,5530,5600,5605,5615,5624,5625,5632,5636,5639,5642,5645,5648,5651,5654,5657,5661,5664,5667,5670,5674,5677,5681,5685,5912,5913,5914,5915,5916,5917,5918,5919,5920,5921,5922,5923,5924,5925,5926,5927,5928,5929,5930,5931,5933,5935,5936,5937,5938,5939,5940,5941,5942,5944,5945,5947,5948,5950,5952,5953,5955,5956,5957,5958,5959,5960,5962,5963,5964,5965,5966,5967,6261,6263,6265,6272,6273,6274,6275,6276,6277,6278,6279,6280,6281,6282,6283,6285,6286,6287,6288,6289,6290,6291,6293,6297,6301,6547,6548,6549,6550,6551,6555,6556,6557,6558,7006,7008,7010,7012,7014,7015,7016,7017,7019,7021,7023,7024,7025,7026,7027,7028,7029,7030,7031,7032,7033,7034,7037,7038,7039,7040,7042,7044,7045,7047,7048,7050,7052,7054,7055,7056,7057,7058,7059,7060,7061,7062,7063,7064,7065,7067,7068,7069,7070,7072,7073,7074,7075,7076,7078,7080,7082,7084,7085,7086,7087,7088,7089,7090,7091,7092,7093,7094,7095,7096,7097,7098,7099,8907,8910,8913,8916,8930,8936,8946,9053,9082,9109,9118,9182,9545,9740,9802,10117,10324,10692,10698,10736,11063,11187,11207,11447,11451,11473,11536,11785,11945,12068,12359,12400,12426,12436", "endColumns": "54,44,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "5649,9717,25502,27754,27816,28241,28311,28372,28447,28523,28600,28678,28923,29005,29081,29157,29234,29312,29418,29524,29603,29683,29989,30047,33819,33894,33959,34025,34085,34146,34705,34778,34845,34913,44796,44855,44914,44973,45032,45086,45140,45193,45247,45301,45355,45409,46559,46638,46711,46785,46856,46928,47000,47073,47130,47188,47261,47335,47409,47484,47614,47687,47757,47828,47930,47991,48819,48888,48958,49032,49108,49172,49249,49325,49402,49467,49536,49613,49688,49757,49825,49902,49968,50029,50126,50191,50260,50359,50430,50489,50547,50604,50663,50727,50798,50870,50942,51014,51086,51153,51221,51289,51348,51411,51475,51565,51656,51716,51782,51849,51915,51985,52049,52102,52169,52230,52297,52410,52468,52531,52596,52661,52736,52809,52881,52925,52972,53018,53067,53128,53189,53250,53312,53376,53440,53504,53569,53632,53692,53753,53819,53878,53938,54000,54071,54131,54199,58523,58610,60942,61029,61117,61199,61282,61372,61463,91328,91386,91431,91497,91561,91618,91675,91729,98540,98588,98637,98688,101181,102689,102738,103245,104834,105420,105482,105670,105727,109920,109990,110068,110122,110192,110277,110325,110371,110432,110495,110561,110625,110696,110759,110824,110888,110949,111010,111062,111135,111209,111278,111353,111427,111501,111642,111712,140487,142922,143012,143234,143330,143420,145467,145556,145803,146084,146336,147035,147428,149321,149543,149765,150041,150268,150498,150728,150958,151188,151415,151834,152060,152485,152715,153143,153362,153645,153853,153984,154211,154637,154862,155289,155510,155935,156055,156331,156632,156956,157247,157561,157698,157829,157934,158176,158343,158547,158755,159026,159138,159250,159355,159472,161514,161660,161800,161886,162234,162322,162568,162986,163235,163317,163415,164072,164172,164424,164848,165103,165197,170105,170342,172366,172608,172710,172963,175119,292546,294062,304757,306285,308042,308668,309088,310349,311614,311870,312106,312653,313147,313752,313950,314530,315898,316273,316391,316929,317086,317282,317555,317811,317981,318122,318186,318551,318918,319594,319858,320196,320549,320643,320829,321135,321397,321522,321649,321888,322099,322218,322411,322588,323043,323224,323346,323605,323718,323905,324007,324114,324243,324518,325026,325522,326399,326693,327263,327412,328144,328316,328400,328736,328828,329106,371902,377273,377679,378257,378841,378932,379290,379519,379679,379831,380002,380168,380337,380504,380667,380910,381080,381253,381424,381698,381897,382102,382432,394778,394874,394970,395068,395168,395270,395372,395474,395576,395678,395778,395874,395986,396115,396238,396369,396500,396598,396712,396806,396946,397080,397176,397288,397388,397504,397600,397712,397812,397952,398088,398252,398382,398540,398690,398831,398975,399110,399222,399372,399500,399628,399764,399896,400026,400156,400268,400408,418326,418470,418608,419010,419100,419176,419280,419370,419472,419580,419688,419788,419868,419960,420058,420168,420220,420298,420404,420496,420600,420710,420832,420995,421152,438410,438510,438600,438710,438800,439041,439135,439241,439333,470473,470585,470699,470815,470931,471025,471139,471251,471353,471473,471595,471677,471781,471901,472027,472125,472219,472307,472419,472535,472657,472769,472944,473060,473146,473238,473350,473474,473541,473667,473735,473863,474007,474135,474204,474299,474414,474527,474626,474735,474846,474957,475058,475163,475263,475393,475484,475607,475701,475813,475899,476003,476099,476187,476305,476409,476513,476639,476727,476835,476935,477025,477135,477219,477321,477405,477459,477523,477629,477715,477825,477909,478029,588334,588452,588567,588647,589008,589241,589758,593840,595184,596545,596933,599776,609829,616989,619486,630050,652194,669771,670033,671485,682080,686358,686964,696781,696932,697807,700013,706355,712703,716884,727341,729961,731272,731585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\448e2a47c2b2ada0bf8cd80bd6ec7e39\\transformed\\media-1.4.3\\res\\values\\values.xml", "from": {"startLines": "2,5,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,288,410,476,598,659,725", "endColumns": "88,61,65,121,60,65,66", "endOffsets": "139,345,471,593,654,720,787"}, "to": {"startLines": "828,1653,5973,5975,5976,5981,5983", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "46401,101621,400731,400907,401029,401291,401486", "endColumns": "88,61,65,121,60,65,66", "endOffsets": "46485,101678,400792,401024,401085,401352,401548"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eeb7a35c1d2dda21edaace2253c1f099\\transformed\\exoplayer-ui-2.18.1\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,15,16,20,21,22,23,24,25,26,27,28,29,30,35,42,43,44,45,46,47,52,53,54,55,56,57,58,59,60,61,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,208,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,261,265,269,273,277,281,285,289,290,296,307,311,315,319,323,327,331,335,339,343,347,351,362,367,372,377,388,396,406,410,414,418,421,437,463,498,527", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,539,588,714,763,812,871,925,977,1027,1092,1149,1196,1251,1399,1637,1686,1747,1807,1863,1923,2093,2153,2206,2263,2318,2374,2431,2480,2531,2590,2877,2942,3000,3049,3097,3148,3205,3262,3324,3391,3462,3534,3578,3635,3691,3754,3827,3897,3956,4013,4060,4115,4160,4209,4264,4318,4368,4419,4473,4532,4582,4640,4696,4749,4812,4877,4940,4992,5052,5116,5182,5240,5312,5373,5443,5513,5578,5643,5714,5802,5900,5996,6070,6146,6220,6302,6388,6474,6560,6638,6726,6812,6882,6974,7052,7132,7210,7296,7378,7471,7549,7640,7721,7810,7913,8014,8098,8194,8291,8386,8479,8571,8664,8757,8850,8933,9020,9115,9208,9289,9384,9477,9554,9598,9639,9684,9732,9776,9819,9868,9915,9959,10015,10068,10110,10157,10205,10265,10303,10353,10397,10447,10499,10537,10584,10631,10672,10711,10749,10793,10841,10883,10921,10963,11017,11064,11101,11150,11192,11233,11274,11316,11359,11397,11433,11511,11589,11886,12156,12238,12320,12462,12540,12627,12712,12779,12842,12934,13026,13091,13154,13216,13287,13397,13508,13618,13685,13765,13836,13903,13988,14073,14136,14224,14288,14430,14530,14578,14721,14784,14846,14911,14982,15040,15098,15164,15228,15294,15346,15408,15484,15560,15614,15893,16117,16320,16526,16729,16944,17153,17350,17388,17742,18529,18770,19010,19267,19520,19773,20008,20255,20494,20738,20959,21154,21729,22020,22316,22619,23188,23722,24196,24407,24607,24783,24891,25467,26406,27582,28638", "endLines": "10,11,12,13,14,15,19,20,21,22,23,24,25,26,27,28,29,34,41,42,43,44,45,46,51,52,53,54,55,56,57,58,59,60,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,207,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,260,264,268,272,276,280,284,288,289,295,306,310,314,318,322,326,330,334,338,342,346,350,361,366,371,376,387,395,405,409,413,417,420,436,462,497,526,565", "endColumns": "17,49,53,53,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,87,97,95,73,75,73,81,85,85,85,77,87,85,69,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22,22", "endOffsets": "330,380,434,488,534,583,709,758,807,866,920,972,1022,1087,1144,1191,1246,1394,1632,1681,1742,1802,1858,1918,2088,2148,2201,2258,2313,2369,2426,2475,2526,2585,2872,2937,2995,3044,3092,3143,3200,3257,3319,3386,3457,3529,3573,3630,3686,3749,3822,3892,3951,4008,4055,4110,4155,4204,4259,4313,4363,4414,4468,4527,4577,4635,4691,4744,4807,4872,4935,4987,5047,5111,5177,5235,5307,5368,5438,5508,5573,5638,5709,5797,5895,5991,6065,6141,6215,6297,6383,6469,6555,6633,6721,6807,6877,6969,7047,7127,7205,7291,7373,7466,7544,7635,7716,7805,7908,8009,8093,8189,8286,8381,8474,8566,8659,8752,8845,8928,9015,9110,9203,9284,9379,9472,9549,9593,9634,9679,9727,9771,9814,9863,9910,9954,10010,10063,10105,10152,10200,10260,10298,10348,10392,10442,10494,10532,10579,10626,10667,10706,10744,10788,10836,10878,10916,10958,11012,11059,11096,11145,11187,11228,11269,11311,11354,11392,11428,11506,11584,11881,12151,12233,12315,12457,12535,12622,12707,12774,12837,12929,13021,13086,13149,13211,13282,13392,13503,13613,13680,13760,13831,13898,13983,14068,14131,14219,14283,14425,14525,14573,14716,14779,14841,14906,14977,15035,15093,15159,15223,15289,15341,15403,15479,15555,15609,15888,16112,16315,16521,16724,16939,17148,17345,17383,17737,18524,18765,19005,19262,19515,19768,20003,20250,20489,20733,20954,21149,21724,22015,22311,22614,23183,23717,24191,24402,24602,24778,24886,25462,26401,27577,28633,29957"}, "to": {"startLines": "2,11,12,14,21,22,24,28,43,91,98,204,205,217,408,409,410,414,419,436,437,438,439,440,446,451,452,453,454,455,456,457,458,459,466,513,527,537,539,540,657,658,659,660,661,662,663,664,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1734,1735,1783,1787,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,5201,5206,5210,5214,5218,5222,5226,5230,5234,5235,5241,5252,5256,5260,5264,5268,5272,5276,5280,5284,5288,5292,5296,5307,5312,5317,5322,5333,5341,5351,5355,5359,9546,10291,11381,11407,11808,11837", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,541,837,883,981,1107,1792,4408,4713,9775,9827,10404,19927,19984,20031,20260,20408,21059,21108,21169,21229,21285,21657,21827,21887,21940,21997,22052,22108,22165,22214,22265,22627,25401,25998,26458,26566,26614,34151,34208,34265,34327,34394,34465,34537,34581,58615,58671,58734,58807,58877,58936,58993,59040,59095,59140,59189,59244,59298,59348,59399,59453,59512,59562,59620,59676,59729,59792,59857,59920,59972,60032,60096,60162,60220,60292,60353,60423,60493,60558,60623,91734,91822,91920,92016,92090,92166,92240,92322,92408,92494,92580,92658,92746,92832,92902,92994,93072,93152,93230,93316,93398,93491,93569,93660,93741,93830,93933,94034,94118,94214,94311,94406,94499,94591,94684,94777,94870,94953,95040,95135,95228,95309,95404,95497,98918,98962,99003,99048,99096,99140,99183,99232,99279,99323,99379,99432,99474,99521,99569,99629,99667,99717,99761,99811,99863,99901,99948,99995,100036,100075,100113,100157,100205,100247,100285,100327,100381,100428,100465,100514,100556,100597,100638,100680,100723,100761,105935,106013,108964,109261,119958,120040,120122,120264,120342,120429,120514,120581,120644,120736,120828,120893,120956,121018,121089,121199,121310,121420,121487,121567,121638,121705,121790,121875,121938,122026,122736,122878,122978,123026,123169,123232,123294,123359,123430,123488,123546,123612,123676,123742,123794,123856,123932,124008,350960,351239,351463,351666,351872,352075,352290,352499,352696,352734,353088,353875,354116,354356,354613,354866,355119,355354,355601,355840,356084,356305,356500,357075,357366,357662,357965,358534,359068,359542,359753,359953,609834,651051,694442,695381,707131,708187", "endLines": "10,11,12,14,21,22,27,28,43,91,98,204,205,217,408,409,410,418,425,436,437,438,439,440,450,451,452,453,454,455,456,457,458,459,472,513,527,537,539,540,657,658,659,660,661,662,663,664,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1734,1735,1786,1790,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,5205,5209,5213,5217,5221,5225,5229,5233,5234,5240,5251,5255,5259,5263,5267,5271,5275,5279,5283,5287,5291,5295,5306,5311,5316,5321,5332,5340,5350,5354,5358,5362,9548,10306,11406,11441,11836,11875", "endColumns": "17,49,53,53,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,87,97,95,73,75,73,81,85,85,85,77,87,85,69,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22,22", "endOffsets": "375,425,479,590,878,927,1102,1151,1836,4462,4762,9822,9872,10464,19979,20026,20081,20403,20641,21103,21164,21224,21280,21340,21822,21882,21935,21992,22047,22103,22160,22209,22260,22319,22909,25461,26051,26502,26609,26660,34203,34260,34322,34389,34460,34532,34576,34633,58666,58729,58802,58872,58931,58988,59035,59090,59135,59184,59239,59293,59343,59394,59448,59507,59557,59615,59671,59724,59787,59852,59915,59967,60027,60091,60157,60215,60287,60348,60418,60488,60553,60618,60689,91817,91915,92011,92085,92161,92235,92317,92403,92489,92575,92653,92741,92827,92897,92989,93067,93147,93225,93311,93393,93486,93564,93655,93736,93825,93928,94029,94113,94209,94306,94401,94494,94586,94679,94772,94865,94948,95035,95130,95223,95304,95399,95492,95569,98957,98998,99043,99091,99135,99178,99227,99274,99318,99374,99427,99469,99516,99564,99624,99662,99712,99756,99806,99858,99896,99943,99990,100031,100070,100108,100152,100200,100242,100280,100322,100376,100423,100460,100509,100551,100592,100633,100675,100718,100756,100792,106008,106086,109256,109526,120035,120117,120259,120337,120424,120509,120576,120639,120731,120823,120888,120951,121013,121084,121194,121305,121415,121482,121562,121633,121700,121785,121870,121933,122021,122085,122873,122973,123021,123164,123227,123289,123354,123425,123483,123541,123607,123671,123737,123789,123851,123927,124003,124057,351234,351458,351661,351867,352070,352285,352494,352691,352729,353083,353870,354111,354351,354608,354861,355114,355349,355596,355835,356079,356300,356495,357070,357361,357657,357960,358529,359063,359537,359748,359948,360124,609937,651622,695376,696552,708182,709506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fa08fd0869163705d58d4f365af34e4b\\transformed\\play-services-maps-18.2.0\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "167", "endLines": "66", "endColumns": "20", "endOffsets": "1669"}, "to": {"startLines": "10750", "startColumns": "4", "startOffsets": "671805", "endLines": "10812", "endColumns": "20", "endOffsets": "673307"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\generated\\res\\processReleaseGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,138,242,352,472,578", "endColumns": "82,103,109,119,105,74", "endOffsets": "133,237,347,467,573,648"}, "to": {"startLines": "1962,1963,1964,1965,1966,2068", "startColumns": "4,4,4,4,4,4", "startOffsets": "125125,125208,125312,125422,125542,139922", "endColumns": "82,103,109,119,105,74", "endOffsets": "125203,125307,125417,125537,125643,139992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3d816cf54fe94cb3ebe3970169e0d2ed\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "8947,8963,8969,11787,11803", "startColumns": "4,4,4,4,4", "startOffsets": "589763,590188,590366,706487,706898", "endLines": "8962,8968,8978,11802,11806", "endColumns": "24,24,24,24,24", "endOffsets": "590183,590361,590645,706893,707020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e9aa3803274a6cf3eaa3a68ef58f1ff5\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1591,1592,1593,1642,1644,1724,1866,1867,1891,1893,1894,1977,1978,2057,2058,2070,2071,2075,2079,2082,2084,2089,2090,2092,5197,5363,5366", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "96228,96287,96346,96406,96466,96526,96586,96646,96706,96766,96826,96886,96946,97005,97065,97125,97185,97245,97305,97365,97425,97485,97545,97605,97664,97724,97784,97843,97902,97961,98020,98079,98731,98805,98863,101101,101186,105308,116559,116624,119462,119592,119693,126714,126766,138689,138751,140099,140149,140492,140972,141313,141454,142040,142087,142222,350817,360129,360240", "endLines": "1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1591,1592,1593,1642,1644,1724,1866,1867,1891,1893,1894,1977,1978,2057,2058,2070,2071,2075,2079,2082,2084,2089,2090,2092,5199,5365,5369", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "96282,96341,96401,96461,96521,96581,96641,96701,96761,96821,96881,96941,97000,97060,97120,97180,97240,97300,97360,97420,97480,97540,97600,97659,97719,97779,97838,97897,97956,98015,98074,98133,98800,98858,98913,101147,101236,105356,116619,116673,119523,119688,119746,126761,126821,138746,138800,140144,140198,140533,141013,141350,141489,142082,142118,142307,350924,360235,360430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a6591935515ee6cd5ec7881ab1d3c64e\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "391,580,581,598,599,826,827,952,953,954,955,956,957,958,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1541,1542,1543,1649,1650,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1706,1782,1828,1829,1830,1831,1832,1833,1834,2087,5968,5969,5974,5977,5982,7134,7135,9741,10080,10403,10436,10606,10639", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19244,28683,28755,30052,30117,46269,46338,54752,54822,54890,54962,55032,55093,55167,88967,89028,89089,89151,89215,89277,89338,89406,89506,89566,89632,89705,89774,89831,89883,95851,95923,95999,101452,101487,103250,103305,103368,103423,103481,103539,103600,103663,103720,103771,103821,103882,103939,104005,104039,104421,108894,112480,112547,112619,112688,112757,112831,112903,141786,400413,400530,400797,401090,401357,479920,479992,616994,628397,654344,656075,661151,661833", "endLines": "391,580,581,598,599,826,827,952,953,954,955,956,957,958,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1541,1542,1543,1649,1650,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1706,1782,1828,1829,1830,1831,1832,1833,1834,2087,5968,5972,5974,5980,5982,7134,7135,9746,10089,10435,10456,10638,10644", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "19299,28750,28838,30112,30178,46333,46396,54817,54885,54957,55027,55088,55162,55235,89023,89084,89146,89210,89272,89333,89401,89501,89561,89627,89700,89769,89826,89878,89940,95918,95994,96059,101482,101517,103300,103363,103418,103476,103534,103595,103658,103715,103766,103816,103877,103934,104000,104034,104069,104451,108959,112542,112614,112683,112752,112826,112898,112986,141852,400525,400726,400902,401286,401481,479987,480054,617192,628693,656070,656751,661828,661995"}}, {"source": "D:\\App\\Delivery-Partner\\Delivery-Partner-App\\android\\app\\build\\generated\\res\\resValues\\release\\values\\gradleResValues.xml", "from": {"startLines": "6", "startColumns": "4", "startOffsets": "159", "endColumns": "63", "endOffsets": "218"}, "to": {"startLines": "1780", "startColumns": "4", "startOffsets": "108773", "endColumns": "63", "endOffsets": "108832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7460a359c4c281b49833222722dfee74\\transformed\\play-services-wallet-18.1.3\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,17,20,28,31,39,54,73,98", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "172,249,316,384,457,519,585,652,715,779,834,993,1169,1642,1810,2283,3295,3690,4203", "endLines": "4,5,6,7,8,9,10,11,12,13,14,17,27,30,38,51,72,97,170", "endColumns": "76,66,67,72,61,65,66,62,63,54,52,70,8,8,8,8,20,20,20", "endOffsets": "248,315,383,456,518,584,651,714,778,833,886,1063,1641,1809,2282,3190,3689,4202,5978"}, "to": {"startLines": "851,852,853,854,855,856,857,858,859,860,861,2097,6973,6981,6984,6992,10272,12437,12462", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "47996,48077,48148,48220,48297,48363,48433,48504,48571,48639,48698,142774,468340,468817,468989,469466,650652,731590,732107", "endLines": "851,852,853,854,855,856,857,858,859,860,861,2097,6980,6983,6991,7004,10290,12461,12534", "endColumns": "80,70,71,76,65,69,70,66,67,58,56,74,8,8,8,8,20,20,20", "endOffsets": "48072,48143,48215,48292,48358,48428,48499,48566,48634,48693,48750,142844,468812,468984,469461,470373,651046,732102,733882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\00636ad238812e00d92433007e026a91\\transformed\\exoplayer-core-2.18.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "1924,1925,1926,1927,1928,1929,1930,1931,1932", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "122090,122160,122222,122287,122351,122428,122493,122583,122667", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "122155,122217,122282,122346,122423,122488,122578,122662,122731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\145a58c47b72462367ffa7d2f3c84c3b\\transformed\\drawee-3.2.0\\res\\values\\values.xml", "from": {"startLines": "2,136", "startColumns": "4,4", "startOffsets": "55,3906", "endLines": "135,218", "endColumns": "22,22", "endOffsets": "3901,5346"}, "to": {"startLines": "10472,11612", "startColumns": "4,4", "startOffsets": "657300,702104", "endLines": "10605,11694", "endColumns": "22,22", "endOffsets": "661146,703544"}}]}]}