import DashboardSlice from "./dashboard/dashboardSlice";
import OrdersSlice from "./orders/orderSlice";
import ProfileSlice from "./profile/profileSlice";
import { configureStore } from "@reduxjs/toolkit";

export const store = configureStore({
  reducer: {
    dashboard: DashboardSlice,
    profile: ProfileSlice,
    orders: OrdersSlice,
  },
});

export type Rootstate = ReturnType<typeof store.getState>;
export type AppDispacher = typeof store.dispatch;
