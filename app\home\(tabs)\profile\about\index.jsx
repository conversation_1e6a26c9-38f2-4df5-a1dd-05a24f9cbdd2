import { View, Text, TouchableOpacity, ScrollView } from "react-native";
import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import BackIcon from "@/assets/icons/BackArrow.svg";
import { SlideComponent } from "../help";
const index = () => {
  return (
    <SafeAreaView className="flex-1">
      <ScrollView>
        <View className="items-center justify-center relative mt-6">
          <TouchableOpacity
            className="absolute left-[3%]"
            onPress={() => {
              router.back();
            }}
          >
            <BackIcon />
          </TouchableOpacity>
          <View>
            <Text className="font-[600] text-[20px] leading-[30px]">About</Text>
          </View>
        </View>
        <View className="mt-8">
          <SlideComponent text={"Terms of Service"} />
        </View>
        <View className="">
          <SlideComponent text={"Privacy Policy"} />
        </View>
        <View className="">
          <SlideComponent text={"Licenses and Registrations"} />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default index;
