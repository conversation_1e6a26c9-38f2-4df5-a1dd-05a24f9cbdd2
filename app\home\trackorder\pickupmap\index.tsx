import * as Location from "expo-location";
import BackArrow from "../../../../assets/icons/BackArrow.svg";
import DirectionsIcon from "../../../../assets/Hometab/ic_outline-directions.svg";
import DotIcon from "../../../../assets/Hometab/doticon.svg";
import HeadSetIcon from "../../../../assets/Hometab/lucide_headset.svg";
import LocationIcon from "../../../../assets/Hometab/drop_location.svg";
import MapView, { Marker, Polyline } from "react-native-maps";
import MessageIcon from "../../../../assets/Hometab/ic_round-message.svg";
import PhoneIcon from "../../../../assets/Hometab/mingcute_phone-fill.svg";
import PopUpComponent from "../../../../component/PopUpModle/PopUpComponent";
import React, { useEffect, useRef, useState } from "react";
import VegIcon from "../../../../assets/Hometab/mdi_lacto-vegetarian.svg";
import { router, useLocalSearchParams } from "expo-router";
import { Linking } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from "react-native";

const index = () => {
  const [region, setRegion] = useState<any>(null);
  const mapRef = useRef<MapView>(null);
  const {
    id,
    shop_name,
    logo,
    banner_image,
    address,
    latitude,
    longitude,
    distance,
    IsOrderPickedUp,
    orderNumber,
    otp,
    invoice_no,
  } = useLocalSearchParams();

  // Parse latitude and longitude from params (they come as strings)
  const lat = parseFloat(latitude as string);
  const lng = parseFloat(longitude as string);

  const [showissueMenu, setshowissueMenu] = useState(false);
  const [userLocation, setUserLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);
  const [routeCoordinates, setRouteCoordinates] = useState<
    { latitude: number; longitude: number }[]
  >([]);

  // Function to get route directions
  const getDirections = async (
    startLat: number,
    startLng: number,
    destLat: number,
    destLng: number
  ) => {
    try {
      const origin = `${startLat},${startLng}`;
      const destination = `${destLat},${destLng}`;

      // You'll need to replace 'YOUR_API_KEY' with your actual Google Maps API key
      const apiKey = "YOUR_API_KEY"; // Replace with your Google Maps API key
      const url = `https://maps.googleapis.com/maps/api/directions/json?origin=${origin}&destination=${destination}&key=${apiKey}`;

      const response = await fetch(url);
      const data = await response.json();

      if (data.routes.length > 0) {
        const points = data.routes[0].overview_polyline.points;
        const decodedPoints = decodePolyline(points);
        setRouteCoordinates(decodedPoints);
      }
    } catch (error) {
      console.error("Error getting directions:", error);
    }
  };

  // Function to decode polyline points
  const decodePolyline = (encoded: string) => {
    const poly = [];
    let index = 0;
    const len = encoded.length;
    let lat = 0;
    let lng = 0;

    while (index < len) {
      let b;
      let shift = 0;
      let result = 0;
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlat = (result & 1) !== 0 ? ~(result >> 1) : result >> 1;
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlng = (result & 1) !== 0 ? ~(result >> 1) : result >> 1;
      lng += dlng;

      poly.push({
        latitude: lat / 1e5,
        longitude: lng / 1e5,
      });
    }
    return poly;
  };

  const requestLocationPermission = async () => {
    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Permission denied",
        "Allow location access to use this feature."
      );
      return;
    }

    let location = await Location.getCurrentPositionAsync({});
    const { latitude: userLat, longitude: userLng } = location.coords;

    // Store user location
    setUserLocation({ latitude: userLat, longitude: userLng });

    // Set region to include both user location and destination
    const newRegion = {
      latitude: lat || userLat,
      longitude: lng || userLng,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    };

    setRegion(newRegion);
    if (mapRef.current) {
      mapRef.current.animateToRegion(newRegion, 0);
    }

    // Get route directions if destination coordinates are available
    if (lat && lng) {
      getDirections(userLat, userLng, lat, lng);
    }
  };

  const openDirections = () => {
    if (!lat || !lng) {
      Alert.alert("Error", "Location coordinates not available");
      return;
    }

    const destination = `${lat},${lng}`;
    const url = `https://www.google.com/maps/dir/?api=1&destination=${destination}&travelmode=driving`;

    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url);
        } else {
          Alert.alert("Error", "Cannot open Google Maps");
        }
      })
      .catch((err) => Alert.alert("Error", err.message));
  };

  useEffect(() => {
    requestLocationPermission();
  }, []);

  return (
    <SafeAreaView className="flex-1 justify-between">
      <ScrollView>
        <View className="px-4 mt-3">
          <View className="flex-row relative items-center justify-start mt-4">
            <TouchableOpacity
              onPress={() => {
                router.back();
              }}
              className="absolute"
            >
              <View>
                <BackArrow />
              </View>
            </TouchableOpacity>
            <View className="flex-1 items-center justify-center">
              <Text className="font-[400] text-[19px] leading-[39px]">
                Go to pickup
              </Text>
              <Text className="">Order ID: {orderNumber}</Text>
            </View>
            <View className="relative">
              <TouchableOpacity
                onPress={() => {
                  setshowissueMenu(!showissueMenu);
                }}
              >
                <DotIcon />
              </TouchableOpacity>
              {showissueMenu && (
                <TouchableOpacity
                  onPress={() => {
                    router.push("/home/<USER>/pickupmap/issue");
                  }}
                  className="right-3 z-10 top-6 absolute flex-row space-x-2 h-[45px] w-[138px] items-center justify-center bg-[#000] rounded-[8px]"
                >
                  <HeadSetIcon />
                  <Text className="font-[400] font-Pop text-[#fff] leading-[21px] text-[14px]">
                    Report issue
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
        <View className="relative h-[400px] mt-7">
          <MapView
            style={StyleSheet.absoluteFill}
            region={region}
            provider={"google"}
            ref={mapRef}
            showsUserLocation
            zoomEnabled={true}
            onRegionChangeComplete={setRegion}
            key={1}
          >
            {lat && lng && (
              <Marker
                coordinate={{
                  latitude: lat,
                  longitude: lng,
                }}
                title={shop_name as string}
                description={address as string}
              />
            )}
          </MapView>
        </View>
        <View className="mt-6 px-4 flex-row items-start justify-between">
          <View
            className="flex-row space-x-6 flex-1"
            style={{
              alignItems: "flex-start",
            }}
          >
            <View className="">
              <LocationIcon />
            </View>
            <View className="space-y-4">
              <Text className="">{shop_name}</Text>
              <Text className="pr-6">{address}</Text>
              {/* <View className="flex-row items-center space-x-6 mt-4">
                <PhoneIcon />
                <MessageIcon />
              </View> */}
            </View>
          </View>
          <View className="items-center justify-center space-y-2">
            <TouchableOpacity onPress={openDirections}>
              <DirectionsIcon />
              <Text className="">Get Directions</Text>
            </TouchableOpacity>
          </View>
        </View>
        <View className="mt-5 p-4">
          <View className="items-center p-4 justify-center border-[#E9EAE9] border-[1px] rounded-[4px]">
            <Text className="">Share this OTP with your seller partner </Text>

            <View className="flex-row items-center space-x-4">
              <View className="mt-4 h-[24px] w-[24px] items-center justify-center border-[1px] border-[#00660A]">
                <Text className="">{String(otp).split("")[0]}</Text>
              </View>
              <View className="mt-4 h-[24px] w-[24px] items-center justify-center border-[1px] border-[#00660A]">
                <Text className="">{String(otp).split("")[1]}</Text>
              </View>
              <View className="mt-4 h-[24px] w-[24px] items-center justify-center border-[1px] border-[#00660A]">
                <Text className="">{String(otp).split("")[2]}</Text>
              </View>
              <View className="mt-4 h-[24px] w-[24px] items-center justify-center border-[1px] border-[#00660A]">
                <Text className="">{String(otp).split("")[3]}</Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
      <View className="my-4 px-4">
        <TouchableOpacity
          onPress={() => {
            router.push({
              pathname: "/home/<USER>/pickupmap/confirmpickup",
              params: {
                id: id,
                shop_name: shop_name,
                logo: logo,
                banner_image: banner_image,
                address: address,
                latitude: latitude,
                longitude: longitude,
                orderNumber: orderNumber,
                distance: distance,
                IsOrderPickedUp: IsOrderPickedUp,
                otp: otp,
                invoice_no: invoice_no,
              },
            });
          }}
          className="h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
        >
          <Text className="font-[400] font-Pop text-[16px] leading-[24px] text-[#fff]">
            Reached Pickup Location
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default index;
