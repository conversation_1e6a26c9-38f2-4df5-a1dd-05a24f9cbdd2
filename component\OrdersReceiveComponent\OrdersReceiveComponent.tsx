import ArrowIcon from "../../assets/Hometab/tabler_arrow-right.svg";
import ClockIcon from "../../assets/Hometab/zondicons_time.svg";
import CrossIcon from "../../assets/Hometab/maki_cross.svg";
import LocationMarkIcon1 from "../../assets/Hometab/drop_location.svg";
import LocationMarkIcon2 from "../../assets/Hometab/gridicons_location.svg";
import RBSheet from "react-native-raw-bottom-sheet";
import React, { forwardRef, useEffect, useRef, useState } from "react";
import RsIcon from "../../assets/Hometab/rs.svg";
import ScooterIcon from "../../assets/Hometab/ph_scooter-bold.svg";
import TickIcon from "../../assets/Hometab/mdi_tick.svg";
import useGetApiDatawithParam from "../../hooks/useGetApiDatawithParam";
import useGetDataApi from "../../hooks/useGetDataApi";
import useSetApiData from "../../hooks/useSetApiData";
import useTenStackMutate from "@/hooks/useTenStackMutate/useTenStackMutate";
import { router } from "expo-router";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";
import { useOrder } from "../../store/Order";

const OrdersReceiveComponent = ({
  id,
  mode,
  statusstate,
  invoice_no,
  orderNumber,
  PickUpAddress,
  DeliveryAddress,
  edt,
  delivery_fees,
}: any) => {
  const OrderDetailsBottomSheetRef = useRef();
  // const [status, setstatus] = useState(
  //   statusstate == 1 ? "Pending" : statusstate == 2 ? "Reject" : "Accept"
  // );
  const [status, setstatus] = useState(statusstate);
  const { setorderStatus } = useOrder((state) => state);
  const [min, setmin] = useState(60);
  // const inerval = () => {
  //   setInterval(() => {
  //     setmin((min) => {
  //       if (min > 0) {
  //         return min - 1;
  //       } else {
  //         if (status !== "Accept") {
  //           setorderStatus(id, "Reject");
  //           setstatus("Reject");
  //           clearInterval();
  //         }
  //       }
  //     });
  //   }, 1000);
  // };

  // useEffect(() => {
  //   inerval();
  // }, []);

  const { data, isLoading, triggerreFresh } = useGetApiDatawithParam({
    endpoint: "order/invoice_details",
    param: {
      invoice_no: invoice_no,
    },
  });
  // const { SetFunction: AcceptOrder } = useSetApiData({
  //   endpoint: "order/accept_order",
  // });
  // const { SetFunction: RejectOrder } = useSetApiData({
  //   endpoint: "order/reject_order",
  // });
  const { mutate: AcceptOrder, isPending: isAccepting } = useTenStackMutate({
    endpoint: "order/accept_order",
    invalidateQueriesKey: ["orders"],
  });
  const { mutate: RejectOrder, isPending: isRejecting } = useTenStackMutate({
    endpoint: "order/reject_order",
    invalidateQueriesKey: ["orders"],
  });

  return (
    <View className="p-5 border-[1px] rounded-[8px] border-[#E9EAE9]">
      {status === "Pending" && (
        <>
          <View className="">
            <Text className="">
              You have {min}sec to accept/reject the order
            </Text>
            <View className="bg-[#D9D9D9] w-full h-[8px] my-4 rounded-[10px]">
              <View
                style={{
                  width: `${((60 - min) / 60) * 100}%`,
                }}
                className="h-full bg-[#00660A] rounded-[10px]"
              />
            </View>
          </View>
          <View className="flex-row w-full items-center justify-between mb-4">
            <TouchableOpacity
              onPress={() => {
                RejectOrder(
                  {
                    invoice_no: invoice_no,
                  },
                  {
                    onSuccess: (response) => {
                      setorderStatus(id, "Reject");
                      setstatus("Reject");
                    },
                  }
                );
              }}
              className="flex-row items-center justify-center h-[44px] px-6 space-x-2 rounded-[4px] border-[#D92D20] border-[1px]"
            >
              <Text className="text-[#D92D20] font-[400] font-Pop leading-[24px] text-[16px]">
                {isRejecting ? "Rejecting..." : "Reject"}
              </Text>
              <CrossIcon />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                AcceptOrder(
                  { invoice_no: invoice_no },
                  {
                    onSuccess: (response) => {
                      setorderStatus(id, "Accept");
                      setstatus("Accept");
                    },
                  }
                );
              }}
              className="flex-row items-center justify-center h-[44px] px-6 space-x-2 bg-[#00660A] rounded-[4px]"
            >
              <Text className="text-[#fff] font-[400] font-Pop leading-[24px] text-[16px]">
                {isAccepting ? "Accepting..." : "Accept"}
              </Text>
              <TickIcon />
            </TouchableOpacity>
          </View>
        </>
      )}
      <View className="my-2">
        <Text className="font-[400] font-Pop text-[18px] leading-[27px]">
          Order ID -{" "}
          <Text className="font-[600] font-Pop text-[18px] leading-[27px]">
            {`#${orderNumber}`}
          </Text>
        </Text>
      </View>
      <View className="my-2 flex-row justify-between ">
        <View className="">
          <Text className="font-[400] font-Pop text-[18px] leading-[27px]">
            Order Earning -  {" "}
            <Text className="font-[600] font-Pop text-[18px] leading-[27px]">
              ₹ {delivery_fees}
            </Text>
          </Text>
        </View>
        <View className="w-[83px] h-[32px] rounded-[15px] bg-[#E0E8FF] items-center justify-center">
          <Text className="font-[400] font-Pop text-[16px] leading-[24px]">
            {mode}
          </Text>
        </View>
      </View>
      <View className="flex-row mt-4 justify-between" style={{}}>
        <View className="items-center justify-center">
          <View className="">
            <Text className="font-[400] text-[12px] leading-[16px] font-Pop text-[#001A03]">
              Order Earning
            </Text>
            <View className="flex-row items-center mt-2 space-x-2">
              <RsIcon />
              <Text className="font-[600] leading-[27px] font-Pop text-[18px] ">
                {delivery_fees}
              </Text>
            </View>
          </View>
        </View>
        <View className="h-full w-[2px] bg-[#939D94]"></View>
        {isLoading ? (
          <></>
        ) : (
          <>
            <View className="items-center justify-center">
              <View className="flex-row items-center space-x-2">
                <ScooterIcon />
                <Text className="pt-1 font-Pop font-[500] leading-[16px] text-[16px]">
                  {data?.info?.distance}km
                </Text>
              </View>
            </View>
          </>
        )}
        <View className="h-full w-[2px] bg-[#939D94]"></View>
        <View className="items-center justify-center">
          <View className="flex-row items-center space-x-2">
            <ClockIcon />
            <Text className="pt-1 font-Pop font-[500] leading-[16px] text-[16px]">
              {edt} mins
            </Text>
          </View>
        </View>
      </View>
      {(status == "Pending" || status == "Accept") && (
        <TouchableOpacity
          onPress={() => {
            OrderDetailsBottomSheetRef.current.open();
          }}
          className="mt-5 flex-row items-center space-x-3"
        >
          <Text className="font-[500] font-Pop leading-[24px] text-[16px]">
            View Details
          </Text>
          <ArrowIcon />
        </TouchableOpacity>
      )}

      {PickUpAddress?.map((data) => {
        return <PickUpCard data={data} />;
      })}

      <DropOffCard data={DeliveryAddress} />

      {status == "Accepted" ? (
        <>
          <TouchableOpacity
            onPress={() => {
              router.push({
                pathname: "home/trackorder",
                params: { invoice_no: invoice_no, orderNumber: orderNumber },
              });
            }}
            className="mt-6 h-[44px] items-center justify-center bg-[#00660A] rounded-[8px]"
          >
            <Text className="font-[600] font-Pop text-[16px] text-[#ffffff]">
              Continue
            </Text>
          </TouchableOpacity>
        </>
      ) : (
        <></>
      )}
      {status == "Rejected" && (
        <View className="flex-row items-center mt-5 justify-center h-[44px] rounded-[4px] border-[#D92D20] border-[1px]">
          <Text className="text-[#D92D20] font-[400] font-Pop leading-[24px] text-[16px]">
            Reject
          </Text>
          <CrossIcon />
        </View>
      )}

      <OrderDetailsBottomSheet
        ref={OrderDetailsBottomSheetRef}
        invoice_no={invoice_no}
      />
    </View>
  );
};
export const OrderDetailsBottomSheet = forwardRef((props, ref) => {
  const { data, isLoading, triggerreFresh } = useGetApiDatawithParam({
    endpoint: "order/invoice_details",
    param: {
      invoice_no: props?.invoice_no,
    },
  });
  return (
    <>
      <RBSheet
        ref={ref}
        customStyles={{
          container: {
            borderRadius: 20,
            height: 500,
          },
          draggableIcon: {
            backgroundColor: "#000",
          },
        }}
        useNativeDriver={true}
        customModalProps={{
          statusBarTranslucent: true,
        }}
        customAvoidingViewProps={{
          enabled: false,
        }}
      >
        <ScrollView>
          <View className="px-8">
            {isLoading ? (
              <></>
            ) : (
              <>
                <View className="flex-row mt-8 justify-between">
                  <View className="items-center justify-center">
                    <View className="">
                      <Text className="font-[400] text-[12px] leading-[16px] font-Pop text-[#001A03]">
                        Order Amount
                      </Text>

                      <View className="flex-row items-center mt-2 space-x-2">
                        <RsIcon />
                        <Text className="font-[600] leading-[27px] font-Pop text-[18px] ">
                          {data?.info?.order_amnt}
                        </Text>
                      </View>
                    </View>
                  </View>
                  <View className="h-full w-[2px] bg-[#939D94]"></View>
                  <View className="items-center justify-center">
                    <View className="flex-row items-center space-x-2">
                      <ScooterIcon />
                      <Text className="pt-1 font-Pop font-[500] leading-[16px] text-[16px]">
                        {data?.info?.distance}km
                      </Text>
                    </View>
                  </View>
                  <View className="h-full w-[2px] bg-[#939D94]"></View>
                  <View className="items-center justify-center">
                    <View className="flex-row items-center space-x-2">
                      <ClockIcon />
                      <Text className="pt-1 font-Pop font-[500] leading-[16px] text-[16px]">
                        {data?.info?.time} mins
                      </Text>
                    </View>
                  </View>
                </View>
              </>
            )}

            <View className="">
              {!isLoading &&
                data?.pickupAddress?.map((data) => {
                  return <PickUpCard data={data} />;
                })}
              {!isLoading && <DropOffCard data={data?.deliveryAddress} />}
            </View>

            {isLoading ? (
              <></>
            ) : (
              <>
                <View className="mt-6">
                  <View className="">
                    <Text className="">Earnings Details</Text>
                  </View>
                  <View className="px-4">
                    <View className="flex-row justify-between my-4">
                      <View className="">
                        <Text className="">Total</Text>
                      </View>
                      <View className="">
                        <Text className="">₹ {data?.info?.delivery_fees}</Text>
                      </View>
                    </View>
                    <View className="flex-row justify-between mb-4">
                      <View className="">
                        <Text className="">Order Mode</Text>
                      </View>
                      <View className="">
                        <Text className="">
                          {data?.info?.product_mode == 1 ? "COD" : "Online"}
                        </Text>
                      </View>
                    </View>
                    {/* <View className="flex-row justify-between mt-4">
                      <View className="">
                        <Text className="">Order Earning</Text>
                      </View>
                      <View className="">
                        <Text className="">₹ 120</Text>
                      </View>
                    </View> */}
                    {/* <View className="flex-row justify-between my-4">
                      <View className="">
                        <Text className="">First Mile Earning</Text>
                      </View>
                      <View className="">
                        <Text className="">₹ 2.0</Text>
                      </View>
                    </View>
                    <View className="flex-row justify-between ">
                      <View className="">
                        <Text className="">Last Mile Earning</Text>
                      </View>
                      <View className="">
                        <Text className="">₹ 39</Text>
                      </View>
                    </View> */}
                    {/* <View className="flex-row justify-between my-4">
                      <View className="">
                        <Text className="">Order Completion Bonus</Text>
                      </View>
                      <View className="">
                        <Text className="">₹ 3.0</Text>
                      </View>
                    </View> */}
                    <View className="flex-row justify-between">
                      <View className="">
                        <Text className="">Wait Time Pay</Text>
                      </View>
                      <View className="">
                        <Text className="">
                          ₹ {data?.info?.order_wait_time}
                        </Text>
                      </View>
                    </View>
                    {/* <View className="flex-row justify-between mt-4">
                      <View className="">
                        <Text className="">Long Distance return Bonus</Text>
                      </View>
                      <View className="">
                        <Text className="">₹ 19.0</Text>
                      </View>
                    </View> */}
                  </View>
                </View>
                {/* <View className="flex-row mt-8 justify-between">
                  <View className="items-center justify-center">
                    <Text className="font-[400] text-[12px] leading-[16px] font-Pop text-[#001A03]">
                      First Mile
                    </Text>
                    <Text className="font-[600] leading-[27px] font-Pop text-[18px] ">
                      0.371 KM
                    </Text>
                  </View>
                  <View className="h-full w-[2px] bg-[#939D94]"></View>
                  <View className="items-center justify-center">
                    <View className="">
                      <Text className="font-[400] text-[12px] leading-[16px] font-Pop text-[#001A03]">
                        Last mile
                      </Text>
                      <Text className="font-[600] leading-[27px] font-Pop text-[18px] ">
                        7.75 KM
                      </Text>
                    </View>
                  </View>
                  <View className="h-full w-[2px] bg-[#939D94]"></View>
                  <View className="items-center justify-center">
                    <View className="">
                      <Text className="font-[400] text-[12px] leading-[16px] font-Pop text-[#001A03]">
                        Total
                      </Text>
                      <Text className="font-[600] leading-[27px] font-Pop text-[18px] ">
                        8.121 KM
                      </Text>
                    </View>
                  </View>
                </View> */}
              </>
            )}
          </View>
        </ScrollView>
      </RBSheet>
    </>
  );
});
const PickUpCard = (data) => {
  return (
    <>
      <View className="flex-row space-x-2 mt-4">
        <View className="">
          <LocationMarkIcon2 />
        </View>
        <View className="">
          <Text className="">Pickup Location</Text>
          <Text className="">{data?.data?.shop_name}</Text>
          <Text className="pr-2">{data?.data?.address}</Text>
        </View>
      </View>
    </>
  );
};
const DropOffCard = (data) => {
  return (
    <>
      <View className="flex-row space-x-2 mt-5">
        <View className="">
          <LocationMarkIcon1 />
        </View>
        <View className="">
          <Text className="">Dropoff Location</Text>
          <Text className="">{data?.data?.receiver_name}</Text>
          <Text className="pr-2">{data?.data?.address}</Text>
        </View>
      </View>
    </>
  );
};

export default OrdersReceiveComponent;
