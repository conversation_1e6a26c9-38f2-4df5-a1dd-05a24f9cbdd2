import { View, Image, Text, TouchableOpacity, Linking } from "react-native";
import React from "react";
import Bgimg from "../../assets/images/image.png";
import PlayIcon from "../../assets/icons/playIcon.svg";
import TvIcon from "../../assets/icons/TV.svg";
import { LinearGradient } from "expo-linear-gradient";

const LearnCardComponent = (data) => {
  const openApp = (url, appName) => {
    Linking.canOpenURL(url)
      .then((supported) => {
        if (!supported) {
          Alert.alert(`${appName} is not installed on this device`);
        } else {
          return Linking.openURL(url);
        }
      })
      .catch((err) => console.error("An error occurred", err));
  };
  const openVideoLink = (videoUrl) => {
    openApp(videoUrl, "Video Player");
  };
  console.log(data?.image)
  return (
    <View
      className="relative mt-4"
      style={{ borderRadius: 12, overflow: "hidden" }}
    >
      <LinearGradient
        colors={["transparent", "#000"]}
        start={[0, 0]}
        end={[0, 1.4]}
        style={{
          position: "absolute",
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
          zIndex: 1,
          flex: 1,
        }}
      />
      <Image
        source={{ uri: data?.data?.image }}
        className="w-[100%] h-[300px] object-fill"
      />
      <View className="absolute bottom-0 z-10">
        <View className="p-4">
          <Text className="mb-2 font-[700] text-[16px] leading-[24px] text-[#fff]">
            Learn business fundamentals
          </Text>
          <TouchableOpacity
            onPress={() => {
              openVideoLink(data?.data?.video);
            }}
            className="flex-row items-center gap-[10px] mb-2"
          >
            <View>
              <PlayIcon />
            </View>
            <View>
              <Text className="font-[500] text-[14px] leading-[21px] text-[#E9EAE9]">
                Watch video
              </Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              openVideoLink(data?.data?.article);
            }}
            className="flex-row items-center gap-[10px]"
          >
            <View>
              <TvIcon />
            </View>
            <View>
              <Text className="font-[500] text-[14px] leading-[21px] text-[#E9EAE9]">
                Read Article
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default LearnCardComponent;
