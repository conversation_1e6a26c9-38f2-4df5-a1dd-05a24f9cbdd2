<svg width="62" height="62" viewBox="0 0 62 62" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_251_8612)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M47.7851 13.1155C46.0577 12.6357 44.2632 13.6337 43.7661 15.3706C43.7059 15.5806 43.6049 15.7766 43.4689 15.9475C43.3329 16.1184 43.1646 16.2609 42.9735 16.3668C42.7825 16.4726 42.5724 16.5398 42.3554 16.5645C42.1384 16.5892 41.9186 16.5709 41.7087 16.5107C41.4987 16.4504 41.3027 16.3494 41.1318 16.2134C40.9609 16.0774 40.8184 15.9091 40.7126 15.7181C40.6067 15.527 40.5395 15.317 40.5148 15.0999C40.4901 14.8829 40.5084 14.6632 40.5687 14.4532C41.0456 12.783 42.1625 11.3685 43.6766 10.5171C45.1907 9.66573 46.9796 9.4463 48.6545 9.90649C48.7198 9.92185 48.8503 9.95447 48.9693 9.99478C53.754 11.5897 54.134 18.0825 49.8425 20.4547L40.3038 25.7327C40.1128 25.8384 39.9028 25.9055 39.6858 25.9301C39.4688 25.9546 39.2491 25.9362 39.0393 25.8759C38.8294 25.8156 38.6335 25.7145 38.4627 25.5785C38.2919 25.4424 38.1496 25.2741 38.0439 25.083C37.9381 24.892 37.8711 24.682 37.8465 24.465C37.8219 24.2481 37.8403 24.0284 37.9007 23.8185C37.961 23.6087 38.0621 23.4128 38.1981 23.242C38.3341 23.0712 38.5025 22.9288 38.6935 22.8231L48.2323 17.5451C50.117 16.503 49.8118 13.7911 47.9271 13.1539L47.9156 13.15C47.8722 13.138 47.8287 13.1265 47.7851 13.1155Z" fill="#FFD340"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M47.7851 13.1155C46.0577 12.6357 44.2632 13.6337 43.7661 15.3706C43.7059 15.5806 43.6049 15.7766 43.4689 15.9475C43.3329 16.1184 43.1646 16.2609 42.9735 16.3668C42.7825 16.4726 42.5724 16.5398 42.3554 16.5645C42.1384 16.5892 41.9186 16.5709 41.7087 16.5107C41.4987 16.4504 41.3027 16.3494 41.1318 16.2134C40.9609 16.0774 40.8184 15.9091 40.7126 15.7181C40.6067 15.527 40.5395 15.317 40.5148 15.0999C40.4901 14.8829 40.5084 14.6632 40.5687 14.4532C41.0456 12.783 42.1625 11.3685 43.6766 10.5171C45.1907 9.66573 46.9796 9.4463 48.6545 9.90649C48.7198 9.92185 48.8503 9.95447 48.9693 9.99478C53.754 11.5897 54.134 18.0825 49.8425 20.4547L40.3038 25.7327C40.1128 25.8384 39.9028 25.9055 39.6858 25.9301C39.4688 25.9546 39.2491 25.9362 39.0393 25.8759C38.8294 25.8156 38.6335 25.7145 38.4627 25.5785C38.2919 25.4424 38.1496 25.2741 38.0439 25.083C37.9381 24.892 37.8711 24.682 37.8465 24.465C37.8219 24.2481 37.8403 24.0284 37.9007 23.8185C37.961 23.6087 38.0621 23.4128 38.1981 23.242C38.3341 23.0712 38.5025 22.9288 38.6935 22.8231L48.2323 17.5451C50.117 16.503 49.8118 13.7911 47.9271 13.1539L47.9156 13.15C47.8722 13.138 47.8287 13.1265 47.7851 13.1155Z" fill="url(#paint0_linear_251_8612)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M47.7851 13.1155C46.0577 12.6357 44.2632 13.6337 43.7661 15.3706C43.7059 15.5806 43.6049 15.7766 43.4689 15.9475C43.3329 16.1184 43.1646 16.2609 42.9735 16.3668C42.7825 16.4726 42.5724 16.5398 42.3554 16.5645C42.1384 16.5892 41.9186 16.5709 41.7087 16.5107C41.4987 16.4504 41.3027 16.3494 41.1318 16.2134C40.9609 16.0774 40.8184 15.9091 40.7126 15.7181C40.6067 15.527 40.5395 15.317 40.5148 15.0999C40.4901 14.8829 40.5084 14.6632 40.5687 14.4532C41.0456 12.783 42.1625 11.3685 43.6766 10.5171C45.1907 9.66573 46.9796 9.4463 48.6545 9.90649C48.7198 9.92185 48.8503 9.95447 48.9693 9.99478C53.754 11.5897 54.134 18.0825 49.8425 20.4547L40.3038 25.7327C40.1128 25.8384 39.9028 25.9055 39.6858 25.9301C39.4688 25.9546 39.2491 25.9362 39.0393 25.8759C38.8294 25.8156 38.6335 25.7145 38.4627 25.5785C38.2919 25.4424 38.1496 25.2741 38.0439 25.083C37.9381 24.892 37.8711 24.682 37.8465 24.465C37.8219 24.2481 37.8403 24.0284 37.9007 23.8185C37.961 23.6087 38.0621 23.4128 38.1981 23.242C38.3341 23.0712 38.5025 22.9288 38.6935 22.8231L48.2323 17.5451C50.117 16.503 49.8118 13.7911 47.9271 13.1539L47.9156 13.15C47.8722 13.138 47.8287 13.1265 47.7851 13.1155Z" fill="url(#paint1_radial_251_8612)"/>
</g>
<g filter="url(#filter1_i_251_8612)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.7633 9.9069C14.4388 9.4462 16.2284 9.66561 17.7429 10.5174C19.2575 11.3692 20.3745 12.7845 20.8511 14.4555C20.9725 14.8796 20.9204 15.3344 20.7065 15.7201C20.4925 16.1058 20.1341 16.3906 19.7101 16.512C19.286 16.6334 18.8312 16.5814 18.4455 16.3674C18.0598 16.1535 17.775 15.795 17.6536 15.371C17.5359 14.9558 17.3371 14.568 17.0687 14.2301C16.8003 13.8921 16.4676 13.6107 16.0898 13.4021C15.712 13.1935 15.2966 13.0618 14.8676 13.0147C14.4387 12.9675 14.0046 13.0058 13.5905 13.1274C13.561 13.1342 13.5316 13.1413 13.5022 13.1485L13.4907 13.1524C11.606 13.7896 11.3008 16.5015 13.1855 17.5436L22.7243 22.8216C22.9153 22.9273 23.0836 23.0697 23.2197 23.2404C23.3557 23.4112 23.4568 23.6072 23.5171 23.817C23.5775 24.0269 23.5959 24.2465 23.5713 24.4635C23.5467 24.6805 23.4797 24.8905 23.3739 25.0815C23.2682 25.2726 23.1259 25.4409 22.9551 25.577C22.7843 25.713 22.5884 25.8141 22.3785 25.8744C22.1687 25.9347 21.949 25.9531 21.732 25.9286C21.515 25.904 21.305 25.8369 21.114 25.7312L11.5753 20.4532C7.28573 18.081 7.66574 11.5882 12.4505 9.99327C12.5675 9.95488 12.7 9.92034 12.7633 9.90498V9.9069Z" fill="#EAA73A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.7633 9.9069C14.4388 9.4462 16.2284 9.66561 17.7429 10.5174C19.2575 11.3692 20.3745 12.7845 20.8511 14.4555C20.9725 14.8796 20.9204 15.3344 20.7065 15.7201C20.4925 16.1058 20.1341 16.3906 19.7101 16.512C19.286 16.6334 18.8312 16.5814 18.4455 16.3674C18.0598 16.1535 17.775 15.795 17.6536 15.371C17.5359 14.9558 17.3371 14.568 17.0687 14.2301C16.8003 13.8921 16.4676 13.6107 16.0898 13.4021C15.712 13.1935 15.2966 13.0618 14.8676 13.0147C14.4387 12.9675 14.0046 13.0058 13.5905 13.1274C13.561 13.1342 13.5316 13.1413 13.5022 13.1485L13.4907 13.1524C11.606 13.7896 11.3008 16.5015 13.1855 17.5436L22.7243 22.8216C22.9153 22.9273 23.0836 23.0697 23.2197 23.2404C23.3557 23.4112 23.4568 23.6072 23.5171 23.817C23.5775 24.0269 23.5959 24.2465 23.5713 24.4635C23.5467 24.6805 23.4797 24.8905 23.3739 25.0815C23.2682 25.2726 23.1259 25.4409 22.9551 25.577C22.7843 25.713 22.5884 25.8141 22.3785 25.8744C22.1687 25.9347 21.949 25.9531 21.732 25.9286C21.515 25.904 21.305 25.8369 21.114 25.7312L11.5753 20.4532C7.28573 18.081 7.66574 11.5882 12.4505 9.99327C12.5675 9.95488 12.7 9.92034 12.7633 9.90498V9.9069Z" fill="url(#paint2_linear_251_8612)"/>
</g>
<g filter="url(#filter2_i_251_8612)">
<path d="M33.9164 33.6247V25.5254H27.5061V33.6247C27.5061 35.0449 26.8727 36.4076 25.7787 37.3096L22.0938 40.3805H39.3287L35.6437 37.3096C35.1043 36.8587 34.6702 36.2951 34.3718 35.6585C34.0734 35.0219 33.918 34.3277 33.9164 33.6247Z" fill="url(#paint3_linear_251_8612)"/>
<path d="M33.9164 33.6247V25.5254H27.5061V33.6247C27.5061 35.0449 26.8727 36.4076 25.7787 37.3096L22.0938 40.3805H39.3287L35.6437 37.3096C35.1043 36.8587 34.6702 36.2951 34.3718 35.6585C34.0734 35.0219 33.918 34.3277 33.9164 33.6247Z" fill="url(#paint4_radial_251_8612)"/>
</g>
<path d="M33.9164 33.6247V25.5254H27.5061V33.6247C27.5061 35.0449 26.8727 36.4076 25.7787 37.3096L22.0938 40.3805H39.3287L35.6437 37.3096C35.1043 36.8587 34.6702 36.2951 34.3718 35.6585C34.0734 35.0219 33.918 34.3277 33.9164 33.6247Z" fill="url(#paint5_radial_251_8612)"/>
<path d="M30.7132 31.7635C27.1653 31.7635 23.7628 30.3541 21.254 27.8454C18.7453 25.3366 17.3359 21.9341 17.3359 18.3862V5.2201C17.3359 4.47159 17.9501 3.85742 18.6986 3.85742H42.7469C43.4955 3.85742 44.1096 4.47159 44.1096 5.2201V18.3862C44.1096 25.7754 38.1215 31.7635 30.7132 31.7635Z" fill="#EA873A"/>
<path d="M30.7132 31.7635C27.1653 31.7635 23.7628 30.3541 21.254 27.8454C18.7453 25.3366 17.3359 21.9341 17.3359 18.3862V5.2201C17.3359 4.47159 17.9501 3.85742 18.6986 3.85742H42.7469C43.4955 3.85742 44.1096 4.47159 44.1096 5.2201V18.3862C44.1096 25.7754 38.1215 31.7635 30.7132 31.7635Z" fill="url(#paint6_radial_251_8612)"/>
<path d="M30.7132 31.7635C27.1653 31.7635 23.7628 30.3541 21.254 27.8454C18.7453 25.3366 17.3359 21.9341 17.3359 18.3862V5.2201C17.3359 4.47159 17.9501 3.85742 18.6986 3.85742H42.7469C43.4955 3.85742 44.1096 4.47159 44.1096 5.2201V18.3862C44.1096 25.7754 38.1215 31.7635 30.7132 31.7635Z" fill="url(#paint7_radial_251_8612)"/>
<path d="M30.7132 31.7635C27.1653 31.7635 23.7628 30.3541 21.254 27.8454C18.7453 25.3366 17.3359 21.9341 17.3359 18.3862V5.2201C17.3359 4.47159 17.9501 3.85742 18.6986 3.85742H42.7469C43.4955 3.85742 44.1096 4.47159 44.1096 5.2201V18.3862C44.1096 25.7754 38.1215 31.7635 30.7132 31.7635Z" fill="url(#paint8_radial_251_8612)"/>
<g filter="url(#filter3_i_251_8612)">
<path d="M43.9908 40.3809H17.4282C16.0271 40.3809 14.8372 41.3597 14.5685 42.7224L11.632 56.33C11.4977 56.9633 11.9775 57.5583 12.63 57.5583H48.8081C49.4607 57.5583 49.9405 56.9633 49.8061 56.33L46.8697 42.7224C46.5818 41.3597 45.3918 40.3809 43.9908 40.3809Z" fill="url(#paint9_linear_251_8612)"/>
<path d="M43.9908 40.3809H17.4282C16.0271 40.3809 14.8372 41.3597 14.5685 42.7224L11.632 56.33C11.4977 56.9633 11.9775 57.5583 12.63 57.5583H48.8081C49.4607 57.5583 49.9405 56.9633 49.8061 56.33L46.8697 42.7224C46.5818 41.3597 45.3918 40.3809 43.9908 40.3809Z" fill="url(#paint10_linear_251_8612)"/>
<path d="M43.9908 40.3809H17.4282C16.0271 40.3809 14.8372 41.3597 14.5685 42.7224L11.632 56.33C11.4977 56.9633 11.9775 57.5583 12.63 57.5583H48.8081C49.4607 57.5583 49.9405 56.9633 49.8061 56.33L46.8697 42.7224C46.5818 41.3597 45.3918 40.3809 43.9908 40.3809Z" fill="url(#paint11_linear_251_8612)"/>
</g>
<g filter="url(#filter4_f_251_8612)">
<path d="M35.9776 45.8389H24.4236C23.7519 45.8389 23.1953 46.3763 23.1953 47.0672V50.5411C23.1953 51.2128 23.7327 51.7694 24.4236 51.7694H35.9776C36.6493 51.7694 37.2059 51.232 37.2059 50.5411V47.0672C37.2059 46.3955 36.6685 45.8389 35.9776 45.8389Z" fill="#914556"/>
</g>
<g filter="url(#filter5_i_251_8612)">
<path d="M36.4854 45.4861H24.9315C24.2597 45.4861 23.7031 46.0235 23.7031 46.7145V50.1883C23.7031 50.8601 24.2405 51.4167 24.9315 51.4167H36.4854C37.1571 51.4167 37.7137 50.8793 37.7137 50.1883V46.7145C37.7137 46.0427 37.1763 45.4861 36.4854 45.4861Z" fill="url(#paint12_linear_251_8612)"/>
</g>
<g filter="url(#filter6_i_251_8612)">
<path d="M29.4109 10.9922H32.1996V24.9357H29.0125V14.3585L26.5425 15.0358L25.7656 12.3069L29.4109 10.9922Z" fill="#EE9C45"/>
</g>
<defs>
<filter id="filter0_i_251_8612" x="37.8359" y="9.4834" width="14.9883" height="16.4572" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.1875"/>
<feGaussianBlur stdDeviation="0.1875"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.937255 0 0 0 0 0.576471 0 0 0 0 0.247059 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_251_8612"/>
</filter>
<filter id="filter1_i_251_8612" x="8.59375" y="9.48346" width="14.9883" height="16.4557" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.1875"/>
<feGaussianBlur stdDeviation="0.1875"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.854902 0 0 0 0 0.458824 0 0 0 0 0.286275 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_251_8612"/>
</filter>
<filter id="filter2_i_251_8612" x="22.0938" y="25.5254" width="17.6094" height="15.0426" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.375" dy="0.1875"/>
<feGaussianBlur stdDeviation="0.1875"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.843137 0 0 0 0 0.462745 0 0 0 0 0.215686 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_251_8612"/>
</filter>
<filter id="filter3_i_251_8612" x="11.6094" y="40.3809" width="38.7062" height="17.6649" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.4875" dy="0.4875"/>
<feGaussianBlur stdDeviation="0.28125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.509804 0 0 0 0 0.270588 0 0 0 0 0.270588 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_251_8612"/>
</filter>
<filter id="filter4_f_251_8612" x="22.8953" y="45.5389" width="14.6117" height="6.53054" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.15" result="effect1_foregroundBlur_251_8612"/>
</filter>
<filter id="filter5_i_251_8612" x="23.7031" y="45.2986" width="14.1992" height="6.11804" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.1875" dy="-0.1875"/>
<feGaussianBlur stdDeviation="0.15"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.941176 0 0 0 0 0.533333 0 0 0 0 0.333333 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_251_8612"/>
</filter>
<filter id="filter6_i_251_8612" x="25.7656" y="10.9922" width="6.43359" height="13.9435" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.295833 0 0 0 0 0.124384 0 0 0 0 0.110937 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_251_8612"/>
</filter>
<linearGradient id="paint0_linear_251_8612" x1="43.3343" y1="22.9229" x2="50.3051" y2="16.6354" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEC551"/>
<stop offset="1" stop-color="#FEC551" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint1_radial_251_8612" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(43.4111 17.0903) rotate(-21.5519) scale(9.69317 8.9289)">
<stop offset="0.727" stop-color="#FFEC6A" stop-opacity="0"/>
<stop offset="1" stop-color="#FFEC6A"/>
</radialGradient>
<linearGradient id="paint2_linear_251_8612" x1="19.0162" y1="15.2712" x2="14.243" y2="13.9086" gradientUnits="userSpaceOnUse">
<stop stop-color="#D27840"/>
<stop offset="1" stop-color="#D27840" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_251_8612" x1="33.1967" y1="37.9238" x2="23.4814" y2="37.9238" gradientUnits="userSpaceOnUse">
<stop stop-color="#FBC33A"/>
<stop offset="1" stop-color="#DB7B3C"/>
</linearGradient>
<radialGradient id="paint4_radial_251_8612" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(33.7379 32.9529) rotate(90) scale(4.01125 3.65859)">
<stop offset="0.193" stop-color="#FFE469"/>
<stop offset="1" stop-color="#FFE469" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint5_radial_251_8612" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(37.8796 39.0588) rotate(149.162) scale(4.36618 6.62922)">
<stop stop-color="#FFD34D"/>
<stop offset="1" stop-color="#FFD34D" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint6_radial_251_8612" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(35.5977 6.01659) rotate(83.702) scale(23.8443 15.311)">
<stop offset="0.121" stop-color="#FFFA73"/>
<stop offset="1" stop-color="#FFFA73" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint7_radial_251_8612" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(35.7532 3.85742) rotate(90) scale(1.35181 15.1075)">
<stop stop-color="#FFF45B"/>
<stop offset="1" stop-color="#FFF45B" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint8_radial_251_8612" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(37.1581 9.13539) rotate(121.572) scale(25.202 24.1794)">
<stop offset="0.787" stop-color="#C55D73" stop-opacity="0"/>
<stop offset="1" stop-color="#C55D73"/>
</radialGradient>
<linearGradient id="paint9_linear_251_8612" x1="11.609" y1="48.9638" x2="49.8215" y2="48.9638" gradientUnits="userSpaceOnUse">
<stop stop-color="#9D5B6C"/>
<stop offset="1" stop-color="#955569"/>
</linearGradient>
<linearGradient id="paint10_linear_251_8612" x1="30.7191" y1="58.3049" x2="30.7191" y2="55.1842" gradientUnits="userSpaceOnUse">
<stop stop-color="#8C3A79"/>
<stop offset="1" stop-color="#8C3A79" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint11_linear_251_8612" x1="48.3897" y1="50.9215" x2="46.5894" y2="51.3418" gradientUnits="userSpaceOnUse">
<stop stop-color="#B0817C"/>
<stop offset="1" stop-color="#B0817C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint12_linear_251_8612" x1="38.4315" y1="48.4514" x2="23.7031" y2="48.4514" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE767"/>
<stop offset="1" stop-color="#FEBB5B"/>
</linearGradient>
</defs>
</svg>
