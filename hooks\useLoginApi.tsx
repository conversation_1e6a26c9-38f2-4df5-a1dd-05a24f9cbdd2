import AsyncStorage from "@react-native-async-storage/async-storage";
import Constants from "expo-constants";
import axios from "axios";
import { useState } from "react";

const useLoginApi = ({ endpoint }) => {
  const [data, setdata] = useState(null);
  const [isLoading, setLoading] = useState(false);
  const SetFunction = async (datas) => {
    let ResponseData;
    setLoading(true);
    await axios
      .post(
        `${Constants.expoConfig?.extra?.BASE_URL}${endpoint}`,
        { ...datas },
        {
          headers: {
            "X-Powered-By": "Express",
            "Access-Control-Allow-Origin": "*",
            "Content-Type": "application/json",
          },
        }
      )
      .then((response) => {
        console.log(response.data);
        ResponseData = response;
        setdata(response);
        return response.data;
      })
      .then((data) => {
        setLoading(false);
        console.log(data);
        return data;
      })
      .catch((error) => {
        console.error(error.response ? error.response.data : error.message);
      });
    return ResponseData;
  };

  return { isLoading, data, SetFunction };
};

export default useLoginApi;
