import { View, Text } from "react-native";
import React, { useEffect, useState } from "react";
import { Dropdown } from "react-native-element-dropdown";
import { Controller } from "react-hook-form";

const DropDownComponent = ({
  text,
  placeholder,
  style,
  data,
  defaul,
  control,
  name,
  rules,
  setvaluefun,
}) => {
  return (
    <View className="mt-4">
      <Text className="font-[400] text-[16px] text-[#4D4D4D]">{text}</Text>
      <Controller
        name={name}
        control={control}
        rules={{ ...rules }}
        defaultValue={defaul}
        render={({ field: { onChange, value } }) => {
          return (
            <>
              <Dropdown
                data={data}
                placeholder={placeholder}
                placeholderStyle={{
                  color: "#B3B3B3",
                  height: 35,
                  textAlignVertical: "center",
                }}
                style={{
                  minHeight: 40,
                }}
                labelField="name"
                valueField="name"
                value={value}
                onChange={(item) => {
                  onChange(item.name);
                  setvaluefun(item.name);
                }}
                className="border-[1px] border-[#ACB9D5] rounded-[4px] px-3 mt-2"
              />
            </>
          );
        }}
      />
    </View>
  );
};

export default DropDownComponent;
