import * as ImagePicker from "expo-image-picker";
import AsyncStorage from "@react-native-async-storage/async-storage";
import BackIcon from "../../../../../assets/OrderDetails/backIcon.svg";
import CalendarIcon from "../../../../../assets/ProfileAsser/SectionIcon/UserprofileAssert/calander.svg";
import CamaraIcon from "../../../../../assets/icons/bottemSheetIcon/camara.svg";
import Close from "../../../../../assets/icons/bottemSheetIcon/Close.svg";
import Close2 from "../../../../../assets/icons/bottemSheetIcon/Close2.svg";
import Constants from "expo-constants";
import DateTimePicker from "@react-native-community/datetimepicker";
import DeleteIcon from "../../../../../assets/icons/bottemSheetIcon/delete.svg";
import DropDownComponent from "../../../../../component/DropDownComponent";
import EditIcon from "../../../../../assets/ProfileAsser/SectionIcon/UserprofileAssert/Group 1321316369.svg";
import EyeIcon from "../../../../../assets/icons/bottemSheetIcon/Eye.svg";
import GalleryIcon from "../../../../../assets/icons/bottemSheetIcon/gallery.svg";
import RBSheet from "react-native-raw-bottom-sheet";
import React, { useEffect, useRef, useState } from "react";
import TextinputWithEdit from "../../../../../component/TextinputWithEdit";
import UserIcon from "../../../../../assets/ProfileAsser/Profile.svg";
import useCallApi from "../../../../../hooks/useCallApi";
import useGetDataApi from "../../../../../hooks/useGetDataApi";
import useSetApiData from "../../../../../hooks/useSetApiData";
import useUpdateWithFormData from "../../../../../hooks/useUpdateWithFormData";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import { Controller, useForm } from "react-hook-form";
import { SafeAreaView } from "react-native-safe-area-context";
import { useConformNumberEmail } from "../../../../../store";

import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Image,
  Modal,
} from "react-native";
import Svg, {
  G,
  Path,
  Circle,
  TSpan,
  Rect,
  Defs,
  Pattern,
  Use,
  ClipPath,
  Image as SvgImage,
  Text as SvgText,
} from "react-native-svg";

const index = () => {
  const { control, setValue, watch, handleSubmit } = useForm();
  const refRBSheet = useRef();
  const [showprofile, setshowprofile] = useState(false);
  const [image, setImage] = useState([]);
  const [showImg, setshowImg] = useState(true);
  const formData = new FormData();
  const { data, isLoading, triggerreFresh } = useGetDataApi("getProfileImage");
  const {
    data: ProfileData,
    isLoading: ProfileLoading,
    triggerreFresh: ProfileRefresh,
  } = useGetDataApi("getProfile");

  const { UpdateBankDetails: UpdateProfileImage } = useUpdateWithFormData({
    endpoint: "updateProfileImage",
  });

  const { CallFunction } = useCallApi();

  const SetFunctions = async (datas) => {
    const token = await AsyncStorage.getItem("login");
    await fetch(
      `${Constants.expoConfig?.extra?.BASE_URL}auth/updateProfileImage`,
      {
        method: "POST",
        headers: {
          "X-Powered-By": "Express",
          "Access-Control-Allow-Origin": "*",
          "content-type": "multipart/form-data",
          Authorization: token,
        },
        body: datas,
      }
    ).then((val) => {});
  };

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      await formData.append("image", {
        uri: result.assets[0].uri,
        name: result.assets[0].uri,
        filename: result.assets[0].uri,
        type: result.assets[0].mimeType,
        size: 100000000,
      });
      UpdateProfileImage(formData).then(() => {
        triggerreFresh();
      });
    }
  };

  const [name, setname] = useState("");
  const [number, setnumber] = useState("");
  const [birth, setbirth] = useState("06/03/1992");
  const [gender, setgender] = useState("");

  const [date, setdate] = useState();
  const [showpicker, setshowpicker] = useState(false);
  const [datevale, setdatevale] = useState("");

  const [JoinDate, setJoinDate] = useState();
  const [showpickerJoinDate, setshowpickerJoinDate] = useState(false);
  const [dateJoinDate, setdateJoinDate] = useState("");
  const toggledatepicker = () => {
    setshowpicker(!showpicker);
  };
  const toggledatepickerJoinDate = () => {
    setshowpickerJoinDate(!showpicker);
  };
  const change = ({ type }, selectedDate) => {
    if (type == "set") {
      const currentdate = selectedDate;
      const currentdatestringt = DataFormat(new Date(selectedDate));
      setdate(currentdate);
      setdatevale(currentdatestringt);
      setshowpicker(false);
    } else {
      setshowpicker(false);
    }
  };
  const JoinDateChange = ({ type }, selectedDate) => {
    if (type == "set") {
      const currentdate = selectedDate;
      const currentdatestringt = DataFormat(new Date(selectedDate));
      setJoinDate(currentdate);
      setdateJoinDate(currentdatestringt);
      setshowpickerJoinDate(false);
    } else {
      setshowpickerJoinDate(false);
    }
  };

  const DataFormat = (date) => {
    let day = String(date.getDate()).padStart(2, "0");
    let month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero based
    let year = date.getFullYear();

    return `${day}/${month}/${year}`;
  };
  const { SetFunction: UpdateProfile } = useSetApiData({
    endpoint: "auth/updateProfile",
  });

  const { phone, email, setPhone, setEmail } = useConformNumberEmail(
    (state) => state
  );

  const UpdateProfileData = async (data) => {
    UpdateProfile({
      name: data.name,
      email: email,
      phone: phone,
      dob: date,
      date_of_joinning: JoinDate,
      gender: data.gender,
    }).then(() => {
      ProfileRefresh();
    });
  };

  useEffect(() => {
    if (!ProfileLoading) {
      setValue("name", ProfileData?.data?.name);
      setValue("number", ProfileData?.data?.phone);
      setValue("email", ProfileData?.data?.email);
      setValue("gender", ProfileData?.data?.gender);
      const dob = DataFormat(
        new Date(ProfileData?.data?.dob ? ProfileData?.data?.dob : Date())
      );
      setdatevale(dob);

      setdate(
        new Date(
          ProfileData?.data?.date_of_joinning
            ? ProfileData?.data?.date_of_joinning
            : Date()
        )
      );
      const ApiJoinDate = DataFormat(
        new Date(
          ProfileData?.data?.date_of_joinning
            ? ProfileData?.data?.date_of_joinning
            : Date()
        )
      );
      setdateJoinDate(ApiJoinDate);
      setJoinDate(
        new Date(
          ProfileData?.data?.date_of_joinning
            ? ProfileData?.data?.date_of_joinning
            : Date()
        )
      );
    }
  }, [ProfileLoading]);

  return (
    <SafeAreaView className="flex-1">
      <ScrollView>
        <View className="items-center justify-center relative mt-6">
          <TouchableOpacity
            className="absolute left-[3%]"
            // onPress={() => {
            //   if (
            //     watch("name") !== name ||
            //     watch("number") !== number ||
            //     watch("gender") !== gender
            //   ) {
            //     Alert.alert("Save Changes", "Changes Happend Plaese Save", [
            //       {
            //         text: "Save",
            //         onPress: () => {
            //           router.back();
            //         },
            //       },
            //       {
            //         text: "DisCard",
            //         onPress: () => {
            //           router.back();
            //         },
            //       },
            //     ]);
            //   } else {
            //     router.back();
            //   }
            // }}
            onPress={() => {
              router.back();
            }}
          >
            <BackIcon />
          </TouchableOpacity>
          <View>
            <Text className="font-[600] text-[20px] leading-[30px]">
              Your Profile
            </Text>
          </View>
        </View>
        <View className="mt-8 items-center justify-center mb-6">
          <View className="relative">
            {data?.image ? (
              <>
                <Image
                  source={{
                    uri: data?.image,
                  }}
                  className="h-[120px] w-[120px] rounded-full"
                />
              </>
            ) : (
              <>
                {ProfileLoading ? (
                  <></>
                ) : (
                  <>
                    <EditableSVG
                      text={
                        ProfileData?.data?.name
                          ? ProfileData?.data?.name.split("")[0]
                          : ""
                      }
                      height={120}
                      width={120}
                    />
                  </>
                )}
              </>
            )}
            <TouchableOpacity
              onPress={() => {
                refRBSheet.current.open();
              }}
              className="absolute bottom-0 left-0"
            >
              <EditIcon />
            </TouchableOpacity>
          </View>
        </View>
        <View className="mt-4">
          <TextFieldComponent
            text={"Name"}
            defaultval={ProfileLoading ? "" : ProfileData?.data?.name}
            control={control}
            name={"name"}
          />
        </View>
        {ProfileLoading ? (
          <></>
        ) : (
          <>
            <View key={1} className="px-4">
              <TextinputWithEdit
                text={"Email ID"}
                placeholder={ProfileData?.data?.email}
                ApiData={ProfileData?.data}
                watch={watch}
                control={control}
                name={"email"}
                IsVerified={ProfileData?.data?.is_email_verified}
                changes={ProfileData?.data?.email !== watch("email")}
              />
            </View>
          </>
        )}
        {ProfileLoading ? (
          <></>
        ) : (
          <>
            <View className="mt-4 px-4">
              <TextinputWithEdit
                text={"Phone Number"}
                placeholder={ProfileData?.data?.phone}
                ApiData={ProfileData?.data}
                watch={watch}
                control={control}
                name={"number"}
                IsVerified={ProfileData?.data?.is_phone_verified}
                changes={ProfileData?.data?.phone !== watch("number")}
              />
            </View>
          </>
        )}

        {/* Date of Birth Textfield Area */}

        <View className="mt-4 px-4">
          <Text>Date of Birth</Text>
          {showpicker && (
            <DateTimePicker
              maximumDate={new Date()}
              value={date}
              mode="date"
              onChange={change}
            />
          )}
          <TouchableOpacity
            onPress={() => {
              toggledatepicker();
            }}
          >
            <View className="rounded-[4px] px-2 flex-row justify-center items-center mt-2 border-[1px] h-[40px] border-[#ACB9D5]">
              <TextInput
                className="flex-1 text-[#000] font-Pop"
                editable={false}
                value={datevale}
              />
              <CalendarIcon />
            </View>
          </TouchableOpacity>
        </View>

        {/* Date of Birth Textfield Area */}

        <View className="mt-4 px-4">
          <Text>Joining Date</Text>
          {showpickerJoinDate && (
            <DateTimePicker
              maximumDate={new Date()}
              value={JoinDate}
              mode="date"
              onChange={JoinDateChange}
            />
          )}
          <TouchableOpacity
            onPress={() => {
              toggledatepickerJoinDate();
            }}
          >
            <View className="rounded-[4px] px-2 flex-row justify-center items-center mt-2 border-[1px] h-[40px] border-[#ACB9D5]">
              <TextInput
                className="flex-1 text-[#000] font-Pop"
                editable={false}
                value={dateJoinDate}
              />
              <CalendarIcon />
            </View>
          </TouchableOpacity>
        </View>
        <View>
          <View className="px-4">
            <DropDownComponent
              control={control}
              setvaluefun={setValue}
              name={"gender"}
              data={[
                { label: "Male", value: "1" },
                { label: "Female", value: "2" },
              ]}
              text={"Gender"}
              placeholder={"Select type of request"}
            />
          </View>
        </View>
      </ScrollView>
      <View className="px-4 mb-4">
        <TouchableOpacity
          onPress={() => {
            handleSubmit(UpdateProfileData)();
          }}
          className="mt-4 h-[44px] items-center justify-center bg-[#00660A]"
        >
          <Text className="font-[400] leading-[24px] text-[#fff]">
            Update Profile
          </Text>
        </TouchableOpacity>
      </View>

      {ProfileLoading ? (
        <></>
      ) : (
        <>
          {isLoading ? (
            <></>
          ) : (
            <>
              <View className="">
                <ShowUserProfile
                  showprofile={showprofile}
                  setfun={setshowprofile}
                  img={data?.image ? { uri: data?.image } : undefined}
                  LogoFirstLetter={ProfileData?.data?.name.split("")[0]}
                />
              </View>
            </>
          )}
        </>
      )}

      <RBSheet
        ref={refRBSheet}
        customStyles={{
          container: {
            borderRadius: 20,
            height: "35%",
          },
          draggableIcon: {
            backgroundColor: "#000",
          },
        }}
        customModalProps={{
          statusBarTranslucent: true,
        }}
        customAvoidingViewProps={{
          enabled: false,
        }}
      >
        <View className="px-6 py-6 bg-[#EDEEED]">
          <View className="items-end">
            <TouchableOpacity
              onPress={() => {
                refRBSheet.current.close();
              }}
            >
              <Close />
            </TouchableOpacity>
          </View>

          <View className="bg-[#FFFFFF] mt-3 px-5">
            <TouchableOpacity
              onPress={() => {
                setshowprofile(true);
              }}
              className="flex-row items-center justify-between h-[56px]"
            >
              <Text className="font-[400] text-[16px] leading-[20px] text-[#627164]">
                View Profile Picture
              </Text>
              <EyeIcon />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                pickImage();
              }}
              className="flex-row items-center justify-between h-[56px]"
            >
              <Text className="font-[400] text-[16px] leading-[20px] text-[#627164]">
                Upload from Gallery
              </Text>
              <GalleryIcon />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                router.push("home/camara");
              }}
              className="flex-row items-center justify-between h-[56px]"
            >
              <Text className="font-[400] text-[16px] leading-[20px] text-[#627164]">
                Take Photo
              </Text>
              <CamaraIcon />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                CallFunction("auth/deleteProfileImage");
                triggerreFresh();
              }}
              className="flex-row items-center justify-between h-[56px]"
            >
              <Text className="font-[400] text-[16px] leading-[20px] text-[#627164]">
                Delete Photo
              </Text>
              <DeleteIcon />
            </TouchableOpacity>
          </View>
        </View>
      </RBSheet>
    </SafeAreaView>
  );
};

const TextFieldComponent = ({ text, defaultval, name, control }) => {
  return (
    <>
      <View className="px-4">
        <Text>{text}</Text>
        <Controller
          name={name}
          control={control}
          defaultValue={defaultval}
          render={({ field: { value, onChange } }) => {
            return (
              <TextInput
                value={value}
                onChangeText={(text) => {
                  onChange(text);
                }}
                className="h-[40px] border-[1px] border-[#ACB9D5] rounded-[4px] pl-2 mt-2 text-[#000]"
              />
            );
          }}
        />
      </View>
    </>
  );
};

export const ShowUserProfile = ({
  showprofile,
  setfun,
  img,
  LogoFirstLetter,
}) => {
  return (
    <>
      <Modal transparent={true} visible={showprofile}>
        <View className="flex-1">
          <LinearGradient
            colors={["#000", "#000"]}
            start={[0, 0]}
            style={{
              position: "absolute",
              left: 0,
              right: 0,
              top: 0,
              bottom: 0,
              zIndex: 1,
              flex: -1,
              opacity: 0.8,
            }}
          />
          <View className="flex-1 items-center z-30 justify-center ">
            {!img ? (
              <View className="flex-1 items-center justify-center">
                {LogoFirstLetter && LogoFirstLetter.length === 1 ? (
                  <>
                    <EditableSVG
                      text={LogoFirstLetter}
                      height={300}
                      width={300}
                    />
                  </>
                ) : (
                  <>
                    {LogoFirstLetter && LogoFirstLetter.length > 1 ? (
                      <>
                        <EditableSVGFullNameForShowProfile
                          text={LogoFirstLetter}
                          height={300}
                          width={500}
                        />
                      </>
                    ) : (
                      <></>
                    )}
                  </>
                )}
              </View>
            ) : (
              <>
                <Image
                  source={img}
                  className="w-[100%] h-[100%]"
                  style={{ objectFit: "contain" }}
                />
              </>
            )}
            <TouchableOpacity
              onPress={() => {
                setfun(false);
              }}
              className="absolute z-50 top-14 right-8"
            >
              <Close2 width={26} height={26} fill={"#fff"} />
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </>
  );
};

export const EditableSVG = ({ text, width, height }) => {
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 60 60"
      fill="none"
      justifyContent="center"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Circle cx="30" cy="30" r="30" fill="#00660A" />
      <SvgText
        x={31}
        y={45}
        textAnchor="middle"
        fill={"#FFFFFF"}
        fontSize={42}
        fontFamily="Arial"
      >
        {text}
      </SvgText>
    </Svg>
  );
};
export const EditableSVGFullName = ({ text, width, height }) => {
  return (
    <Svg
      width={"100%"}
      height={height}
      viewBox={`0 0 100% ${height}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Rect x="0" y="0" width={"100%"} height={height} fill="#FFFFFF" />
      <SvgText
        x="50%"
        y="50%"
        textAnchor="middle"
        fill="#00660A"
        fontSize={80}
        fontFamily="Arial"
        alignmentBaseline="middle" // Align text to middle of the y coordinate
      >
        {text}
      </SvgText>
    </Svg>
  );
};
export const EditableSVGFullNameForShowProfile = ({ text, width, height }) => {
  return (
    <Svg
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Rect x="0" y="0" width={width} height={height} fill={"#FFFFFF"} />
      <SvgText
        x="50%"
        y="50%"
        textAnchor="middle"
        fill="#00660A"
        fontSize={80}
        fontFamily="Arial"
        alignmentBaseline="middle" // Align text to middle of the y coordinate
      >
        {text}
      </SvgText>
    </Svg>
  );
};

export default index;
