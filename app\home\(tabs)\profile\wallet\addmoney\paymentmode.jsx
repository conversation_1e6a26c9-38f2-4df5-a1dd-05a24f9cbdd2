import {
  View,
  Text,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  Image,
  FlatList,
} from "react-native";
import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { router, useLocalSearchParams } from "expo-router";
import BackArrow from "@/assets/icons/BackArrow.svg";
import Rsicon from "@/assets/Hometab/rs.svg";
import Gpayimg from "@/assets/PaymentImg/Gpay.svg";
import PhonePay from "@/assets/PaymentImg/Phone.svg";
import Paytmimg from "@/assets/PaymentImg/paytm.svg";
import DownArrow from "@/assets/PaymentImg/Downarrow.svg";
import WelletIcon from "@/assets/PaymentImg/walletsvg.svg";
import NetBankIcon from "@/assets/PaymentImg/netbanksvg.svg";
import useSetApiData from "../../../../../../hooks/useSetApiData";
import { Payment } from "../../../../../../utils/Wallet";

const paymentmode = () => {
  const { amount } = useLocalSearchParams();
  const Icons = [
    { icons: <Gpayimg />, name: "Gpay" },
    { icons: <PhonePay />, name: "Ponepay" },
    { icons: <Paytmimg />, name: "paytm" },
  ];
  const [acvtive, setacvtive] = useState("");
  const { SetFunction } = useSetApiData({
    endpoint: "deposit_money_to_wallet",
  });

  return (
    <SafeAreaView className="flex-1">
      <View className="px-4 flex-1 justify-between bg-[#FFFFFF]">
        <View>
          {/* Title Area  */}
          <View className="justify-between mt-3">
            <View className="flex-row relative items-center justify-start mt-4">
              <TouchableOpacity
                onPress={() => {
                  router.back();
                }}
                className="absolute"
              >
                <View>
                  <BackArrow />
                </View>
              </TouchableOpacity>
              <View className="flex-1 items-center justify-center">
                <Text className="font-[400] text-[26px] leading-[39px]">
                  Choose mode of payment
                </Text>
              </View>
            </View>
          </View>
          {/* Amount Area */}
          <View className="flex-row items-center justify-between p-6 mt-8 border-[1px] border-[#E7EFFB] rounded-[4px]">
            <View className="">
              <Text className="font-[500] text-[#000] leading-[27px] text-[18px]">
                Amount Payable
              </Text>
              <Text className="font-[400] text-[#81878C] text-[14px] leading-[21px]">
                *including taxes
              </Text>
            </View>
            <View className="flex-row items-center">
              <Rsicon />
              <View>
                <Text className="font-[600] text-[20px] leading-[30px] text-[#627164]">
                  {amount}
                </Text>
              </View>
            </View>
          </View>

          {/* Payment Area */}
          {/* <View className="mt-4 border-[1px] border-[#E7EFFB] rounded-[4px] p-2">
            <View className="">
              <Text className="font-[400] text-[16px] leading-[20px] text-[#4D4D4D]">
                Select Payment Amount
              </Text>
            </View>
            <View className="p-4">
              <View>
                <Text className="font-[500] text-[12px] leading-[14px]">
                  UPI
                </Text>
              </View>
              <View className="flex-row items-center mt-4 space-x-3">
                <FlatList
                  data={Icons}
                  horizontal
                  contentContainerStyle={{ gap: 10 }}
                  renderItem={({ item: { icons, name } }) => {
                    return (
                      <TouchableOpacity
                        onPress={() => {
                          setacvtive(name);
                        }}
                        className="rounded-full h-[50px] w-[50px] items-center justify-center border-[#1F7AE0]"
                        style={{
                          borderWidth: name === acvtive ? 1 : 0,
                        }}
                      >
                        {icons}
                      </TouchableOpacity>
                    );
                  }}
                />
              </View>
            </View>
          </View> */}

          {/* Banks Area */}
          {/* <View className="mt-4 border-[1px] border-[#E7EFFB] rounded-[4px]">
            <View className="flex-row items-center justify-between p-5 pt-[20px]">
              <View className="flex-row items-center space-x-2">
                <WelletIcon />
                <View>
                  <Text className="font-[500] text-[12px] text-[#151E28]">
                    Credit, Debit & ATM Cards
                  </Text>
                </View>
              </View>
              <DownArrow />
            </View>
            <View
              style={{
                borderBottomColor: "#1F7AE0",
                borderBottomWidth: 1,
                opacity: 0.2,
              }}
            />
            <View className="flex-row items-center justify-between p-5">
              <View className="flex-row items-center space-x-2">
                <NetBankIcon />
                <View>
                  <Text className="font-[500] text-[12px] text-[#151E28]">
                    Net Banking
                  </Text>
                </View>
              </View>
              <DownArrow />
            </View>
          </View> */}
        </View>
        <View className="mb-10 items-center justify-center">
          <TouchableOpacity
            onPress={() => {
              Payment(amount).then((val) => {
                if (val) {
                  SetFunction({
                    amount: amount,
                  }).then((val) => {
                    router.navigate("home/profile/wallet/addmoney/paysuccess");
                  });
                }
              });
            }}
            className="h-[44px] w-[224px] items-center justify-center bg-[#00660A]"
          >
            <Text className="font-[500] text-[16px] leading-[24px] text-[#FFFFFF]">
              Pay Now
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default paymentmode;
